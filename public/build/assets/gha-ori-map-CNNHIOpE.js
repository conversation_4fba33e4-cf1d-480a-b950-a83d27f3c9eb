(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1141],{44968:function(Ve,ce,i){i.d(ce,{j:function(){return L}});var m=i(67294),w={sm:"640px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px"},L=function(){var R=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"sm",H=(0,m.useState)(!1),l=H[0],ne=H[1];return(0,m.useEffect)(function(){var M=w[R],O=window.matchMedia("(min-width: ".concat(M,")"));ne(O.matches);var oe=function(Z){ne(Z.matches)};return O.addEventListener("change",oe),function(){O.removeEventListener("change",oe)}},[R]),l}},71265:function(Ve,ce,i){i.d(ce,{Z:function(){return ot}});var m,w,L,R=i(29815),H=i(85893),l=i(67294),ne=i(6158),M=i.n(ne),O=i(73935);i(81634);var oe=i(84381),Z=i.n(oe),Ue=i(79466),_e=i(60461),Q=i(81043),qe=i(29806),Oe=i(59007),Me=i(60023),Qe=i(78650),Ye=function(){if(m)return m.cloneNode(!0);(m=document.createElementNS("http://www.w3.org/2000/svg","svg")).setAttribute("viewBox","0 0 70 70"),m.setAttribute("fill","none"),m.setAttribute("width","32"),m.setAttribute("height","32");var o=document.createElementNS("http://www.w3.org/2000/svg","path");return o.setAttribute("fill-rule","evenodd"),o.setAttribute("clip-rule","evenodd"),o.setAttribute("fill","white"),o.setAttribute("d","M18.885 20.6993C20.9057 18.6482 23.6148 17.5 26.4924 17.5C29.3717 17.5 32.0798 18.6483 34.0993 20.7002L35.2917 21.9123L36.485 20.6993C38.5055 18.6484 41.213 17.5 44.0925 17.5C46.9703 17.5 49.6799 18.6484 51.7008 20.7017C55.8791 24.9489 55.8795 31.8379 51.7012 36.085L37.1638 50.8639C36.6704 51.3656 35.9961 51.6481 35.2924 51.6481C34.5887 51.6481 33.9144 51.3655 33.421 50.8638L18.8835 36.0833C14.7048 31.837 14.7063 24.9489 18.8833 20.701L18.885 20.6993Z"),m.appendChild(o),m.cloneNode(!0)},Ge=function(){if(w)return w.cloneNode(!0);(w=document.createElementNS("http://www.w3.org/2000/svg","svg")).setAttribute("viewBox","0 0 70 70"),w.setAttribute("fill","none"),w.setAttribute("width","32"),w.setAttribute("height","32");var o=document.createElementNS("http://www.w3.org/2000/svg","path");return o.setAttribute("fill-rule","evenodd"),o.setAttribute("clip-rule","evenodd"),o.setAttribute("fill","white"),o.setAttribute("d","M18.885 20.6993C20.9057 18.6482 23.6148 17.5 26.4924 17.5C29.3717 17.5 32.0798 18.6483 34.0993 20.7002L35.2917 21.9123L36.485 20.6993C38.5055 18.6484 41.213 17.5 44.0925 17.5C46.9703 17.5 49.6799 18.6484 51.7008 20.7017C55.8791 24.9489 55.8795 31.8379 51.7012 36.085L37.1638 50.8639C36.6704 51.3656 35.9961 51.6481 35.2924 51.6481C34.5887 51.6481 33.9144 51.3655 33.421 50.8638L18.8841 36.0839C18.8839 36.0837 18.8837 36.0835 18.8835 36.0833C14.7048 31.837 14.7063 24.9489 18.8833 20.701L18.885 20.6993ZM22.6259 24.3828C20.4573 26.589 20.4588 30.1995 22.6258 32.4012L35.2926 45.2797L47.9586 32.4033C50.1269 30.1992 50.127 26.588 47.9587 24.384C46.9125 23.3212 45.546 22.75 44.0925 22.75C42.6375 22.75 41.272 23.3211 40.2254 24.3833C40.2255 24.3832 40.2253 24.3835 40.2254 24.3833L37.7113 26.9389C36.3938 28.2775 34.1901 28.278 32.8725 26.9394L30.3575 24.3828C30.3576 24.3829 30.3574 24.3827 30.3575 24.3828C29.3129 23.3217 27.9476 22.75 26.4924 22.75C25.039 22.75 23.6723 23.3211 22.6259 24.3828Z"),w.appendChild(o),w.cloneNode(!0)},Ae=function(o){return o?Ye():Ge()},Xe=function(){if(L)return L.cloneNode(!0);(L=document.createElementNS("http://www.w3.org/2000/svg","svg")).setAttribute("viewBox","0 0 50 50"),L.setAttribute("fill","none"),L.setAttribute("width","30"),L.setAttribute("height","30");var o=document.createElementNS("http://www.w3.org/2000/svg","path");return L.appendChild(o),o.setAttribute("fillRule","evenodd"),o.setAttribute("clipRule","evenodd"),o.setAttribute("fill","white"),o.setAttribute("d","M24.6 13.9h-3C28 13.9 33 18.9 33 25s-5 11.1-11.2 11.1h2.9c6.1 0 11.1-5 11.1-11.1s-5-11.1-11.1-11.1ZM15.3 36H18V14h-2.8V36Z"),L.cloneNode(!0)},Ke=i(29090),$e=i(1959),et=i(67262),tt=i(53547),re=i(71626);M().accessToken="pk.eyJ1IjoibWFrc3ltLW1hbHRzZXYiLCJhIjoiY2tweTVkbG1tMDQ3dTJzcnJxMHV0enJ4MCJ9.2TfFQqNNty9MDAFxZriNaA";var nt=function(o,I){var g,T=o.filter(function(t){var y,d,s,p,b=(0,re.eg)(t==null||(y=t.hotel)===null||y===void 0?void 0:y.sabreTaxSetup),A=(d=t==null?void 0:t.hotel.priceInfo)===null||d===void 0?void 0:d[b.dailyPrice];return(t==null||(s=t.hotel)===null||s===void 0||(p=s.priceInfo)===null||p===void 0?void 0:p[b.dailyMemberPrice])||A}).map(function(t){var y,d,s,p,b,A=(0,re.eg)(t==null||(y=t.hotel)===null||y===void 0?void 0:y.sabreTaxSetup),Y=(d=t==null?void 0:t.hotel.priceInfo)===null||d===void 0?void 0:d[A.dailyPrice],a=t==null||(s=t.hotel)===null||s===void 0||(p=s.priceInfo)===null||p===void 0?void 0:p[A.dailyMemberPrice];return I.convertCurrency(a||Y,((b=t.hotel.priceInfo)===null||b===void 0?void 0:b.currency)||"USD","USD")});return(g=Math).min.apply(g,(0,R.Z)(T.filter(function(t){return t})))},ie=function(o,I,g){var T=nt(I,g),t=o.filter(function(d){return!d.hotel.priceLoaded}),y=o.filter(function(d){return d.hotel.priceLoaded});return(0,R.Z)(t).concat((0,R.Z)(y)).map(function(d){var s,p,b=d.id,A=d.longitude,Y=d.latitude,a=d.hotel,r=(0,re.eg)(a==null?void 0:a.sabreTaxSetup),G=(s=a.priceInfo)===null||s===void 0?void 0:s[r.dailyPrice],J=a==null||(p=a.priceInfo)===null||p===void 0?void 0:p[r.dailyMemberPrice],k="",le="",V=!1;if(a.priceLoaded&&a.priceInfo){G&&(le=g.getPriceWithCode(G,a.priceInfo.currency,void 0,void 0,void 0,!0)),J&&(k=g.getPriceWithCode(J,a.priceInfo.currency,void 0,void 0,void 0,!0));var X=a.priceInfo?g.convertCurrency(J||G,a.priceInfo.currency,"USD"):void 0;V=!!X&&X===T}return{type:"Feature",id:b,properties:{id:b,price:k||le,newHotelMessage:(0,Me.xM)(a)?"New hotel":void 0,lowMessage:V?"Lowest rate":void 0},geometry:{type:"Point",coordinates:[A,Y]}}})};function ot(o){var I,g,T,t=o.markers,y=o.isLoading,d=o.onPinClick,s=o.hotelPage,p=o.onClickContainerMap,b=o.heightScreen,A=o.hidePrice,Y=o.hideFullScreenBtn,a=(0,l.useState)(),r=a[0],G=a[1],J=(0,l.useState)(!1),k=J[0],le=J[1],V=(0,l.useRef)(null),X=(0,l.useState)([]),Ne=X[0],rt=X[1],K=(0,l.useRef)(),Fe=(0,l.useState)(),C=Fe[0],se=Fe[1],Pe=(0,l.useState)([]),B=Pe[0],it=Pe[1],Ze=(0,l.useState)(),Ie=Ze[0],lt=Ze[1],ut=(0,Q.v9)(function(e){return e.member.favorites.items}),dt=(0,Ke.Z)(),at=(0,$e.Z)(),ct=(0,et.Z)(),$=(0,_e.Z)(),st=(0,Q.v9)(function(e){return e.global.currency.rates}),Te=(0,Q.v9)(function(e){return e.global.currency.selectedCurrency}),ee=(0,qe.Z)(Te,st),Be=(0,Q.v9)(function(e){return e.booking.hotel.params}),c=(I=t.find(function(e){return e.id===C}))===null||I===void 0?void 0:I.hotel,ze=(0,re.eg)(c==null?void 0:c.sabreTaxSetup),vt=c==null?void 0:c.priceLoaded,ft=c==null||(g=c.priceInfo)===null||g===void 0?void 0:g[ze.dailyPrice],pt=c==null||(T=c.priceInfo)===null||T===void 0?void 0:T[ze.dailyMemberPrice],De=(0,Q.v9)(function(e){return e.search.hotel.viewType});(0,l.useEffect)(function(){De!==Qe.vt.map&&se(void 0)},[De]),(0,l.useEffect)(function(){if(K.current&&r){var e,u=document.createElement("div");u.className=Z().popup,c&&O.render((0,H.jsx)(Ue.Z,{hotel:c,favoriteUse:$,converter:ee,bookingParams:Be,newHotel:(0,Me.xM)(c),trackAddToWishList:dt,trackSelectItem:at}),u),(e=K.current)===null||e===void 0||e.setDOMContent(u)}c&&ct.trackViewItemList({listName:(0,tt.u)({path:window.location.pathname}),hotels:[c],hotelSearchData:Be})},[C,Te,r,vt,ft,pt,c==null?void 0:c.priceLoading,C&&$.isFavorite(C)]),(0,l.useEffect)(function(){r?r.on("load",function(){le(!0)}):G(ht())},[r]),(0,l.useEffect)(function(){r&&k&&ve(r,ie(t,B,ee))},[k]),(0,l.useEffect)(function(){return function(){r==null||r.remove()}},[]),(0,l.useEffect)(function(){if(!(t!=null&&t.length)||Ne.length!==t.length){je();return}var e=function(v,S){return v+S.id},u=Ne.reduce(e,""),h=t.reduce(e,"");u!==h&&je()},[r,t,k]),(0,l.useEffect)(function(){if(r&&k){var e,u=(e=ie(t,B,ee).find(function(h){var v;return(v=h.properties)===null||v===void 0?void 0:v.lowMessage}))===null||e===void 0?void 0:e.id;u&&u!==Ie&&lt(u)}},[B==null?void 0:B.map(function(e){return e.id}).join(",")]),(0,l.useEffect)(function(){r&&B.length&&ve(r,ie(t,B,ee))},[Ie,C&&$.isFavorite(C),ut]);var ht=function(){if(V.current){var e={projection:{name:"mercator"},container:V.current,style:"mapbox://styles/maksym-maltsev/clgkmhjmg007h01qq4u4834ob",zoom:2,attributionControl:!1};t&&t.length>0&&(e.center=[t[0].longitude,t[0].latitude]);var u=new(M()).Map(e);return Y||u.addControl(new(M()).FullscreenControl,"top-right"),u}},N=(0,l.useRef)([]),ve=function(e,u){if(k){var h=[],v=!0,S=!1,ue=void 0;try{for(var We,fe=u[Symbol.iterator]();!(v=(We=fe.next()).done);v=!0)(function(){var te,pe,he,me,ge,ye,be,Ce,xe,f=We.value,de=A||(te=f.properties)===null||te===void 0?void 0:te.price,z=(pe=f.properties)===null||pe===void 0?void 0:pe.newHotelMessage,F=de?(he=f.properties)===null||he===void 0?void 0:he.lowMessage:void 0,Re=z||F,ae=$.isFavorite((me=f.properties)===null||me===void 0?void 0:me.id),He=C===((ge=f.properties)===null||ge===void 0?void 0:ge.id),we=""+de+z+F+ae+(He?"+":"-")+((ye=f.properties)===null||ye===void 0?void 0:ye.id)+f.geometry.coordinates.join(",");if(h.push(we),N.current.some(function(j){return j.hash===we}))return"continue";var n=document.createElement("div"),P=document.createElement("p"),x=document.createElement("button");x.setAttribute("type","button"),He?n.style.zIndex="3":F?n.style.zIndex="2":z&&(n.style.zIndex="1");var Le="#300B5C";if(F?Le="#8BBCD9":z&&(Le="#F69B6F"),n.style.backgroundColor=Le,F?C===((be=f.properties)===null||be===void 0?void 0:be.id)?n.style.backgroundColor="#094F7F":(n.addEventListener("mouseenter",function(){n.style.backgroundColor="#094F7F"}),n.addEventListener("mouseleave",function(){n.style.backgroundColor="#8BBCD9"})):z?C===((Ce=f.properties)===null||Ce===void 0?void 0:Ce.id)?n.style.backgroundColor="#FF6D26":(n.addEventListener("mouseenter",function(){n.style.backgroundColor="#FF6D26"}),n.addEventListener("mouseleave",function(){n.style.backgroundColor="#F69B6F"})):C===((xe=f.properties)===null||xe===void 0?void 0:xe.id)?n.style.backgroundColor="#bbb0dc":(n.addEventListener("mouseenter",function(){n.style.backgroundColor="#bbb0dc"}),n.addEventListener("mouseleave",function(){n.style.backgroundColor="#300B5C"})),de){n.style.padding="4px 56px 4px 20px",n.style.borderRadius="5px",n.style.boxShadow="0px 4px 4px 0px rgba(0, 0, 0, 0.25)",n.style.cursor="pointer";var D=document.createElement("p");if(n.appendChild(D),D.style.color="#fff",D.style.fontFamily="Jost-SemiBold",D.style.fontSize="18px",D.style.lineHeight=Re?"20px":"26px",D.style.fontWeight=Re?"bold":"500",D.textContent=de,n.appendChild(x),x.appendChild(Ae(ae)),x.style.position="absolute",x.style.top="1px",x.style.right="20px",x.style.width="32px",x.style.height="32px",x.style.cursor="pointer",x.style.zIndex="1",x.addEventListener("click",function(j){var W;j.stopPropagation(),j.preventDefault(),$.favoriteClick((W=f.properties)===null||W===void 0?void 0:W.id,Oe.r.hotel)}),z&&!F){var _=document.createElement("p");n.appendChild(_),_.style.color="#fff",_.style.fontSize="12px",_.style.fontWeight="bold",_.style.textTransform="uppercase",_.textContent=z}F&&(n.appendChild(P),P.style.color="#fff",P.style.fontFamily="Jost-SemiBold",P.style.fontSize="12px",P.style.lineHeight="15px",P.style.fontWeight="bold",P.style.textTransform="uppercase",P.textContent=F)}else if(n.style.width="".concat(30,"px"),n.style.height="".concat(30,"px"),n.style.borderRadius="50%",n.style.cursor="pointer",ae){var ke=Ae(ae);ke.style.left="-0.9px",ke.style.position="absolute",n.appendChild(ke)}else{var Ee=Xe();Ee.style.left="1px",Ee.style.position="absolute",n.appendChild(Ee)}n.addEventListener("click",function(j){if(j.preventDefault(),j.stopPropagation(),e){for(var W,Se,Je=(W=f.properties)===null||W===void 0?void 0:W.id,q=f.geometry.coordinates.slice();Math.abs(q[0]-e.getCenter().lng)>180;)q[0]=Number(q[0])+(e.getCenter().lng>q[0]?360:-360);K.current=K.current||new(M()).Popup({closeButton:!1,className:Z().popup,closeOnMove:!1,offset:18,anchor:"top",maxWidth:"320px",focusAfterOpen:!0}).on("close",function(){return se(void 0)}),(Se=K.current)===null||Se===void 0||Se.setLngLat(q).addTo(e),e.flyTo({center:q,offset:[0,-200],duration:300,curve:0}),d==null||d(Je),se(Je)}});var mt=new(M()).Marker(n).setLngLat(f.geometry.coordinates).addTo(e);N.current.push({hash:we,marker:mt})})()}catch(te){S=!0,ue=te}finally{try{v||fe.return==null||fe.return()}finally{if(S)throw ue}}if(N.current.length)for(var U=N.current.length-1;U>=0;U--)h.includes(N.current[U].hash)||(N.current[U].marker.getElement().remove(),N.current[U].marker.remove(),N.current.splice(U,1))}},E=r==null?void 0:r.getBounds();(0,l.useEffect)(function(){var e=function(){r&&E&&it(t.filter(function(u){var h=u.latitude,v=u.longitude;return v>=E.getWest()&&v<=E.getEast()&&h>=E.getSouth()&&h<=E.getNorth()}))};return r==null||r.on("zoomend",e),r==null||r.on("moveend",e),function(){r==null||r.off("zoomend",e),r==null||r.off("moveend",e)}},[E?Math.round(1e3*E.getWest()):null,E?Math.round(1e3*E.getSouth()):null,!r,t==null?void 0:t.map(function(e){return e.id}).join(",")]);var je=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},u=e.customPadding,h=e.customMaxZoom;if(r&&k){if(!y&&t.length){var v=t.map(function(S){return[S.longitude,S.latitude]}).reduce(function(S,ue){return S.extend(ue)},new(M()).LngLatBounds);C||r.fitBounds(v,{padding:u===void 0?100:u,maxZoom:h===void 0?12:h})}ve(r,ie(t,B,ee)),rt(t)}};return(0,H.jsx)("div",{className:s?"h-full w-full":"w-full absolute z-20 left-0 md:relative md:px-6 md:w-full ".concat(Z().containerMapBoxHotel," ").concat(b&&Z().heightScreen),onClick:p,style:{borderRadius:"14px"},children:(0,H.jsx)("div",{ref:V,style:{borderRadius:"14px"},className:"h-full w-full ".concat(s?"":"shadow-lg"," ").concat(Z().mapbox)})})}}}]);
