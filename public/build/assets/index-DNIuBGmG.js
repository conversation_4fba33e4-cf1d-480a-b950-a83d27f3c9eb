import{r as a,R as Y}from"./PeopleSelectPopover-CDKo4AC-.js";import{c as ve,C as ce,e as q,g as Ne,m as je,n as _e,k as Te,_ as re,q as et,f as Oe,h as oe,l as ze,i as tt}from"./genStyleUtils-CI3YU7Yv.js";import{w as Re,a as $e,D as Me,o as nt,c as Pe,_ as Ve}from"./isVisible-Bd4H7hpW.js";import{F as ue}from"./context-Gzj2nObQ.js";import{a as at,i as De,d as Fe,c as Be,R as ot,u as rt,b as st,g as it}from"./index-wg7qNA5H.js";import{I as ge,u as lt,a as ut,B as ct,r as Ae,t as dt,g as ft}from"./Input-DCb0FIZx.js";import{p as mt}from"./pickAttrs-D1C8emUZ.js";import{u as Ee}from"./useSize-CbUlsqBW.js";import{I as ke,B as pt}from"./button-C2fNxKeA.js";import{u as Le}from"./color-DKTup0-d.js";import{u as He}from"./useMergedState-BDSe6zqT.js";import{R as vt}from"./index-CaUFHQr4.js";import{u as gt}from"./useCSSVarCls-DWPRWpfJ.js";var xt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"}}]},name:"eye",theme:"outlined"},bt=function(n,o){return a.createElement(ke,ve({},n,{ref:o,icon:xt}))},ht=a.forwardRef(bt);const Ct=e=>{const{getPrefixCls:n,direction:o}=a.useContext(ce),{prefixCls:t,className:r}=e,d=n("input-group",t),u=n("input"),[c,y,p]=at(u),f=q(d,p,{[`${d}-lg`]:e.size==="large",[`${d}-sm`]:e.size==="small",[`${d}-compact`]:e.compact,[`${d}-rtl`]:o==="rtl"},y,r),C=a.useContext(ue),x=a.useMemo(()=>Object.assign(Object.assign({},C),{isFormItemInput:!1}),[C]);return c(a.createElement("span",{className:f,style:e.style,onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,onFocus:e.onFocus,onBlur:e.onBlur},a.createElement(ue.Provider,{value:x},e.children)))},yt=e=>{const{componentCls:n,paddingXS:o}=e;return{[n]:{display:"inline-flex",alignItems:"center",flexWrap:"nowrap",columnGap:o,[`${n}-input-wrapper`]:{position:"relative",[`${n}-mask-icon`]:{position:"absolute",zIndex:"1",top:"50%",right:"50%",transform:"translate(50%, -50%)",pointerEvents:"none"},[`${n}-mask-input`]:{color:"transparent",caretColor:e.colorText},[`${n}-mask-input[type=number]::-webkit-inner-spin-button`]:{"-webkit-appearance":"none",margin:0},[`${n}-mask-input[type=number]`]:{"-moz-appearance":"textfield"}},"&-rtl":{direction:"rtl"},[`${n}-input`]:{textAlign:"center",paddingInline:e.paddingXXS},[`&${n}-sm ${n}-input`]:{paddingInline:e.calc(e.paddingXXS).div(2).equal()},[`&${n}-lg ${n}-input`]:{paddingInline:e.paddingXS}}}},St=Ne(["Input","OTP"],e=>{const n=je(e,De(e));return yt(n)},Fe);var wt=function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(o[t[r]]=e[t[r]]);return o};const zt=a.forwardRef((e,n)=>{const{className:o,value:t,onChange:r,onActiveChange:d,index:u,mask:c}=e,y=wt(e,["className","value","onChange","onActiveChange","index","mask"]),{getPrefixCls:p}=a.useContext(ce),f=p("otp"),C=typeof c=="string"?c:t,x=a.useRef(null);a.useImperativeHandle(n,()=>x.current);const N=w=>{r(u,w.target.value)},b=()=>{Re(()=>{var w;const S=(w=x.current)===null||w===void 0?void 0:w.input;document.activeElement===S&&S&&S.select()})},j=w=>{const{key:S,ctrlKey:$,metaKey:V}=w;S==="ArrowLeft"?d(u-1):S==="ArrowRight"?d(u+1):S==="z"&&($||V)&&w.preventDefault(),b()},M=w=>{w.key==="Backspace"&&!t&&d(u-1),b()};return a.createElement("span",{className:`${f}-input-wrapper`,role:"presentation"},c&&t!==""&&t!==void 0&&a.createElement("span",{className:`${f}-mask-icon`,"aria-hidden":"true"},C),a.createElement(ge,Object.assign({"aria-label":`OTP Input ${u+1}`,type:c===!0?"password":"text"},y,{ref:x,value:t,onInput:N,onFocus:b,onKeyDown:j,onKeyUp:M,onMouseDown:b,onMouseUp:b,className:q(o,{[`${f}-mask-input`]:c})})))});var Rt=function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(o[t[r]]=e[t[r]]);return o};function pe(e){return(e||"").split("")}const Et=e=>{const{index:n,prefixCls:o,separator:t}=e,r=typeof t=="function"?t(n):t;return r?a.createElement("span",{className:`${o}-separator`},r):null},It=a.forwardRef((e,n)=>{const{prefixCls:o,length:t=6,size:r,defaultValue:d,value:u,onChange:c,formatter:y,separator:p,variant:f,disabled:C,status:x,autoFocus:N,mask:b,type:j,onInput:M,inputMode:w}=e,S=Rt(e,["prefixCls","length","size","defaultValue","value","onChange","formatter","separator","variant","disabled","status","autoFocus","mask","type","onInput","inputMode"]),{getPrefixCls:$,direction:V}=a.useContext(ce),m=$("otp",o),D=mt(S,{aria:!0,data:!0,attr:!0}),[F,H,k]=St(m),P=Ee(s=>r??s),I=a.useContext(ue),A=Be(I.status,x),K=a.useMemo(()=>Object.assign(Object.assign({},I),{status:A,hasFeedback:!1,feedbackIcon:null}),[I,A]),W=a.useRef(null),_=a.useRef({});a.useImperativeHandle(n,()=>({focus:()=>{var s;(s=_.current[0])===null||s===void 0||s.focus()},blur:()=>{var s;for(let l=0;l<t;l+=1)(s=_.current[l])===null||s===void 0||s.blur()},nativeElement:W.current}));const B=s=>y?y(s):s,[z,v]=a.useState(()=>pe(B(d||"")));a.useEffect(()=>{u!==void 0&&v(pe(u))},[u]);const Z=$e(s=>{v(s),M&&M(s),c&&s.length===t&&s.every(l=>l)&&s.some((l,g)=>z[g]!==l)&&c(s.join(""))}),Q=$e((s,l)=>{let g=_e(z);for(let R=0;R<s;R+=1)g[R]||(g[R]="");l.length<=1?g[s]=l:g=g.slice(0,s).concat(pe(l)),g=g.slice(0,t);for(let R=g.length-1;R>=0&&!g[R];R-=1)g.pop();const J=B(g.map(R=>R||" ").join(""));return g=pe(J).map((R,ne)=>R===" "&&!g[ne]?g[ne]:R),g}),U=(s,l)=>{var g;const J=Q(s,l),R=Math.min(s+l.length,t-1);R!==s&&J[s]!==void 0&&((g=_.current[R])===null||g===void 0||g.focus()),Z(J)},L=s=>{var l;(l=_.current[s])===null||l===void 0||l.focus()},i={variant:f,disabled:C,status:A,mask:b,type:j,inputMode:w};return F(a.createElement("div",Object.assign({},D,{ref:W,className:q(m,{[`${m}-sm`]:P==="small",[`${m}-lg`]:P==="large",[`${m}-rtl`]:V==="rtl"},k,H),role:"group"}),a.createElement(ue.Provider,{value:K},Array.from({length:t}).map((s,l)=>{const g=`otp-${l}`,J=z[l]||"";return a.createElement(a.Fragment,{key:g},a.createElement(zt,Object.assign({ref:R=>{_.current[l]=R},index:l,size:P,htmlSize:1,className:`${m}-input`,onChange:U,value:J,onActiveChange:L,autoFocus:l===0&&N},i)),l<t-1&&a.createElement(Et,{separator:p,index:l,prefixCls:m}))}))))});var Ot={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"}},{tag:"path",attrs:{d:"M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"}}]},name:"eye-invisible",theme:"outlined"},$t=function(n,o){return a.createElement(ke,ve({},n,{ref:o,icon:Ot}))},Pt=a.forwardRef($t),At=function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(o[t[r]]=e[t[r]]);return o};const Nt=e=>e?a.createElement(ht,null):a.createElement(Pt,null),jt={click:"onClick",hover:"onMouseOver"},_t=a.forwardRef((e,n)=>{const{disabled:o,action:t="click",visibilityToggle:r=!0,iconRender:d=Nt,suffix:u}=e,c=a.useContext(Me),y=o??c,p=typeof r=="object"&&r.visible!==void 0,[f,C]=a.useState(()=>p?r.visible:!1),x=a.useRef(null);a.useEffect(()=>{p&&C(r.visible)},[p,r]);const N=lt(x),b=()=>{var I;if(y)return;f&&N();const A=!f;C(A),typeof r=="object"&&((I=r.onVisibleChange)===null||I===void 0||I.call(r,A))},j=I=>{const A=jt[t]||"",K=d(f),W={[A]:b,className:`${I}-icon`,key:"passwordIcon",onMouseDown:_=>{_.preventDefault()},onMouseUp:_=>{_.preventDefault()}};return a.cloneElement(a.isValidElement(K)?K:a.createElement("span",null,K),W)},{className:M,prefixCls:w,inputPrefixCls:S,size:$}=e,V=At(e,["className","prefixCls","inputPrefixCls","size"]),{getPrefixCls:m}=a.useContext(ce),D=m("input",S),F=m("input-password",w),H=r&&j(F),k=q(F,M,{[`${F}-${$}`]:!!$}),P=Object.assign(Object.assign({},nt(V,["suffix","iconRender","visibilityToggle"])),{type:f?"text":"password",className:k,prefixCls:D,suffix:a.createElement(a.Fragment,null,H,u)});return $&&(P.size=$),a.createElement(ge,Object.assign({ref:Te(n,x)},P))});var Tt=function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(o[t[r]]=e[t[r]]);return o};const Mt=a.forwardRef((e,n)=>{const{prefixCls:o,inputPrefixCls:t,className:r,size:d,suffix:u,enterButton:c=!1,addonAfter:y,loading:p,disabled:f,onSearch:C,onChange:x,onCompositionStart:N,onCompositionEnd:b,variant:j,onPressEnter:M}=e,w=Tt(e,["prefixCls","inputPrefixCls","className","size","suffix","enterButton","addonAfter","loading","disabled","onSearch","onChange","onCompositionStart","onCompositionEnd","variant","onPressEnter"]),{getPrefixCls:S,direction:$}=a.useContext(ce),V=a.useRef(!1),m=S("input-search",o),D=S("input",t),{compactSize:F}=Le(m,$),H=Ee(i=>{var s;return(s=d??F)!==null&&s!==void 0?s:i}),k=a.useRef(null),P=i=>{i!=null&&i.target&&i.type==="click"&&C&&C(i.target.value,i,{source:"clear"}),x==null||x(i)},I=i=>{var s;document.activeElement===((s=k.current)===null||s===void 0?void 0:s.input)&&i.preventDefault()},A=i=>{var s,l;C&&C((l=(s=k.current)===null||s===void 0?void 0:s.input)===null||l===void 0?void 0:l.value,i,{source:"input"})},K=i=>{V.current||p||(M==null||M(i),A(i))},W=typeof c=="boolean"?a.createElement(ot,null):null,_=`${m}-button`;let B;const z=c||{},v=z.type&&z.type.__ANT_BUTTON===!0;v||z.type==="button"?B=Pe(z,Object.assign({onMouseDown:I,onClick:i=>{var s,l;(l=(s=z==null?void 0:z.props)===null||s===void 0?void 0:s.onClick)===null||l===void 0||l.call(s,i),A(i)},key:"enterButton"},v?{className:_,size:H}:{})):B=a.createElement(pt,{className:_,color:c?"primary":"default",size:H,disabled:f,key:"enterButton",onMouseDown:I,onClick:A,loading:p,icon:W,variant:j==="borderless"||j==="filled"||j==="underlined"?"text":c?"solid":void 0},c),y&&(B=[B,Pe(y,{key:"addonAfter"})]);const Z=q(m,{[`${m}-rtl`]:$==="rtl",[`${m}-${H}`]:!!H,[`${m}-with-button`]:!!c},r),Q=i=>{V.current=!0,N==null||N(i)},U=i=>{V.current=!1,b==null||b(i)},L=Object.assign(Object.assign({},w),{className:Z,prefixCls:D,type:"search",size:H,variant:j,onPressEnter:K,onCompositionStart:Q,onCompositionEnd:U,addonAfter:B,suffix:u,onChange:P,disabled:f});return a.createElement(ge,Object.assign({ref:Te(k,n)},L))});var Vt=`
  min-height:0 !important;
  max-height:none !important;
  height:0 !important;
  visibility:hidden !important;
  overflow:hidden !important;
  position:absolute !important;
  z-index:-1000 !important;
  top:0 !important;
  right:0 !important;
  pointer-events: none !important;
`,Dt=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","font-variant","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing","word-break","white-space"],Ce={},X;function Ft(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,o=e.getAttribute("id")||e.getAttribute("data-reactid")||e.getAttribute("name");if(n&&Ce[o])return Ce[o];var t=window.getComputedStyle(e),r=t.getPropertyValue("box-sizing")||t.getPropertyValue("-moz-box-sizing")||t.getPropertyValue("-webkit-box-sizing"),d=parseFloat(t.getPropertyValue("padding-bottom"))+parseFloat(t.getPropertyValue("padding-top")),u=parseFloat(t.getPropertyValue("border-bottom-width"))+parseFloat(t.getPropertyValue("border-top-width")),c=Dt.map(function(p){return"".concat(p,":").concat(t.getPropertyValue(p))}).join(";"),y={sizingStyle:c,paddingSize:d,borderSize:u,boxSizing:r};return n&&o&&(Ce[o]=y),y}function Bt(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,t=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null;X||(X=document.createElement("textarea"),X.setAttribute("tab-index","-1"),X.setAttribute("aria-hidden","true"),X.setAttribute("name","hiddenTextarea"),document.body.appendChild(X)),e.getAttribute("wrap")?X.setAttribute("wrap",e.getAttribute("wrap")):X.removeAttribute("wrap");var r=Ft(e,n),d=r.paddingSize,u=r.borderSize,c=r.boxSizing,y=r.sizingStyle;X.setAttribute("style","".concat(y,";").concat(Vt)),X.value=e.value||e.placeholder||"";var p=void 0,f=void 0,C,x=X.scrollHeight;if(c==="border-box"?x+=u:c==="content-box"&&(x-=d),o!==null||t!==null){X.value=" ";var N=X.scrollHeight-d;o!==null&&(p=N*o,c==="border-box"&&(p=p+d+u),x=Math.max(p,x)),t!==null&&(f=N*t,c==="border-box"&&(f=f+d+u),C=x>f?"":"hidden",x=Math.min(f,x))}var b={height:x,overflowY:C,resize:"none"};return p&&(b.minHeight=p),f&&(b.maxHeight=f),b}var kt=["prefixCls","defaultValue","value","autoSize","onResize","className","style","disabled","onChange","onInternalAutoSize"],ye=0,Se=1,we=2,Lt=a.forwardRef(function(e,n){var o=e,t=o.prefixCls,r=o.defaultValue,d=o.value,u=o.autoSize,c=o.onResize,y=o.className,p=o.style,f=o.disabled,C=o.onChange;o.onInternalAutoSize;var x=Ve(o,kt),N=He(r,{value:d,postState:function(i){return i??""}}),b=re(N,2),j=b[0],M=b[1],w=function(i){M(i.target.value),C==null||C(i)},S=a.useRef();a.useImperativeHandle(n,function(){return{textArea:S.current}});var $=a.useMemo(function(){return u&&et(u)==="object"?[u.minRows,u.maxRows]:[]},[u]),V=re($,2),m=V[0],D=V[1],F=!!u,H=a.useState(we),k=re(H,2),P=k[0],I=k[1],A=a.useState(),K=re(A,2),W=K[0],_=K[1],B=function(){I(ye)};Oe(function(){F&&B()},[d,m,D,F]),Oe(function(){if(P===ye)I(Se);else if(P===Se){var L=Bt(S.current,!1,m,D);I(we),_(L)}},[P]);var z=a.useRef(),v=function(){Re.cancel(z.current)},Z=function(i){P===we&&(c==null||c(i),u&&(v(),z.current=Re(function(){B()})))};a.useEffect(function(){return v},[]);var Q=F?W:null,U=oe(oe({},p),Q);return(P===ye||P===Se)&&(U.overflowY="hidden",U.overflowX="hidden"),a.createElement(vt,{onResize:Z,disabled:!(u||c)},a.createElement("textarea",ve({},x,{ref:S,style:U,className:q(t,y,ze({},"".concat(t,"-disabled"),f)),disabled:f,value:j,onChange:w})))}),Ht=["defaultValue","value","onFocus","onBlur","onChange","allowClear","maxLength","onCompositionStart","onCompositionEnd","suffix","prefixCls","showCount","count","className","style","disabled","hidden","classNames","styles","onResize","onClear","onPressEnter","readOnly","autoSize","onKeyDown"],Kt=Y.forwardRef(function(e,n){var o,t=e.defaultValue,r=e.value,d=e.onFocus,u=e.onBlur,c=e.onChange,y=e.allowClear,p=e.maxLength,f=e.onCompositionStart,C=e.onCompositionEnd,x=e.suffix,N=e.prefixCls,b=N===void 0?"rc-textarea":N,j=e.showCount,M=e.count,w=e.className,S=e.style,$=e.disabled,V=e.hidden,m=e.classNames,D=e.styles,F=e.onResize,H=e.onClear,k=e.onPressEnter,P=e.readOnly,I=e.autoSize,A=e.onKeyDown,K=Ve(e,Ht),W=He(t,{value:r,defaultValue:t}),_=re(W,2),B=_[0],z=_[1],v=B==null?"":String(B),Z=Y.useState(!1),Q=re(Z,2),U=Q[0],L=Q[1],i=Y.useRef(!1),s=Y.useState(null),l=re(s,2),g=l[0],J=l[1],R=a.useRef(null),ne=a.useRef(null),G=function(){var h;return(h=ne.current)===null||h===void 0?void 0:h.textArea},se=function(){G().focus()};a.useImperativeHandle(n,function(){var T;return{resizableTextArea:ne.current,focus:se,blur:function(){G().blur()},nativeElement:((T=R.current)===null||T===void 0?void 0:T.nativeElement)||G()}}),a.useEffect(function(){L(function(T){return!$&&T})},[$]);var xe=Y.useState(null),fe=re(xe,2),ie=fe[0],be=fe[1];Y.useEffect(function(){if(ie){var T;(T=G()).setSelectionRange.apply(T,_e(ie))}},[ie]);var O=ut(M,j),E=(o=O.max)!==null&&o!==void 0?o:p,te=Number(E)>0,ee=O.strategy(v),Ke=!!E&&ee>E,Ie=function(h,ae){var me=ae;!i.current&&O.exceedFormatter&&O.max&&O.strategy(ae)>O.max&&(me=O.exceedFormatter(ae,{max:O.max}),ae!==me&&be([G().selectionStart||0,G().selectionEnd||0])),z(me),Ae(h.currentTarget,h,c,me)},qe=function(h){i.current=!0,f==null||f(h)},Xe=function(h){i.current=!1,Ie(h,h.currentTarget.value),C==null||C(h)},We=function(h){Ie(h,h.target.value)},Ue=function(h){h.key==="Enter"&&k&&k(h),A==null||A(h)},Ge=function(h){L(!0),d==null||d(h)},Ye=function(h){L(!1),u==null||u(h)},Ze=function(h){z(""),se(),Ae(G(),h,c)},he=x,le;O.show&&(O.showFormatter?le=O.showFormatter({value:v,count:ee,maxLength:E}):le="".concat(ee).concat(te?" / ".concat(E):""),he=Y.createElement(Y.Fragment,null,he,Y.createElement("span",{className:q("".concat(b,"-data-count"),m==null?void 0:m.count),style:D==null?void 0:D.count},le)));var Qe=function(h){var ae;F==null||F(h),(ae=G())!==null&&ae!==void 0&&ae.style.height&&J(!0)},Je=!I&&!j&&!y;return Y.createElement(ct,{ref:R,value:v,allowClear:y,handleReset:Ze,suffix:he,prefixCls:b,classNames:oe(oe({},m),{},{affixWrapper:q(m==null?void 0:m.affixWrapper,ze(ze({},"".concat(b,"-show-count"),j),"".concat(b,"-textarea-allow-clear"),y))}),disabled:$,focused:U,className:q(w,Ke&&"".concat(b,"-out-of-range")),style:oe(oe({},S),g&&!Je?{height:"auto"}:{}),dataAttrs:{affixWrapper:{"data-count":typeof le=="string"?le:void 0}},hidden:V,readOnly:P,onClear:H},Y.createElement(Lt,ve({},K,{autoSize:I,maxLength:p,onKeyDown:Ue,onChange:We,onFocus:Ge,onBlur:Ye,onCompositionStart:qe,onCompositionEnd:Xe,className:q(m==null?void 0:m.textarea),style:oe(oe({},D==null?void 0:D.textarea),{},{resize:S==null?void 0:S.resize}),disabled:$,prefixCls:b,onResize:Qe,ref:ne,readOnly:P})))});const qt=e=>{const{componentCls:n,paddingLG:o}=e,t=`${n}-textarea`;return{[`textarea${n}`]:{maxWidth:"100%",height:"auto",minHeight:e.controlHeight,lineHeight:e.lineHeight,verticalAlign:"bottom",transition:`all ${e.motionDurationSlow}`,resize:"vertical",[`&${n}-mouse-active`]:{transition:`all ${e.motionDurationSlow}, height 0s, width 0s`}},[`${n}-textarea-affix-wrapper-resize-dirty`]:{width:"auto"},[t]:{position:"relative","&-show-count":{[`${n}-data-count`]:{position:"absolute",bottom:e.calc(e.fontSize).mul(e.lineHeight).mul(-1).equal(),insetInlineEnd:0,color:e.colorTextDescription,whiteSpace:"nowrap",pointerEvents:"none"}},[`
        &-allow-clear > ${n},
        &-affix-wrapper${t}-has-feedback ${n}
      `]:{paddingInlineEnd:o},[`&-affix-wrapper${n}-affix-wrapper`]:{padding:0,[`> textarea${n}`]:{fontSize:"inherit",border:"none",outline:"none",background:"transparent",minHeight:e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),"&:focus":{boxShadow:"none !important"}},[`${n}-suffix`]:{margin:0,"> *:not(:last-child)":{marginInline:0},[`${n}-clear-icon`]:{position:"absolute",insetInlineEnd:e.paddingInline,insetBlockStart:e.paddingXS},[`${t}-suffix`]:{position:"absolute",top:0,insetInlineEnd:e.paddingInline,bottom:0,zIndex:1,display:"inline-flex",alignItems:"center",margin:"auto",pointerEvents:"none"}}},[`&-affix-wrapper${n}-affix-wrapper-rtl`]:{[`${n}-suffix`]:{[`${n}-data-count`]:{direction:"ltr",insetInlineStart:0}}},[`&-affix-wrapper${n}-affix-wrapper-sm`]:{[`${n}-suffix`]:{[`${n}-clear-icon`]:{insetInlineEnd:e.paddingInlineSM}}}}}},Xt=Ne(["Input","TextArea"],e=>{const n=je(e,De(e));return qt(n)},Fe,{resetFont:!1});var Wt=function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(o[t[r]]=e[t[r]]);return o};const Ut=a.forwardRef((e,n)=>{var o;const{prefixCls:t,bordered:r=!0,size:d,disabled:u,status:c,allowClear:y,classNames:p,rootClassName:f,className:C,style:x,styles:N,variant:b,showCount:j,onMouseDown:M,onResize:w}=e,S=Wt(e,["prefixCls","bordered","size","disabled","status","allowClear","classNames","rootClassName","className","style","styles","variant","showCount","onMouseDown","onResize"]),{getPrefixCls:$,direction:V,allowClear:m,autoComplete:D,className:F,style:H,classNames:k,styles:P}=tt("textArea"),I=a.useContext(Me),A=u??I,{status:K,hasFeedback:W,feedbackIcon:_}=a.useContext(ue),B=Be(K,c),z=a.useRef(null);a.useImperativeHandle(n,()=>{var O;return{resizableTextArea:(O=z.current)===null||O===void 0?void 0:O.resizableTextArea,focus:E=>{var te,ee;dt((ee=(te=z.current)===null||te===void 0?void 0:te.resizableTextArea)===null||ee===void 0?void 0:ee.textArea,E)},blur:()=>{var E;return(E=z.current)===null||E===void 0?void 0:E.blur()}}});const v=$("input",t),Z=gt(v),[Q,U,L]=rt(v,f),[i]=Xt(v,Z),{compactSize:s,compactItemClassnames:l}=Le(v,V),g=Ee(O=>{var E;return(E=d??s)!==null&&E!==void 0?E:O}),[J,R]=st("textArea",b,r),ne=ft(y??m),[G,se]=a.useState(!1),[xe,fe]=a.useState(!1),ie=O=>{se(!0),M==null||M(O);const E=()=>{se(!1),document.removeEventListener("mouseup",E)};document.addEventListener("mouseup",E)},be=O=>{var E,te;if(w==null||w(O),G&&typeof getComputedStyle=="function"){const ee=(te=(E=z.current)===null||E===void 0?void 0:E.nativeElement)===null||te===void 0?void 0:te.querySelector("textarea");ee&&getComputedStyle(ee).resize==="both"&&fe(!0)}};return Q(i(a.createElement(Kt,Object.assign({autoComplete:D},S,{style:Object.assign(Object.assign({},H),x),styles:Object.assign(Object.assign({},P),N),disabled:A,allowClear:ne,className:q(L,Z,C,f,l,F,xe&&`${v}-textarea-affix-wrapper-resize-dirty`),classNames:Object.assign(Object.assign(Object.assign({},p),k),{textarea:q({[`${v}-sm`]:g==="small",[`${v}-lg`]:g==="large"},U,p==null?void 0:p.textarea,k.textarea,G&&`${v}-mouse-active`),variant:q({[`${v}-${J}`]:R},it(v,B)),affixWrapper:q(`${v}-textarea-affix-wrapper`,{[`${v}-affix-wrapper-rtl`]:V==="rtl",[`${v}-affix-wrapper-sm`]:g==="small",[`${v}-affix-wrapper-lg`]:g==="large",[`${v}-textarea-show-count`]:j||((o=e.count)===null||o===void 0?void 0:o.show)},U)}),prefixCls:v,suffix:W&&a.createElement("span",{className:`${v}-textarea-suffix`},_),showCount:j,ref:z,onResize:be,onMouseDown:ie}))))}),de=ge;de.Group=Ct;de.Search=Mt;de.TextArea=Ut;de.Password=_t;de.OTP=It;export{de as I,ht as R};
