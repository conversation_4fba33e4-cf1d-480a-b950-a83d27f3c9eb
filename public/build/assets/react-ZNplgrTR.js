import{R as u}from"./PeopleSelectPopover-CDKo4AC-.js";const S=t=>{let e;const n=new Set,o=(s,r)=>{const c=typeof s=="function"?s(e):s;if(!Object.is(c,e)){const g=e;e=r??(typeof c!="object"||c===null)?c:Object.assign({},e,c),n.forEach(f=>f(e,g))}},a=()=>e,i={setState:o,getState:a,getInitialState:()=>l,subscribe:s=>(n.add(s),()=>n.delete(s))},l=e=t(o,a,i);return i},d=t=>t?S(t):S,I=t=>t;function j(t,e=I){const n=u.useSyncExternalStore(t.subscribe,u.useCallback(()=>e(t.getState()),[t,e]),u.useCallback(()=>e(t.getInitialState()),[t,e]));return u.useDebugValue(n),n}const b=t=>{const e=d(t),n=o=>j(e,o);return Object.assign(n,e),n},O=t=>t?b(t):b;export{O as c};
