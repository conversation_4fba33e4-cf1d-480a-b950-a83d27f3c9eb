import{j as e}from"./hotel-item-D_zvdyIk.js";import{c as v}from"./client-CHYie6Ha.js";import{r as o}from"./PeopleSelectPopover-CDKo4AC-.js";import{G as w,u as y}from"./GhaConfigProvider-BB5IW5PM.js";import"./mock-OVdH_lkV.js";import{H as N,O as k}from"./OfferItem-RRpQgDme.js";import{a as p}from"./helper-D414uohx.js";import"./index-C_TizibV.js";import"./isVisible-Bd4H7hpW.js";import"./genStyleUtils-CI3YU7Yv.js";import"./CloseOutlined-CGWqYTdG.js";import"./button-C2fNxKeA.js";import"./index-DKvg8qd3.js";import"./useSize-CbUlsqBW.js";import"./color-DKTup0-d.js";import"./KeyCode-lh1qUinJ.js";import"./pickAttrs-D1C8emUZ.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./index-DxcGRMc6.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./zoom-BbRr1fkt.js";import"./extendsObject-78o_rR5W.js";import"./index-DR0pJ98a.js";import"./index-Dq7h7Pqt.js";function S(){const[a,n]=o.useState(!0),[_,f]=o.useState(!1),[r,h]=o.useState(["hotel"]),[l,c]=o.useState({}),[u]=o.useState([{key:"hotel",label:"酒店"},{key:"offer",label:"住宿优惠"}]),j=y();o.useEffect(()=>{n(!0),p.userGetCollectItems(["hotel","offer"]).subscribe(s=>{if(n(!1),s.status_code!==200){f(!0);return}c(s.data)})},[]);function b(s){let t=[...r];r.includes(s.key)&&r.length>1&&(t=t.filter(i=>i!==s.key)),r.includes(s.key)||t.push(s.key),h(t)}function m(s,t){p.collectItem({type:s,id:t.id}).subscribe(i=>{if(i.status_code!==200){j.error(i.msg);return}const d=[...l[s]],x=d.find(g=>g.id===t.id);x.is_collect=!x.is_collect,c({...l,[s]:d})})}return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:`px-10 hidden lg:block ${a?"pb-5 lg:shadow-[0px_5px_5px_0px_rgba(0,0,0,0.03)] border-b border-[#919191]/20":""}`,children:e.jsx("h2",{className:"font-18 font-bold",children:"我的收藏"})}),a?e.jsx("div",{className:"py-8",children:e.jsx("div",{className:"w-10 h-10 mx-auto flex items-center justify-center loading-ami",children:e.jsx("i",{className:"iconfont icon-loading text-4xl"})})}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"pb-5 lg:px-10 lg:mt-4 lg:shadow-[0px_5px_5px_0px_rgba(0,0,0,0.03)] border-b border-[#919191]/20",children:e.jsx("div",{className:"-mx-2.5 flex md:block",children:u.map(s=>e.jsx("div",{onClick:()=>b(s),className:`flex-1 ${r.includes(s.key)?"gha-primary-btn":"gha-btn"} w-32 !py-1.5 mx-2.5`,children:s.label},s.key))})}),e.jsxs("div",{className:"py-6 lg:p-10",children:[e.jsxs("div",{className:`${r.includes("hotel")?"":"hidden"}`,children:[e.jsx("h2",{className:"font-16 font-Jost-SemiBold font-bold text-center md:text-left",children:"酒店"}),e.jsx("div",{className:"flex flex-row flex-wrap -mx-2 -mt-2",children:(l.hotel||[]).map((s,t)=>e.jsx("div",{className:"px-2 w-full md:w-1/2 xl:w-1/3 mt-4",children:e.jsx(N,{hotel:s,onFav:()=>m("hotel",s)})},t))})]}),r.length>1&&e.jsx("div",{className:"w-full h-px bg-[#919191]/20 my-7.5"}),e.jsxs("div",{className:`${r.includes("offer")?"":"hidden"}`,children:[e.jsx("h2",{className:"font-16 font-Jost-SemiBold font-bold",children:"住宿优惠"}),e.jsx("div",{className:"flex flex-row flex-wrap -mx-2 -mt-2",children:(l.offer||[]).map((s,t)=>e.jsx("div",{className:"px-2 w-full md:w-1/2 xl:w-1/3 mt-4",children:e.jsx(k,{offer:s,onFav:()=>m("offer",s)})},t))})]})]})]})]})}v.createRoot(document.querySelector("#favorite")).render(e.jsx(w,{children:e.jsx(S,{})}));
