import{c as A,n as L,H as st,h as R,e as S,q as Ne,_ as Se,Q as Ie,D as ct,g as Ve,m as Ge,a as w,C as V,i as dt,r as ut,B as mt,k as ft,M as gt,o as Ct,u as vt}from"./genStyleUtils-CI3YU7Yv.js";import{r as l,R as y}from"./PeopleSelectPopover-CDKo4AC-.js";import{u as bt,e as Xe,l as Ue,o as Oe,a as pt,v as Ke,n as ht,C as Qe,x as yt}from"./isVisible-Bd4H7hpW.js";import{u as $t}from"./index-DKvg8qd3.js";import{R as xt,a as St}from"./ExclamationCircleFilled-DGwIvyP4.js";import{p as Ee,u as de,R as Ot}from"./pickAttrs-D1C8emUZ.js";import{I as Et,B as we,c as Ze}from"./button-C2fNxKeA.js";import{i as wt,K as Pe,u as Pt,P as jt,a as Nt,C as It,z as Rt,b as Tt}from"./ContextIsolator-CVrwwDX4.js";import{i as Mt,g as ce}from"./zoom-BbRr1fkt.js";import{R as je}from"./CloseOutlined-CGWqYTdG.js";import{K as Re}from"./KeyCode-lh1qUinJ.js";import{m as Te}from"./extendsObject-78o_rR5W.js";import{u as Bt}from"./useCSSVarCls-DWPRWpfJ.js";import{g as zt}from"./index-DR0pJ98a.js";var Ht={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"info-circle",theme:"filled"},kt=function(t,n){return l.createElement(Et,A({},t,{ref:n,icon:Ht}))},Lt=l.forwardRef(kt);function Ft(){const[e,t]=l.useState([]),n=l.useCallback(o=>(t(a=>[].concat(L(a),[o])),()=>{t(a=>a.filter(s=>s!==o))}),[]);return[e,n]}const At=new Pe("antFadeIn",{"0%":{opacity:0},"100%":{opacity:1}}),Dt=new Pe("antFadeOut",{"0%":{opacity:1},"100%":{opacity:0}}),_t=(e,t=!1)=>{const{antCls:n}=e,o=`${n}-fade`,a=t?"&":"";return[wt(o,At,Dt,e.motionDurationMid,t),{[`
        ${a}${o}-enter,
        ${a}${o}-appear
      `]:{opacity:0,animationTimingFunction:"linear"},[`${a}${o}-leave`]:{animationTimingFunction:"linear"}}]};function be(e){return!!(e!=null&&e.then)}const Ye=e=>{const{type:t,children:n,prefixCls:o,buttonProps:a,close:s,autoFocus:d,emitEvent:i,isSilent:r,quitOnNullishReturnValue:u,actionFn:c}=e,m=l.useRef(!1),g=l.useRef(null),[f,v]=bt(!1),b=(...C)=>{s==null||s.apply(void 0,C)};l.useEffect(()=>{let C=null;return d&&(C=setTimeout(()=>{var h;(h=g.current)===null||h===void 0||h.focus({preventScroll:!0})})),()=>{C&&clearTimeout(C)}},[]);const p=C=>{be(C)&&(v(!0),C.then((...h)=>{v(!1,!0),b.apply(void 0,h),m.current=!1},h=>{if(v(!1,!0),m.current=!1,!(r!=null&&r()))return Promise.reject(h)}))},$=C=>{if(m.current)return;if(m.current=!0,!c){b();return}let h;if(i){if(h=c(C),u&&!be(h)){m.current=!1,b(C);return}}else if(c.length)h=c(s),m.current=!1;else if(h=c(),!be(h)){b();return}p(h)};return l.createElement(we,Object.assign({},Ze(t),{onClick:$,loading:f,prefixCls:o},a,{ref:g}),n)},le=y.createContext({}),{Provider:Je}=le,Me=()=>{const{autoFocusButton:e,cancelButtonProps:t,cancelTextLocale:n,isSilent:o,mergedOkCancel:a,rootPrefixCls:s,close:d,onCancel:i,onConfirm:r}=l.useContext(le);return a?y.createElement(Ye,{isSilent:o,actionFn:i,close:(...u)=>{d==null||d.apply(void 0,u),r==null||r(!1)},autoFocus:e==="cancel",buttonProps:t,prefixCls:`${s}-btn`},n):null},Be=()=>{const{autoFocusButton:e,close:t,isSilent:n,okButtonProps:o,rootPrefixCls:a,okTextLocale:s,okType:d,onConfirm:i,onOk:r}=l.useContext(le);return y.createElement(Ye,{isSilent:n,type:d||"primary",actionFn:r,close:(...u)=>{t==null||t.apply(void 0,u),i==null||i(!0)},autoFocus:e==="ok",buttonProps:o,prefixCls:`${a}-btn`},s)};var et=l.createContext({});function ze(e,t,n){var o=t;return!o&&n&&(o="".concat(e,"-").concat(n)),o}function He(e,t){var n=e["page".concat(t?"Y":"X","Offset")],o="scroll".concat(t?"Top":"Left");if(typeof n!="number"){var a=e.document;n=a.documentElement[o],typeof n!="number"&&(n=a.body[o])}return n}function Wt(e){var t=e.getBoundingClientRect(),n={left:t.left,top:t.top},o=e.ownerDocument,a=o.defaultView||o.parentWindow;return n.left+=He(a),n.top+=He(a,!0),n}const qt=l.memo(function(e){var t=e.children;return t},function(e,t){var n=t.shouldUpdate;return!n});var Vt={width:0,height:0,overflow:"hidden",outline:"none"},Gt={outline:"none"},Xt=y.forwardRef(function(e,t){var n=e.prefixCls,o=e.className,a=e.style,s=e.title,d=e.ariaId,i=e.footer,r=e.closable,u=e.closeIcon,c=e.onClose,m=e.children,g=e.bodyStyle,f=e.bodyProps,v=e.modalRender,b=e.onMouseDown,p=e.onMouseUp,$=e.holderRef,C=e.visible,h=e.forceRender,E=e.width,P=e.height,x=e.classNames,N=e.styles,D=y.useContext(et),_=D.panel,H=st($,_),T=l.useRef(),M=l.useRef();y.useImperativeHandle(t,function(){return{focus:function(){var z;(z=T.current)===null||z===void 0||z.focus({preventScroll:!0})},changeActive:function(z){var te=document,X=te.activeElement;z&&X===M.current?T.current.focus({preventScroll:!0}):!z&&X===T.current&&M.current.focus({preventScroll:!0})}}});var j={};E!==void 0&&(j.width=E),P!==void 0&&(j.height=P);var B=i?y.createElement("div",{className:S("".concat(n,"-footer"),x==null?void 0:x.footer),style:R({},N==null?void 0:N.footer)},i):null,k=s?y.createElement("div",{className:S("".concat(n,"-header"),x==null?void 0:x.header),style:R({},N==null?void 0:N.header)},y.createElement("div",{className:"".concat(n,"-title"),id:d},s)):null,I=l.useMemo(function(){return Ne(r)==="object"&&r!==null?r:r?{closeIcon:u??y.createElement("span",{className:"".concat(n,"-close-x")})}:{}},[r,u,n]),F=Ee(I,!0),W=Ne(r)==="object"&&r.disabled,ee=r?y.createElement("button",A({type:"button",onClick:c,"aria-label":"Close"},F,{className:"".concat(n,"-close"),disabled:W}),I.closeIcon):null,q=y.createElement("div",{className:S("".concat(n,"-content"),x==null?void 0:x.content),style:N==null?void 0:N.content},ee,k,y.createElement("div",A({className:S("".concat(n,"-body"),x==null?void 0:x.body),style:R(R({},g),N==null?void 0:N.body)},f),m),B);return y.createElement("div",{key:"dialog-element",role:"dialog","aria-labelledby":s?d:null,"aria-modal":"true",ref:H,style:R(R({},a),j),className:S(n,o),onMouseDown:b,onMouseUp:p},y.createElement("div",{ref:T,tabIndex:0,style:Gt},y.createElement(qt,{shouldUpdate:C||h},v?v(q):q)),y.createElement("div",{tabIndex:0,ref:M,style:Vt}))}),tt=l.forwardRef(function(e,t){var n=e.prefixCls,o=e.title,a=e.style,s=e.className,d=e.visible,i=e.forceRender,r=e.destroyOnClose,u=e.motionName,c=e.ariaId,m=e.onVisibleChanged,g=e.mousePosition,f=l.useRef(),v=l.useState(),b=Se(v,2),p=b[0],$=b[1],C={};p&&(C.transformOrigin=p);function h(){var E=Wt(f.current);$(g&&(g.x||g.y)?"".concat(g.x-E.left,"px ").concat(g.y-E.top,"px"):"")}return l.createElement(Xe,{visible:d,onVisibleChanged:m,onAppearPrepare:h,onEnterPrepare:h,forceRender:i,motionName:u,removeOnLeave:r,ref:f},function(E,P){var x=E.className,N=E.style;return l.createElement(Xt,A({},e,{ref:t,title:o,ariaId:c,prefixCls:n,holderRef:P,style:R(R(R({},N),a),C),className:S(s,x)}))})});tt.displayName="Content";var Ut=function(t){var n=t.prefixCls,o=t.style,a=t.visible,s=t.maskProps,d=t.motionName,i=t.className;return l.createElement(Xe,{key:"mask",visible:a,motionName:d,leavedClassName:"".concat(n,"-mask-hidden")},function(r,u){var c=r.className,m=r.style;return l.createElement("div",A({ref:u,style:R(R({},m),o),className:S("".concat(n,"-mask"),c,i)},s))})},Kt=function(t){var n=t.prefixCls,o=n===void 0?"rc-dialog":n,a=t.zIndex,s=t.visible,d=s===void 0?!1:s,i=t.keyboard,r=i===void 0?!0:i,u=t.focusTriggerAfterClose,c=u===void 0?!0:u,m=t.wrapStyle,g=t.wrapClassName,f=t.wrapProps,v=t.onClose,b=t.afterOpenChange,p=t.afterClose,$=t.transitionName,C=t.animation,h=t.closable,E=h===void 0?!0:h,P=t.mask,x=P===void 0?!0:P,N=t.maskTransitionName,D=t.maskAnimation,_=t.maskClosable,H=_===void 0?!0:_,T=t.maskStyle,M=t.maskProps,j=t.rootClassName,B=t.classNames,k=t.styles,I=l.useRef(),F=l.useRef(),W=l.useRef(),ee=l.useState(d),q=Se(ee,2),G=q[0],z=q[1],te=Pt();function X(){Ie(F.current,document.activeElement)||(I.current=document.activeElement)}function re(){if(!Ie(F.current,document.activeElement)){var O;(O=W.current)===null||O===void 0||O.focus()}}function fe(O){if(O)re();else{if(z(!1),x&&I.current&&c){try{I.current.focus({preventScroll:!0})}catch{}I.current=null}G&&(p==null||p())}b==null||b(O)}function ne(O){v==null||v(O)}var U=l.useRef(!1),oe=l.useRef(),ge=function(){clearTimeout(oe.current),U.current=!0},Ce=function(){oe.current=setTimeout(function(){U.current=!1})},ie=null;H&&(ie=function(Q){U.current?U.current=!1:F.current===Q.target&&ne(Q)});function K(O){if(r&&O.keyCode===Re.ESC){O.stopPropagation(),ne(O);return}d&&O.keyCode===Re.TAB&&W.current.changeActive(!O.shiftKey)}l.useEffect(function(){d&&(z(!0),X())},[d]),l.useEffect(function(){return function(){clearTimeout(oe.current)}},[]);var ve=R(R(R({zIndex:a},m),k==null?void 0:k.wrapper),{},{display:G?null:"none"});return l.createElement("div",A({className:S("".concat(o,"-root"),j)},Ee(t,{data:!0})),l.createElement(Ut,{prefixCls:o,visible:x&&d,motionName:ze(o,N,D),style:R(R({zIndex:a},T),k==null?void 0:k.mask),maskProps:M,className:B==null?void 0:B.mask}),l.createElement("div",A({tabIndex:-1,onKeyDown:K,className:S("".concat(o,"-wrap"),g,B==null?void 0:B.wrapper),ref:F,onClick:ie,style:ve},f),l.createElement(tt,A({},t,{onMouseDown:ge,onMouseUp:Ce,ref:W,closable:E,ariaId:te,prefixCls:o,visible:d&&G,onClose:ne,onVisibleChanged:fe,motionName:ze(o,$,C)}))))},nt=function(t){var n=t.visible,o=t.getContainer,a=t.forceRender,s=t.destroyOnClose,d=s===void 0?!1:s,i=t.afterClose,r=t.panelRef,u=l.useState(n),c=Se(u,2),m=c[0],g=c[1],f=l.useMemo(function(){return{panel:r}},[r]);return l.useEffect(function(){n&&g(!0)},[n]),!a&&d&&!m?null:l.createElement(et.Provider,{value:f},l.createElement(jt,{open:n||a||m,autoDestroy:!1,getContainer:o,autoLock:n||m},l.createElement(Kt,A({},t,{destroyOnClose:d,afterClose:function(){i==null||i(),g(!1)}}))))};nt.displayName="Dialog";function ke(e){if(!e)return;const{closable:t,closeIcon:n}=e;return{closable:t,closeIcon:n}}function Le(e){const{closable:t,closeIcon:n}=e||{};return y.useMemo(()=>{if(!t&&(t===!1||n===!1||n===null))return!1;if(t===void 0&&n===void 0)return null;let o={closeIcon:typeof n!="boolean"&&n!==null?n:void 0};return t&&typeof t=="object"&&(o=Object.assign(Object.assign({},o),t)),o},[t,n])}const Qt={};function Zt(e,t,n=Qt){const o=Le(e),a=Le(t),[s]=de("global",Ue.global),d=typeof o!="boolean"?!!(o!=null&&o.disabled):!1,i=y.useMemo(()=>Object.assign({closeIcon:y.createElement(je,null)},n),[n]),r=y.useMemo(()=>o===!1?!1:o?Te(i,a,o):a===!1?!1:a?Te(i,a):i.closable?i:!1,[o,a,i]);return y.useMemo(()=>{var u,c;if(r===!1)return[!1,null,d,{}];const{closeIconRender:m}=i,{closeIcon:g}=r;let f=g;const v=Ee(r,!0);return f!=null&&(m&&(f=m(g)),f=y.isValidElement(f)?y.cloneElement(f,Object.assign(Object.assign(Object.assign({},f.props),{"aria-label":(c=(u=f.props)===null||u===void 0?void 0:u["aria-label"])!==null&&c!==void 0?c:s.close}),v)):y.createElement("span",Object.assign({"aria-label":s.close},v),f)),[!0,f,d,v]},[r,i])}const Yt=()=>ct()&&window.document.documentElement,ue=e=>{const{prefixCls:t,className:n,style:o,size:a,shape:s}=e,d=S({[`${t}-lg`]:a==="large",[`${t}-sm`]:a==="small"}),i=S({[`${t}-circle`]:s==="circle",[`${t}-square`]:s==="square",[`${t}-round`]:s==="round"}),r=l.useMemo(()=>typeof a=="number"?{width:a,height:a,lineHeight:`${a}px`}:{},[a]);return l.createElement("span",{className:S(t,d,i,n),style:Object.assign(Object.assign({},r),o)})},Jt=new Pe("ant-skeleton-loading",{"0%":{backgroundPosition:"100% 50%"},"100%":{backgroundPosition:"0 50%"}}),me=e=>({height:e,lineHeight:w(e)}),Z=e=>Object.assign({width:e},me(e)),en=e=>({background:e.skeletonLoadingBackground,backgroundSize:"400% 100%",animationName:Jt,animationDuration:e.skeletonLoadingMotionDuration,animationTimingFunction:"ease",animationIterationCount:"infinite"}),pe=(e,t)=>Object.assign({width:t(e).mul(5).equal(),minWidth:t(e).mul(5).equal()},me(e)),tn=e=>{const{skeletonAvatarCls:t,gradientFromColor:n,controlHeight:o,controlHeightLG:a,controlHeightSM:s}=e;return{[t]:Object.assign({display:"inline-block",verticalAlign:"top",background:n},Z(o)),[`${t}${t}-circle`]:{borderRadius:"50%"},[`${t}${t}-lg`]:Object.assign({},Z(a)),[`${t}${t}-sm`]:Object.assign({},Z(s))}},nn=e=>{const{controlHeight:t,borderRadiusSM:n,skeletonInputCls:o,controlHeightLG:a,controlHeightSM:s,gradientFromColor:d,calc:i}=e;return{[o]:Object.assign({display:"inline-block",verticalAlign:"top",background:d,borderRadius:n},pe(t,i)),[`${o}-lg`]:Object.assign({},pe(a,i)),[`${o}-sm`]:Object.assign({},pe(s,i))}},Fe=e=>Object.assign({width:e},me(e)),on=e=>{const{skeletonImageCls:t,imageSizeBase:n,gradientFromColor:o,borderRadiusSM:a,calc:s}=e;return{[t]:Object.assign(Object.assign({display:"inline-flex",alignItems:"center",justifyContent:"center",verticalAlign:"middle",background:o,borderRadius:a},Fe(s(n).mul(2).equal())),{[`${t}-path`]:{fill:"#bfbfbf"},[`${t}-svg`]:Object.assign(Object.assign({},Fe(n)),{maxWidth:s(n).mul(4).equal(),maxHeight:s(n).mul(4).equal()}),[`${t}-svg${t}-svg-circle`]:{borderRadius:"50%"}}),[`${t}${t}-circle`]:{borderRadius:"50%"}}},he=(e,t,n)=>{const{skeletonButtonCls:o}=e;return{[`${n}${o}-circle`]:{width:t,minWidth:t,borderRadius:"50%"},[`${n}${o}-round`]:{borderRadius:t}}},ye=(e,t)=>Object.assign({width:t(e).mul(2).equal(),minWidth:t(e).mul(2).equal()},me(e)),an=e=>{const{borderRadiusSM:t,skeletonButtonCls:n,controlHeight:o,controlHeightLG:a,controlHeightSM:s,gradientFromColor:d,calc:i}=e;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({[n]:Object.assign({display:"inline-block",verticalAlign:"top",background:d,borderRadius:t,width:i(o).mul(2).equal(),minWidth:i(o).mul(2).equal()},ye(o,i))},he(e,o,n)),{[`${n}-lg`]:Object.assign({},ye(a,i))}),he(e,a,`${n}-lg`)),{[`${n}-sm`]:Object.assign({},ye(s,i))}),he(e,s,`${n}-sm`))},ln=e=>{const{componentCls:t,skeletonAvatarCls:n,skeletonTitleCls:o,skeletonParagraphCls:a,skeletonButtonCls:s,skeletonInputCls:d,skeletonImageCls:i,controlHeight:r,controlHeightLG:u,controlHeightSM:c,gradientFromColor:m,padding:g,marginSM:f,borderRadius:v,titleHeight:b,blockRadius:p,paragraphLiHeight:$,controlHeightXS:C,paragraphMarginTop:h}=e;return{[t]:{display:"table",width:"100%",[`${t}-header`]:{display:"table-cell",paddingInlineEnd:g,verticalAlign:"top",[n]:Object.assign({display:"inline-block",verticalAlign:"top",background:m},Z(r)),[`${n}-circle`]:{borderRadius:"50%"},[`${n}-lg`]:Object.assign({},Z(u)),[`${n}-sm`]:Object.assign({},Z(c))},[`${t}-content`]:{display:"table-cell",width:"100%",verticalAlign:"top",[o]:{width:"100%",height:b,background:m,borderRadius:p,[`+ ${a}`]:{marginBlockStart:c}},[a]:{padding:0,"> li":{width:"100%",height:$,listStyle:"none",background:m,borderRadius:p,"+ li":{marginBlockStart:C}}},[`${a}> li:last-child:not(:first-child):not(:nth-child(2))`]:{width:"61%"}},[`&-round ${t}-content`]:{[`${o}, ${a} > li`]:{borderRadius:v}}},[`${t}-with-avatar ${t}-content`]:{[o]:{marginBlockStart:f,[`+ ${a}`]:{marginBlockStart:h}}},[`${t}${t}-element`]:Object.assign(Object.assign(Object.assign(Object.assign({display:"inline-block",width:"auto"},an(e)),tn(e)),nn(e)),on(e)),[`${t}${t}-block`]:{width:"100%",[s]:{width:"100%"},[d]:{width:"100%"}},[`${t}${t}-active`]:{[`
        ${o},
        ${a} > li,
        ${n},
        ${s},
        ${d},
        ${i}
      `]:Object.assign({},en(e))}}},rn=e=>{const{colorFillContent:t,colorFill:n}=e,o=t,a=n;return{color:o,colorGradientEnd:a,gradientFromColor:o,gradientToColor:a,titleHeight:e.controlHeight/2,blockRadius:e.borderRadiusSM,paragraphMarginTop:e.marginLG+e.marginXXS,paragraphLiHeight:e.controlHeight/2}},Y=Ve("Skeleton",e=>{const{componentCls:t,calc:n}=e,o=Ge(e,{skeletonAvatarCls:`${t}-avatar`,skeletonTitleCls:`${t}-title`,skeletonParagraphCls:`${t}-paragraph`,skeletonButtonCls:`${t}-button`,skeletonInputCls:`${t}-input`,skeletonImageCls:`${t}-image`,imageSizeBase:n(e.controlHeight).mul(1.5).equal(),borderRadius:100,skeletonLoadingBackground:`linear-gradient(90deg, ${e.gradientFromColor} 25%, ${e.gradientToColor} 37%, ${e.gradientFromColor} 63%)`,skeletonLoadingMotionDuration:"1.4s"});return ln(o)},rn,{deprecatedTokens:[["color","gradientFromColor"],["colorGradientEnd","gradientToColor"]]}),sn=e=>{const{prefixCls:t,className:n,rootClassName:o,active:a,shape:s="circle",size:d="default"}=e,{getPrefixCls:i}=l.useContext(V),r=i("skeleton",t),[u,c,m]=Y(r),g=Oe(e,["prefixCls","className"]),f=S(r,`${r}-element`,{[`${r}-active`]:a},n,o,c,m);return u(l.createElement("div",{className:f},l.createElement(ue,Object.assign({prefixCls:`${r}-avatar`,shape:s,size:d},g))))},cn=e=>{const{prefixCls:t,className:n,rootClassName:o,active:a,block:s=!1,size:d="default"}=e,{getPrefixCls:i}=l.useContext(V),r=i("skeleton",t),[u,c,m]=Y(r),g=Oe(e,["prefixCls"]),f=S(r,`${r}-element`,{[`${r}-active`]:a,[`${r}-block`]:s},n,o,c,m);return u(l.createElement("div",{className:f},l.createElement(ue,Object.assign({prefixCls:`${r}-button`,size:d},g))))},dn="M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",un=e=>{const{prefixCls:t,className:n,rootClassName:o,style:a,active:s}=e,{getPrefixCls:d}=l.useContext(V),i=d("skeleton",t),[r,u,c]=Y(i),m=S(i,`${i}-element`,{[`${i}-active`]:s},n,o,u,c);return r(l.createElement("div",{className:m},l.createElement("div",{className:S(`${i}-image`,n),style:a},l.createElement("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",className:`${i}-image-svg`},l.createElement("title",null,"Image placeholder"),l.createElement("path",{d:dn,className:`${i}-image-path`})))))},mn=e=>{const{prefixCls:t,className:n,rootClassName:o,active:a,block:s,size:d="default"}=e,{getPrefixCls:i}=l.useContext(V),r=i("skeleton",t),[u,c,m]=Y(r),g=Oe(e,["prefixCls"]),f=S(r,`${r}-element`,{[`${r}-active`]:a,[`${r}-block`]:s},n,o,c,m);return u(l.createElement("div",{className:f},l.createElement(ue,Object.assign({prefixCls:`${r}-input`,size:d},g))))},fn=e=>{const{prefixCls:t,className:n,rootClassName:o,style:a,active:s,children:d}=e,{getPrefixCls:i}=l.useContext(V),r=i("skeleton",t),[u,c,m]=Y(r),g=S(r,`${r}-element`,{[`${r}-active`]:s},c,n,o,m);return u(l.createElement("div",{className:g},l.createElement("div",{className:S(`${r}-image`,n),style:a},d)))},gn=(e,t)=>{const{width:n,rows:o=2}=t;if(Array.isArray(n))return n[e];if(o-1===e)return n},Cn=e=>{const{prefixCls:t,className:n,style:o,rows:a=0}=e,s=Array.from({length:a}).map((d,i)=>l.createElement("li",{key:i,style:{width:gn(i,e)}}));return l.createElement("ul",{className:S(t,n),style:o},s)},vn=({prefixCls:e,className:t,width:n,style:o})=>l.createElement("h3",{className:S(e,t),style:Object.assign({width:n},o)});function $e(e){return e&&typeof e=="object"?e:{}}function bn(e,t){return e&&!t?{size:"large",shape:"square"}:{size:"large",shape:"circle"}}function pn(e,t){return!e&&t?{width:"38%"}:e&&t?{width:"50%"}:{}}function hn(e,t){const n={};return(!e||!t)&&(n.width="61%"),!e&&t?n.rows=3:n.rows=2,n}const J=e=>{const{prefixCls:t,loading:n,className:o,rootClassName:a,style:s,children:d,avatar:i=!1,title:r=!0,paragraph:u=!0,active:c,round:m}=e,{getPrefixCls:g,direction:f,className:v,style:b}=dt("skeleton"),p=g("skeleton",t),[$,C,h]=Y(p);if(n||!("loading"in e)){const E=!!i,P=!!r,x=!!u;let N;if(E){const H=Object.assign(Object.assign({prefixCls:`${p}-avatar`},bn(P,x)),$e(i));N=l.createElement("div",{className:`${p}-header`},l.createElement(ue,Object.assign({},H)))}let D;if(P||x){let H;if(P){const M=Object.assign(Object.assign({prefixCls:`${p}-title`},pn(E,x)),$e(r));H=l.createElement(vn,Object.assign({},M))}let T;if(x){const M=Object.assign(Object.assign({prefixCls:`${p}-paragraph`},hn(E,P)),$e(u));T=l.createElement(Cn,Object.assign({},M))}D=l.createElement("div",{className:`${p}-content`},H,T)}const _=S(p,{[`${p}-with-avatar`]:E,[`${p}-active`]:c,[`${p}-rtl`]:f==="rtl",[`${p}-round`]:m},v,o,a,C,h);return $(l.createElement("div",{className:_,style:Object.assign(Object.assign({},b),s)},N,D))}return d??null};J.Button=cn;J.Avatar=sn;J.Input=mn;J.Image=un;J.Node=fn;function Ae(){}const yn=l.createContext({add:Ae,remove:Ae});function $n(e){const t=l.useContext(yn),n=l.useRef(null);return pt(a=>{if(a){const s=e?a.querySelector(e):a;t.add(s),n.current=s}else t.remove(n.current)})}const De=()=>{const{cancelButtonProps:e,cancelTextLocale:t,onCancel:n}=l.useContext(le);return y.createElement(we,Object.assign({onClick:n},e),t)},_e=()=>{const{confirmLoading:e,okButtonProps:t,okType:n,okTextLocale:o,onOk:a}=l.useContext(le);return y.createElement(we,Object.assign({},Ze(n),{loading:e,onClick:a},t),o)};function xn(e,t){return y.createElement("span",{className:`${e}-close-x`},t||y.createElement(je,{className:`${e}-close-icon`}))}const Sn=e=>{const{okText:t,okType:n="primary",cancelText:o,confirmLoading:a,onOk:s,onCancel:d,okButtonProps:i,cancelButtonProps:r,footer:u}=e,[c]=de("Modal",Ke()),m=t||(c==null?void 0:c.okText),g=o||(c==null?void 0:c.cancelText),f={confirmLoading:a,okButtonProps:i,cancelButtonProps:r,okTextLocale:m,cancelTextLocale:g,okType:n,onOk:s,onCancel:d},v=y.useMemo(()=>f,L(Object.values(f)));let b;return typeof u=="function"||typeof u>"u"?(b=y.createElement(y.Fragment,null,y.createElement(De,null),y.createElement(_e,null)),typeof u=="function"&&(b=u(b,{OkBtn:_e,CancelBtn:De})),b=y.createElement(Je,{value:v},b)):b=u,y.createElement(ht,{disabled:!1},b)};function We(e){return{position:e,inset:0}}const On=e=>{const{componentCls:t,antCls:n}=e;return[{[`${t}-root`]:{[`${t}${n}-zoom-enter, ${t}${n}-zoom-appear`]:{transform:"none",opacity:0,animationDuration:e.motionDurationSlow,userSelect:"none"},[`${t}${n}-zoom-leave ${t}-content`]:{pointerEvents:"none"},[`${t}-mask`]:Object.assign(Object.assign({},We("fixed")),{zIndex:e.zIndexPopupBase,height:"100%",backgroundColor:e.colorBgMask,pointerEvents:"none",[`${t}-hidden`]:{display:"none"}}),[`${t}-wrap`]:Object.assign(Object.assign({},We("fixed")),{zIndex:e.zIndexPopupBase,overflow:"auto",outline:0,WebkitOverflowScrolling:"touch"})}},{[`${t}-root`]:_t(e)}]},En=e=>{const{componentCls:t}=e;return[{[`${t}-root`]:{[`${t}-wrap-rtl`]:{direction:"rtl"},[`${t}-centered`]:{textAlign:"center","&::before":{display:"inline-block",width:0,height:"100%",verticalAlign:"middle",content:'""'},[t]:{top:0,display:"inline-block",paddingBottom:0,textAlign:"start",verticalAlign:"middle"}},[`@media (max-width: ${e.screenSMMax}px)`]:{[t]:{maxWidth:"calc(100vw - 16px)",margin:`${w(e.marginXS)} auto`},[`${t}-centered`]:{[t]:{flex:1}}}}},{[t]:Object.assign(Object.assign({},ut(e)),{pointerEvents:"none",position:"relative",top:100,width:"auto",maxWidth:`calc(100vw - ${w(e.calc(e.margin).mul(2).equal())})`,margin:"0 auto",paddingBottom:e.paddingLG,[`${t}-title`]:{margin:0,color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.titleFontSize,lineHeight:e.titleLineHeight,wordWrap:"break-word"},[`${t}-content`]:{position:"relative",backgroundColor:e.contentBg,backgroundClip:"padding-box",border:0,borderRadius:e.borderRadiusLG,boxShadow:e.boxShadow,pointerEvents:"auto",padding:e.contentPadding},[`${t}-close`]:Object.assign({position:"absolute",top:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),insetInlineEnd:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),zIndex:e.calc(e.zIndexPopupBase).add(10).equal(),padding:0,color:e.modalCloseIconColor,fontWeight:e.fontWeightStrong,lineHeight:1,textDecoration:"none",background:"transparent",borderRadius:e.borderRadiusSM,width:e.modalCloseBtnSize,height:e.modalCloseBtnSize,border:0,outline:0,cursor:"pointer",transition:`color ${e.motionDurationMid}, background-color ${e.motionDurationMid}`,"&-x":{display:"flex",fontSize:e.fontSizeLG,fontStyle:"normal",lineHeight:w(e.modalCloseBtnSize),justifyContent:"center",textTransform:"none",textRendering:"auto"},"&:disabled":{pointerEvents:"none"},"&:hover":{color:e.modalCloseIconHoverColor,backgroundColor:e.colorBgTextHover,textDecoration:"none"},"&:active":{backgroundColor:e.colorBgTextActive}},mt(e)),[`${t}-header`]:{color:e.colorText,background:e.headerBg,borderRadius:`${w(e.borderRadiusLG)} ${w(e.borderRadiusLG)} 0 0`,marginBottom:e.headerMarginBottom,padding:e.headerPadding,borderBottom:e.headerBorderBottom},[`${t}-body`]:{fontSize:e.fontSize,lineHeight:e.lineHeight,wordWrap:"break-word",padding:e.bodyPadding,[`${t}-body-skeleton`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",margin:`${w(e.margin)} auto`}},[`${t}-footer`]:{textAlign:"end",background:e.footerBg,marginTop:e.footerMarginTop,padding:e.footerPadding,borderTop:e.footerBorderTop,borderRadius:e.footerBorderRadius,[`> ${e.antCls}-btn + ${e.antCls}-btn`]:{marginInlineStart:e.marginXS}},[`${t}-open`]:{overflow:"hidden"}})},{[`${t}-pure-panel`]:{top:"auto",padding:0,display:"flex",flexDirection:"column",[`${t}-content,
          ${t}-body,
          ${t}-confirm-body-wrapper`]:{display:"flex",flexDirection:"column",flex:"auto"},[`${t}-confirm-body`]:{marginBottom:"auto"}}}]},wn=e=>{const{componentCls:t}=e;return{[`${t}-root`]:{[`${t}-wrap-rtl`]:{direction:"rtl",[`${t}-confirm-body`]:{direction:"rtl"}}}}},Pn=e=>{const{componentCls:t}=e,n=zt(e),o=Object.assign({},n);delete o.xs;const a=`--${t.replace(".","")}-`,s=Object.keys(o).map(d=>({[`@media (min-width: ${w(o[d])})`]:{width:`var(${a}${d}-width)`}}));return{[`${t}-root`]:{[t]:[].concat(L(Object.keys(n).map((d,i)=>{const r=Object.keys(n)[i-1];return r?{[`${a}${d}-width`]:`var(${a}${r}-width)`}:null})),[{width:`var(${a}xs-width)`}],L(s))}}},ot=e=>{const t=e.padding,n=e.fontSizeHeading5,o=e.lineHeightHeading5;return Ge(e,{modalHeaderHeight:e.calc(e.calc(o).mul(n).equal()).add(e.calc(t).mul(2).equal()).equal(),modalFooterBorderColorSplit:e.colorSplit,modalFooterBorderStyle:e.lineType,modalFooterBorderWidth:e.lineWidth,modalCloseIconColor:e.colorIcon,modalCloseIconHoverColor:e.colorIconHover,modalCloseBtnSize:e.controlHeight,modalConfirmIconSize:e.fontHeight,modalTitleHeight:e.calc(e.titleFontSize).mul(e.titleLineHeight).equal()})},at=e=>({footerBg:"transparent",headerBg:e.colorBgElevated,titleLineHeight:e.lineHeightHeading5,titleFontSize:e.fontSizeHeading5,contentBg:e.colorBgElevated,titleColor:e.colorTextHeading,contentPadding:e.wireframe?0:`${w(e.paddingMD)} ${w(e.paddingContentHorizontalLG)}`,headerPadding:e.wireframe?`${w(e.padding)} ${w(e.paddingLG)}`:0,headerBorderBottom:e.wireframe?`${w(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",headerMarginBottom:e.wireframe?0:e.marginXS,bodyPadding:e.wireframe?e.paddingLG:0,footerPadding:e.wireframe?`${w(e.paddingXS)} ${w(e.padding)}`:0,footerBorderTop:e.wireframe?`${w(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",footerBorderRadius:e.wireframe?`0 0 ${w(e.borderRadiusLG)} ${w(e.borderRadiusLG)}`:0,footerMarginTop:e.wireframe?0:e.marginSM,confirmBodyPadding:e.wireframe?`${w(e.padding*2)} ${w(e.padding*2)} ${w(e.paddingLG)}`:0,confirmIconMarginInlineEnd:e.wireframe?e.margin:e.marginSM,confirmBtnsMarginTop:e.wireframe?e.marginLG:e.marginSM}),jn=Ve("Modal",e=>{const t=ot(e);return[En(t),wn(t),On(t),Mt(t,"zoom"),Pn(t)]},at,{unitless:{titleLineHeight:!0}});var Nn=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};let xe;const In=e=>{xe={x:e.pageX,y:e.pageY},setTimeout(()=>{xe=null},100)};Yt()&&document.documentElement.addEventListener("click",In,!0);const Rn=e=>{const{prefixCls:t,className:n,rootClassName:o,open:a,wrapClassName:s,centered:d,getContainer:i,focusTriggerAfterClose:r=!0,style:u,visible:c,width:m=520,footer:g,classNames:f,styles:v,children:b,loading:p,confirmLoading:$,zIndex:C,mousePosition:h,onOk:E,onCancel:P,destroyOnHidden:x,destroyOnClose:N,panelRef:D=null}=e,_=Nn(e,["prefixCls","className","rootClassName","open","wrapClassName","centered","getContainer","focusTriggerAfterClose","style","visible","width","footer","classNames","styles","children","loading","confirmLoading","zIndex","mousePosition","onOk","onCancel","destroyOnHidden","destroyOnClose","panelRef"]),{getPopupContainer:H,getPrefixCls:T,direction:M,modal:j}=l.useContext(V),B=O=>{$||P==null||P(O)},k=O=>{E==null||E(O)},I=T("modal",t),F=T(),W=Bt(I),[ee,q,G]=jn(I,W),z=S(s,{[`${I}-centered`]:d??(j==null?void 0:j.centered),[`${I}-wrap-rtl`]:M==="rtl"}),te=g!==null&&!p?l.createElement(Sn,Object.assign({},e,{onOk:k,onCancel:B})):null,[X,re,fe,ne]=Zt(ke(e),ke(j),{closable:!0,closeIcon:l.createElement(je,{className:`${I}-close-icon`}),closeIconRender:O=>xn(I,O)}),U=$n(`.${I}-content`),oe=ft(D,U),[ge,Ce]=Nt("Modal",C),[ie,K]=l.useMemo(()=>m&&typeof m=="object"?[void 0,m]:[m,void 0],[m]),ve=l.useMemo(()=>{const O={};return K&&Object.keys(K).forEach(Q=>{const se=K[Q];se!==void 0&&(O[`--${I}-${Q}-width`]=typeof se=="number"?`${se}px`:se)}),O},[K]);return ee(l.createElement(It,{form:!0,space:!0},l.createElement(Rt.Provider,{value:Ce},l.createElement(nt,Object.assign({width:ie},_,{zIndex:ge,getContainer:i===void 0?H:i,prefixCls:I,rootClassName:S(q,o,G,W),footer:te,visible:a??c,mousePosition:h??xe,onClose:B,closable:X&&Object.assign({disabled:fe,closeIcon:re},ne),closeIcon:re,focusTriggerAfterClose:r,transitionName:ce(F,"zoom",e.transitionName),maskTransitionName:ce(F,"fade",e.maskTransitionName),className:S(q,n,j==null?void 0:j.className),style:Object.assign(Object.assign(Object.assign({},j==null?void 0:j.style),u),ve),classNames:Object.assign(Object.assign(Object.assign({},j==null?void 0:j.classNames),f),{wrapper:S(z,f==null?void 0:f.wrapper)}),styles:Object.assign(Object.assign({},j==null?void 0:j.styles),v),panelRef:oe,destroyOnClose:x??N}),p?l.createElement(J,{active:!0,title:!1,paragraph:{rows:4},className:`${I}-body-skeleton`}):b))))},Tn=e=>{const{componentCls:t,titleFontSize:n,titleLineHeight:o,modalConfirmIconSize:a,fontSize:s,lineHeight:d,modalTitleHeight:i,fontHeight:r,confirmBodyPadding:u}=e,c=`${t}-confirm`;return{[c]:{"&-rtl":{direction:"rtl"},[`${e.antCls}-modal-header`]:{display:"none"},[`${c}-body-wrapper`]:Object.assign({},Ct()),[`&${t} ${t}-body`]:{padding:u},[`${c}-body`]:{display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${e.iconCls}`]:{flex:"none",fontSize:a,marginInlineEnd:e.confirmIconMarginInlineEnd,marginTop:e.calc(e.calc(r).sub(a).equal()).div(2).equal()},[`&-has-title > ${e.iconCls}`]:{marginTop:e.calc(e.calc(i).sub(a).equal()).div(2).equal()}},[`${c}-paragraph`]:{display:"flex",flexDirection:"column",flex:"auto",rowGap:e.marginXS,maxWidth:`calc(100% - ${w(e.marginSM)})`},[`${e.iconCls} + ${c}-paragraph`]:{maxWidth:`calc(100% - ${w(e.calc(e.modalConfirmIconSize).add(e.marginSM).equal())})`},[`${c}-title`]:{color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:n,lineHeight:o},[`${c}-content`]:{color:e.colorText,fontSize:s,lineHeight:d},[`${c}-btns`]:{textAlign:"end",marginTop:e.confirmBtnsMarginTop,[`${e.antCls}-btn + ${e.antCls}-btn`]:{marginBottom:0,marginInlineStart:e.marginXS}}},[`${c}-error ${c}-body > ${e.iconCls}`]:{color:e.colorError},[`${c}-warning ${c}-body > ${e.iconCls},
        ${c}-confirm ${c}-body > ${e.iconCls}`]:{color:e.colorWarning},[`${c}-info ${c}-body > ${e.iconCls}`]:{color:e.colorInfo},[`${c}-success ${c}-body > ${e.iconCls}`]:{color:e.colorSuccess}}},Mn=gt(["Modal","confirm"],e=>{const t=ot(e);return Tn(t)},at,{order:-1e3});var Bn=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};function zn(e){const{prefixCls:t,icon:n,okText:o,cancelText:a,confirmPrefixCls:s,type:d,okCancel:i,footer:r,locale:u}=e,c=Bn(e,["prefixCls","icon","okText","cancelText","confirmPrefixCls","type","okCancel","footer","locale"]);let m=n;if(!n&&n!==null)switch(d){case"info":m=l.createElement(Lt,null);break;case"success":m=l.createElement(St,null);break;case"error":m=l.createElement(Ot,null);break;default:m=l.createElement(xt,null)}const g=i??d==="confirm",f=e.autoFocusButton===null?!1:e.autoFocusButton||"ok",[v]=de("Modal"),b=u||v,p=o||(g?b==null?void 0:b.okText:b==null?void 0:b.justOkText),$=a||(b==null?void 0:b.cancelText),C=Object.assign({autoFocusButton:f,cancelTextLocale:$,okTextLocale:p,mergedOkCancel:g},c),h=l.useMemo(()=>C,L(Object.values(C))),E=l.createElement(l.Fragment,null,l.createElement(Me,null),l.createElement(Be,null)),P=e.title!==void 0&&e.title!==null,x=`${s}-body`;return l.createElement("div",{className:`${s}-body-wrapper`},l.createElement("div",{className:S(x,{[`${x}-has-title`]:P})},m,l.createElement("div",{className:`${s}-paragraph`},P&&l.createElement("span",{className:`${s}-title`},e.title),l.createElement("div",{className:`${s}-content`},e.content))),r===void 0||typeof r=="function"?l.createElement(Je,{value:h},l.createElement("div",{className:`${s}-btns`},typeof r=="function"?r(E,{OkBtn:Be,CancelBtn:Me}):E)):r,l.createElement(Mn,{prefixCls:t}))}const Hn=e=>{const{close:t,zIndex:n,maskStyle:o,direction:a,prefixCls:s,wrapClassName:d,rootPrefixCls:i,bodyStyle:r,closable:u=!1,onConfirm:c,styles:m}=e,g=`${s}-confirm`,f=e.width||416,v=e.style||{},b=e.mask===void 0?!0:e.mask,p=e.maskClosable===void 0?!1:e.maskClosable,$=S(g,`${g}-${e.type}`,{[`${g}-rtl`]:a==="rtl"},e.className),[,C]=vt(),h=l.useMemo(()=>n!==void 0?n:C.zIndexPopupBase+Tt,[n,C]);return l.createElement(Rn,Object.assign({},e,{className:$,wrapClassName:S({[`${g}-centered`]:!!e.centered},d),onCancel:()=>{t==null||t({triggerCancel:!0}),c==null||c(!1)},title:"",footer:null,transitionName:ce(i||"","zoom",e.transitionName),maskTransitionName:ce(i||"","fade",e.maskTransitionName),mask:b,maskClosable:p,style:v,styles:Object.assign({body:r,mask:o},m),width:f,zIndex:h,closable:u}),l.createElement(zn,Object.assign({},e,{confirmPrefixCls:g})))},lt=e=>{const{rootPrefixCls:t,iconPrefixCls:n,direction:o,theme:a}=e;return l.createElement(Qe,{prefixCls:t,iconPrefixCls:n,direction:o,theme:a},l.createElement(Hn,Object.assign({},e)))},ae=[];let rt="";function it(){return rt}const kn=e=>{var t,n;const{prefixCls:o,getContainer:a,direction:s}=e,d=Ke(),i=l.useContext(V),r=it()||i.getPrefixCls(),u=o||`${r}-modal`;let c=a;return c===!1&&(c=void 0),y.createElement(lt,Object.assign({},e,{rootPrefixCls:r,prefixCls:u,iconPrefixCls:i.iconPrefixCls,theme:i.theme,direction:s??i.direction,locale:(n=(t=i.locale)===null||t===void 0?void 0:t.Modal)!==null&&n!==void 0?n:d,getContainer:c}))};function io(e){const t=yt(),n=document.createDocumentFragment();let o=Object.assign(Object.assign({},e),{close:r,open:!0}),a,s;function d(...c){var m;if(c.some(v=>v==null?void 0:v.triggerCancel)){var f;(m=e.onCancel)===null||m===void 0||(f=m).call.apply(f,[e,()=>{}].concat(L(c.slice(1))))}for(let v=0;v<ae.length;v++)if(ae[v]===r){ae.splice(v,1);break}s()}function i(c){clearTimeout(a),a=setTimeout(()=>{const m=t.getPrefixCls(void 0,it()),g=t.getIconPrefixCls(),f=t.getTheme(),v=y.createElement(kn,Object.assign({},c));s=$t()(y.createElement(Qe,{prefixCls:m,iconPrefixCls:g,theme:f},t.holderRender?t.holderRender(v):v),n)})}function r(...c){o=Object.assign(Object.assign({},o),{open:!1,afterClose:()=>{typeof e.afterClose=="function"&&e.afterClose(),d.apply(this,c)}}),o.visible&&delete o.visible,i(o)}function u(c){typeof c=="function"?o=c(o):o=Object.assign(Object.assign({},o),c),i(o)}return i(o),ae.push(r),{destroy:r,update:u}}function Ln(e){return Object.assign(Object.assign({},e),{type:"warning"})}function Fn(e){return Object.assign(Object.assign({},e),{type:"info"})}function An(e){return Object.assign(Object.assign({},e),{type:"success"})}function Dn(e){return Object.assign(Object.assign({},e),{type:"error"})}function _n(e){return Object.assign(Object.assign({},e),{type:"confirm"})}function so({rootPrefixCls:e}){rt=e}var Wn=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};const qn=(e,t)=>{var n,{afterClose:o,config:a}=e,s=Wn(e,["afterClose","config"]);const[d,i]=l.useState(!0),[r,u]=l.useState(a),{direction:c,getPrefixCls:m}=l.useContext(V),g=m("modal"),f=m(),v=()=>{var C;o(),(C=r.afterClose)===null||C===void 0||C.call(r)},b=(...C)=>{var h;if(i(!1),C.some(x=>x==null?void 0:x.triggerCancel)){var P;(h=r.onCancel)===null||h===void 0||(P=h).call.apply(P,[r,()=>{}].concat(L(C.slice(1))))}};l.useImperativeHandle(t,()=>({destroy:b,update:C=>{u(h=>{const E=typeof C=="function"?C(h):C;return Object.assign(Object.assign({},h),E)})}}));const p=(n=r.okCancel)!==null&&n!==void 0?n:r.type==="confirm",[$]=de("Modal",Ue.Modal);return l.createElement(lt,Object.assign({prefixCls:g,rootPrefixCls:f},r,{close:b,open:d,afterClose:v,okText:r.okText||(p?$==null?void 0:$.okText:$==null?void 0:$.justOkText),direction:r.direction||c,cancelText:r.cancelText||($==null?void 0:$.cancelText)},s))},Vn=l.forwardRef(qn);let qe=0;const Gn=l.memo(l.forwardRef((e,t)=>{const[n,o]=Ft();return l.useImperativeHandle(t,()=>({patchElement:o}),[]),l.createElement(l.Fragment,null,n)}));function co(){const e=l.useRef(null),[t,n]=l.useState([]);l.useEffect(()=>{t.length&&(L(t).forEach(d=>{d()}),n([]))},[t]);const o=l.useCallback(s=>function(i){var r;qe+=1;const u=l.createRef();let c;const m=new Promise(p=>{c=p});let g=!1,f;const v=l.createElement(Vn,{key:`modal-${qe}`,config:s(i),ref:u,afterClose:()=>{f==null||f()},isSilent:()=>g,onConfirm:p=>{c(p)}});return f=(r=e.current)===null||r===void 0?void 0:r.patchElement(v),f&&ae.push(f),{destroy:()=>{function p(){var $;($=u.current)===null||$===void 0||$.destroy()}u.current?p():n($=>[].concat(L($),[p]))},update:p=>{function $(){var C;(C=u.current)===null||C===void 0||C.update(p)}u.current?$():n(C=>[].concat(L(C),[$]))},then:p=>(g=!0,m.then(p))}},[]);return[l.useMemo(()=>({info:o(Fn),success:o(An),error:o(Dn),warning:o(Ln),confirm:o(_n)}),[]),l.createElement(Gn,{key:"modal-holder",ref:e})]}export{zn as C,Sn as F,Rn as M,Xt as P,Lt as R,co as a,An as b,io as c,Dn as d,_n as e,ae as f,Ln as g,_t as i,so as m,xn as r,jn as u,Fn as w};
