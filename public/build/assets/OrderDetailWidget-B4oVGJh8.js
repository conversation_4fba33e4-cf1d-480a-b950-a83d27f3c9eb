import{j as s}from"./hotel-item-D_zvdyIk.js";import{r as o}from"./PeopleSelectPopover-CDKo4AC-.js";import{M as d}from"./index-DVwr87ys.js";import"./index-DxcGRMc6.js";import"./genStyleUtils-CI3YU7Yv.js";import"./isVisible-Bd4H7hpW.js";import"./index-C_TizibV.js";import"./index-DKvg8qd3.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./button-C2fNxKeA.js";import"./useSize-CbUlsqBW.js";import"./color-DKTup0-d.js";import"./pickAttrs-D1C8emUZ.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./zoom-BbRr1fkt.js";import"./CloseOutlined-CGWqYTdG.js";import"./KeyCode-lh1qUinJ.js";import"./extendsObject-78o_rR5W.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./index-DR0pJ98a.js";import"./PurePanel-zvE2p4pp.js";import"./useMergedState-BDSe6zqT.js";function k({className:c,title:i,hasUserInfo:t,hasOrderTitle:a}){const[l,e]=o.useState(!1);return s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:`order-room-detail-wrap ${c||""}`,children:[s.jsx("h2",{className:"font-20 font-semibold",children:i||"您的预订"}),a?s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"pb-6 pt-2 font-14 border-b border-[#c9c9c9]/50",children:[s.jsxs("p",{children:["订单状态：",s.jsx("span",{className:"text-primary",children:"已预约"})]}),s.jsx("p",{className:"mt-0.5",children:"订单号：GHA12348990"})]}),s.jsxs("div",{className:"flex flex-row items-center border-b border-[#c9c9c9]/50 py-6",children:[s.jsxs("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-16 mt-1",children:"长沙玛拉顿酒店"}),s.jsxs("div",{className:"flex flex-row items-start font-14 mt-1",children:[s.jsx("i",{className:"iconfont icon-Location text-[#bbb] text-[22px]"}),"中国湖南省长沙市芙蓉区蔡锷中路 1 号国金中心 T2 塔楼"]})]}),s.jsx("div",{className:"ml-2 w-24 h-14 md:w-36 md:h-20 rounded-lg border border-[#efefef] shadow-md flex items-center justify-center",children:s.jsx("img",{className:"max-w-[90%] max-h-[90%]",src:"https://cms.ghadiscovery.com/content/download/518/2348?version=38&inline=1",alt:""})})]})]}):s.jsxs("div",{className:"border-b border-[#c9c9c9]/50 py-6",children:[s.jsx("h3",{className:"font-18 mt-2",children:"Maqo"}),s.jsx("h4",{className:"font-16 mt-1",children:"长沙玛拉顿酒店"}),s.jsxs("div",{className:"flex flex-row items-start font-14 mt-1",children:[s.jsx("i",{className:"iconfont icon-Location text-[#bbb] text-[22px]"}),"中国湖南省长沙市芙蓉区蔡锷中路 1 号国金中心 T2 塔楼"]})]}),s.jsxs("div",{className:"py-6 border-b border-[#c9c9c9]/50",children:[s.jsxs("div",{className:"flex flex-row items-center justify-between",children:[s.jsx("h3",{className:"font-16 font-semibold",children:"日期"}),s.jsx("i",{className:"iconfont icon-edit cursor-pointer text-[20px]"})]}),s.jsxs("div",{className:"font-14 mt-1",children:[s.jsx("p",{children:"2023-02-26至2023-02-28， 2晚"}),s.jsx("p",{children:"成人 2， 儿童 1"})]})]}),s.jsxs("div",{className:"pt-6",children:[s.jsxs("div",{className:"flex flex-row items-center justify-between",children:[s.jsx("h3",{className:"font-16 font-semibold",children:"房间"}),s.jsx("i",{className:"iconfont icon-edit cursor-pointer text-[20px]"})]}),s.jsx("div",{className:"",children:new Array(3).fill(0).map((n,r)=>s.jsxs("div",{className:"mt-3 first:mt-1",children:[s.jsx("h4",{className:"font-15",children:"房间一："}),s.jsx("p",{className:"font-14 mt-1",children:"M1 豪华客房（特大床）"}),s.jsx("a",{onClick:()=>e(!0),className:"font-14 mt-1 text-primary underline",href:"javascript:;",children:"取消及税收政策"})]}))})]}),t&&s.jsx(s.Fragment,{children:s.jsxs("div",{className:"mt-6 pt-6 border-t border-[#c9c9c9]/50",children:[s.jsx("h3",{className:"font-16 font-semibold",children:"入住人信息"}),s.jsx("h4",{className:"font-16 mt-1 font-IvyMode-Reg",children:"Bejnd 李"}),s.jsx("p",{className:"font-14 mt-1",children:"SILVER 会员号：8870636037"}),s.jsx("p",{className:"font-14 mt-1",children:"邮箱：<EMAIL>"})]})})]}),s.jsx(d,{open:l,footer:null,width:700,closable:!1,destroyOnHidden:!0,transitionName:"ant-fade",rootClassName:"gha-antd-modal",children:s.jsxs("div",{className:"gha-antd-modal-wrapper",children:[s.jsx("div",{className:"close-icon",onClick:()=>e(!1),children:s.jsx("i",{className:"iconfont icon-Close"})}),s.jsxs("div",{className:"gha-antd-modal-content",children:[s.jsxs("div",{className:"px-8",children:[s.jsx("h2",{className:"font-20 text-center mt-1 font-bold",children:"取消及税收政策"}),s.jsx("div",{className:"",children:new Array(2).fill(0).map((n,r)=>s.jsxs("div",{className:"py-6 border-b border-[#919191]/20 font-14 last:border-b-0",children:[s.jsxs("div",{className:"",children:[s.jsx("p",{className:"font-13 mb-0.5",children:"房间1"}),s.jsx("h4",{className:"font-18 font-IvyMode-Reg",children:"M2尊贵客房（大床）"})]}),s.jsxs("div",{className:"mt-2",children:[s.jsx("p",{className:"font-13 mb-0.5",children:"房价"}),s.jsx("p",{children:"会员特惠价"})]}),s.jsxs("div",{className:"mt-2",children:[s.jsx("p",{className:"font-13 mb-0.5",children:"取消"}),s.jsx("p",{children:"不迟于抵达前24小时取消，否则将收取一晚的取消费用"})]}),s.jsxs("div",{className:"mt-2",children:[s.jsx("p",{className:"font-13 mb-0.5",children:"存款"}),s.jsx("p",{children:"每次预订都需要信用卡"})]})]},r))})]}),s.jsx("div",{className:"pt-6 border-t border-[#919191]/20",children:s.jsxs("div",{className:"px-8",children:[s.jsx("h4",{className:"font-bold font-16",children:"税金"}),s.jsx("p",{className:"mt-1",children:"10%服务费-953元人民币"}),s.jsx("p",{className:"mt-1",children:"增值税-153元人民币"})]})})]})]})})]})}export{k as O};
