import{j as o}from"./hotel-item-D_zvdyIk.js";import{c as b}from"./client-CHYie6Ha.js";import{r as S}from"./PeopleSelectPopover-CDKo4AC-.js";import{T as M}from"./TopFilter-DBPCY3jr.js";import{u as i,T}from"./TagFilter-CavzMCRg.js";import{F as w}from"./FilterState-Dh2pxose.js";import{H}from"./HotelList-CNYvbJs6.js";import{H as Y}from"./HotelMap-Dg4Q1rDu.js";import{M as D}from"./MobileFilterBar-DAdc0RxH.js";import{u as g}from"./GhaSearchBar-CE-42mVw.js";import{a as n,$}from"./helper-D414uohx.js";import{u as k}from"./GhaConfigProvider-BB5IW5PM.js";import"./index-C_TizibV.js";import"./mock-OVdH_lkV.js";import"./react-ZNplgrTR.js";import"./index-PBsuAvuu.js";import"./genStyleUtils-CI3YU7Yv.js";import"./useMergedState-BDSe6zqT.js";import"./isVisible-Bd4H7hpW.js";import"./KeyCode-lh1qUinJ.js";import"./zoom-BbRr1fkt.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./color-DKTup0-d.js";import"./useSize-CbUlsqBW.js";import"./index-CJQq4WAU.js";import"./index-CaUFHQr4.js";import"./roundedArrow-DVJD-5zd.js";import"./OfferItem-RRpQgDme.js";import"./HotelMapBar-CGEY0e3y.js";import"./_map_helper-CAWV87xN.js";import"./MobileFilterModal-DMSnbK7_.js";import"./index-DVwr87ys.js";import"./index-DxcGRMc6.js";import"./index-DKvg8qd3.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./button-C2fNxKeA.js";import"./pickAttrs-D1C8emUZ.js";import"./CloseOutlined-CGWqYTdG.js";import"./extendsObject-78o_rR5W.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./index-DR0pJ98a.js";import"./PurePanel-zvE2p4pp.js";import"./RightOutlined-DZyqW5jV.js";import"./collapse-BbEVqHco.js";import"./index-CosgXXok.js";import"./Overflow-YL9RZFGj.js";import"./index-wg7qNA5H.js";import"./useIcons-BIWMCRV_.js";import"./index-BBp8sG_H.js";import"./zh-cn-DO3l3KOs.js";import"./index-DNIuBGmG.js";import"./Input-DCb0FIZx.js";import"./index-Dq7h7Pqt.js";function F(){const f=S.useRef(null),d=i(r=>r.showType),m=i(r=>r.slug),u=i(r=>r.list),j=k();S.useEffect(()=>{c()},[]);function _(r,e){let s=m==="hotels"?"hotel":"offer";n.collectItem({type:s,id:r}).subscribe(a=>{if(a.status_code!==200){j.error(a.msg);return}const t=[...u],l=t.find(y=>`${y.id}`==`${r}`);l.is_collect=!l.is_collect,i.setState({list:t}),e(l.is_collect,t)})}function p(r){if(r.type==="search"){const e=g.getState(),s={keyword:e.keyword,rooms:JSON.stringify(e.rooms),date:e.date.map(a=>a.format("YYYY-MM-DD"))};location.href=`/search/${m}#/?${$.encodeSearchHash(s)}`}c()}function x(r){i.setState({...r}),c()}function h(){const r=g.getState(),e=i.getState();console.error("searchStoreState",e);const s=e.selectTags.filter(t=>t.indexOf("_all")<0),a={keyword:r.keyword,rooms:JSON.stringify(r.rooms),date:r.date.map(t=>t.format("YYYY-MM-DD")),order:e.order,brands:s.filter(t=>t.startsWith("brands_")).map(t=>t.split("_")[1]).join("_"),categories:s.filter(t=>t.startsWith("categories_")).map(t=>t.split("_")[1]).join("_"),facts:s.filter(t=>t.startsWith("facts_")).map(t=>t.split("_")[1]).join("_"),series:s.filter(t=>t.startsWith("series_")).map(t=>t.split("_")[1]).join("_")};return r.offerType&&r.offerType!=="all"&&(a.offer_type=r.offerType),a}function c(){i.setState({loading:!0}),rxjs.of(null).pipe(rxjs.concatMap(()=>m==="offers"?n.searchOffers(h()):n.searchHotels(h()))).subscribe(r=>{var e;i.setState({loading:!1,list:r.data.data}),(e=f.current)==null||e.updateMapData(r.data.data)})}return o.jsxs(o.Fragment,{children:[o.jsx(M,{onCondChange:p}),o.jsxs("div",{className:"hidden lg:block relative",children:[o.jsx(T,{onCondChange:p}),o.jsx(w,{onCondChange:p})]}),o.jsx("div",{className:"lg:hidden relative",children:o.jsx(D,{onSubmit:x})}),d==="list"&&o.jsx(H,{}),d==="map"&&o.jsx(Y,{ref:f,dataSource:u,onHotelLike:_})]})}b.createRoot(document.querySelector(".hotel-list-page-wrap")).render(o.jsx(F,{}));
