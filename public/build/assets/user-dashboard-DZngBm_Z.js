import{j as e}from"./hotel-item-D_zvdyIk.js";import{c as o}from"./client-CHYie6Ha.js";import{r as m}from"./PeopleSelectPopover-CDKo4AC-.js";import{P as s}from"./panel-level-CLxtGU0U.js";import{P as l}from"./panel-donate-CPLjdKiD.js";import{P as n}from"./panel-detail-DzBwfafC.js";import{G as p}from"./GhaConfigProvider-BB5IW5PM.js";import{T as c}from"./index-CMZw1aDs.js";import"./index-C_TizibV.js";import"./genStyleUtils-CI3YU7Yv.js";import"./useSize-CbUlsqBW.js";import"./button-C2fNxKeA.js";import"./isVisible-Bd4H7hpW.js";import"./index-DKvg8qd3.js";import"./color-DKTup0-d.js";import"./index-CaUFHQr4.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./pickAttrs-D1C8emUZ.js";import"./index-BBp8sG_H.js";import"./useMergedState-BDSe6zqT.js";import"./KeyCode-lh1qUinJ.js";import"./Overflow-YL9RZFGj.js";import"./zoom-BbRr1fkt.js";import"./PurePanel-zvE2p4pp.js";import"./index-wg7qNA5H.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./useIcons-BIWMCRV_.js";import"./CloseOutlined-CGWqYTdG.js";import"./index-DoO4tVvP.js";import"./RightOutlined-DZyqW5jV.js";import"./EllipsisOutlined-cRWH5_fX.js";import"./index-CJQq4WAU.js";import"./roundedArrow-DVJD-5zd.js";import"./collapse-BbEVqHco.js";import"./index-Bkcwbepo.js";import"./useForm-DF1XNZWJ.js";import"./useBreakpoint-D48_IWaH.js";import"./extendsObject-78o_rR5W.js";import"./Input-DCb0FIZx.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./index-DxcGRMc6.js";import"./index-DR0pJ98a.js";function d(){const[t,i]=m.useState("level"),a=[{key:"level",label:"我的礼遇",children:e.jsx(s,{})},{key:"donate",label:"捐赠奖励金",children:e.jsx(l,{})},{key:"detail",label:"奖励金明细",children:e.jsx(n,{})}];return e.jsxs("div",{className:"user-dashboard-wrap",children:[e.jsxs("div",{className:"lg:hidden px-5 py-4 shadow-[0px_6px_6px_0px_rgba(0,0,0,0.05)] rounded-xl text-center font-14 bg-white",children:[e.jsx("p",{className:"",children:"欢迎你"}),e.jsx("h2",{className:"font-18 font-Jost-SemiBold font-bold mt-[5px]",children:window.__Server_Data__.gha_user.name}),e.jsxs("p",{className:"mt-[5px]",children:[e.jsx("span",{className:"text-[#737373]",children:"会员卡号："}),window.__Server_Data__.gha_user.member_balance.membership_card_no]}),e.jsxs("p",{className:"mt-[5px]",children:[e.jsx("span",{className:"text-[#737373]",children:"会员等级："}),window.__Server_Data__.user_card.title,"会员"]}),e.jsx("div",{className:"text-center mt-3",children:e.jsx("div",{className:"gha-primary-btn !py-1.5 !px-8 logout-btn-trigger",children:"退出账号"})}),e.jsx("div",{className:"w-full h-px bg-[#919191]/20 my-3"}),e.jsx("p",{children:"您的奖励金"}),e.jsxs("h2",{className:"font-36 font-Jost-SemiBold font-bold",children:["D$",window.__Server_Data__.gha_user.member_balance.gravty_discovery_balance]})]}),Math.random()>1&&e.jsx("div",{className:"flex flex-row items-center lg:hidden -mx-1.5 mt-6 min-w-0",children:a.map(r=>e.jsx("div",{className:"flex-1 mx-1.5",children:e.jsx("div",{onClick:()=>i(r.key),className:`${t===r.key?"gha-primary-btn":"gha-btn"} !py-1.5 inline-block w-full`,children:r.label},r.key)}))}),e.jsx("div",{className:"mt-6 lg:mt-6",children:e.jsx(c,{items:a,onChange:i,activeKey:t})})]})}o.createRoot(document.querySelector("#dashboard")).render(e.jsx(p,{children:e.jsx(d,{})}));
