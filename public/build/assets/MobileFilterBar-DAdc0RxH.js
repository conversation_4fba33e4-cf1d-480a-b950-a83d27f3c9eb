import{j as t}from"./hotel-item-D_zvdyIk.js";import{r as i,R as n}from"./PeopleSelectPopover-CDKo4AC-.js";import{M as p}from"./MobileFilterModal-DMSnbK7_.js";import{u as l}from"./TagFilter-CavzMCRg.js";import"./mock-OVdH_lkV.js";import"./FilterState-Dh2pxose.js";import"./index-PBsuAvuu.js";import"./genStyleUtils-CI3YU7Yv.js";import"./useMergedState-BDSe6zqT.js";import"./isVisible-Bd4H7hpW.js";import"./index-C_TizibV.js";import"./KeyCode-lh1qUinJ.js";import"./zoom-BbRr1fkt.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./color-DKTup0-d.js";import"./useSize-CbUlsqBW.js";import"./index-CJQq4WAU.js";import"./index-CaUFHQr4.js";import"./roundedArrow-DVJD-5zd.js";import"./helper-D414uohx.js";import"./index-Dq7h7Pqt.js";import"./react-ZNplgrTR.js";import"./index-DVwr87ys.js";import"./index-DxcGRMc6.js";import"./index-DKvg8qd3.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./button-C2fNxKeA.js";import"./pickAttrs-D1C8emUZ.js";import"./CloseOutlined-CGWqYTdG.js";import"./extendsObject-78o_rR5W.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./index-DR0pJ98a.js";import"./PurePanel-zvE2p4pp.js";import"./RightOutlined-DZyqW5jV.js";import"./collapse-BbEVqHco.js";function Q({onSubmit:s}){const e=i.useRef(null),r=i.useRef(null),[m,o]=n.useState(!1);return i.useEffect(()=>{window.addEventListener("scroll",()=>{!r.current||!e.current||(window.innerWidth<1024&&r.current.getBoundingClientRect().top<146?e.current.classList.add("gha-fixed"):e.current.classList.remove("gha-fixed"))})},[]),t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"mobile-filter-bar",children:t.jsx("div",{className:"g-main-content",children:t.jsx("div",{ref:r,className:"h-[56px]",children:t.jsxs("div",{ref:e,className:"flex flex-row items-center justify-between h-[56px] font-14",children:[t.jsxs("p",{onClick:()=>o(!0),className:"flex flex-row items-center cursor-pointer",children:["筛选与排序 ",t.jsx("i",{className:"iconfont icon-filter ml-1 text-lg"})]}),t.jsxs("p",{onClick:()=>l.setState({showType:"map"}),className:"flex flex-row items-center cursor-pointer",children:["显示地图 ",t.jsx("i",{className:"iconfont icon-Map ml-1 text-2xl"})]})]})})})}),t.jsx(p,{modalOpend:m,setModalOpend:o,onSubmit:s})]})}export{Q as M};
