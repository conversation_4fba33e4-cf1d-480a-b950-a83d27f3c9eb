import"./mock-hotel-CziwfxDC.js";import{$ as l}from"./helper-D414uohx.js";import{$ as o}from"./_map_helper-CAWV87xN.js";import"./index-Dq7h7Pqt.js";$(function(){new p});class p{constructor(){this.bootstrap(),this.mapBuilded=!1}bootstrap(){this.initBrandDetailSwiper(".brands-hotel-block"),this.initBrandDetailSwiper(".brands-discount-block"),this.initBrandDetailSwiper(".hotel-activity-block"),this.initFacilityMore(),this.initThumbSwiper(),this.initHotelRoomItemSwiper(),this.initRecommendedHotelSwiper(),this.initFixedNavBar(),this.initNavScrollActive(),this.initModalMap()}initModalThumbGlary(){$(".gha-modal-wrap.thumbs .swiper-slide img").each(function(){$(this).attr("src",$(this).attr("data-src"))}),$(".glary-item").each(function(e){new Swiper(`.thumbs-swiper-m.thumbs-swiper-${e} .swiper-container`,{slidesPerView:"auto",spaceBetween:10,navigation:{nextEl:`.thumbs-swiper-m.thumbs-swiper-${e} .gha-swiper-button-next`,prevEl:`.thumbs-swiper-m.thumbs-swiper-${e} .gha-swiper-button-prev`},on:{slideChange:function(){$(`.thumbs-swiper-m.thumbs-swiper-${e} .page-count span`).text(this.activeIndex+1)}}});let i=new Swiper(`.thumbs-swiper-top.thumbs-swiper-${e} .swiper-container`,{navigation:{nextEl:`.thumbs-swiper-top.thumbs-swiper-${e} .gha-swiper-button-next`,prevEl:`.thumbs-swiper-top.thumbs-swiper-${e} .gha-swiper-button-prev`},on:{slideChange:function(){$(`.thumbs-swiper-top.thumbs-swiper-${e} .page-count span`).text(this.activeIndex+1),t.slideTo(this.activeIndex),$(`.thumbs-swiper-bottom.thumbs-swiper-${e} .swiper-slide`).removeClass("active"),t.slides[this.activeIndex].classList.add("active")}}}),t=new Swiper(`.thumbs-swiper-bottom.thumbs-swiper-${e} .swiper-container`,{slidesPerView:"auto",spaceBetween:8,freeMode:!0,slidesOffsetBefore:32,slidesOffsetAfter:32});$("body").on("click",`.thumbs-swiper-bottom.thumbs-swiper-${e} .swiper-container .swiper-slide`,function(){const a=$(this).index();i.slideTo(a)})}),$("body").on("click",".glary-item",function(){$(".glary-item").removeClass("active"),$(this).addClass("active");const e=$(this).index();console.error("idx",e),$(".thumbs-swiper-m").addClass("hidden"),$(".thumbs-swiper-top").addClass("hidden"),$(".thumbs-swiper-bottom").addClass("hidden"),$(`.thumbs-swiper-m.thumbs-swiper-${e}`).removeClass("hidden"),$(`.thumbs-swiper-top.thumbs-swiper-${e}`).removeClass("hidden"),$(`.thumbs-swiper-bottom.thumbs-swiper-${e}`).removeClass("hidden")})}initModalMap(){l.getGlobalSubject().on("updateItemFav",function(i){window.$hotel.is_collect=i.data.nextState;let t=$(`.trigger-fav-el[data-type="${i.data.type}"][data-id="${i.data.id}"]`);t.find("i").removeClass("icon-Heart").removeClass("icon-Heart-filled"),t.find("i").addClass(i.data.nextState?"icon-Heart-filled":"icon-Heart")});const e=this;$("body").on("click",".gha-modal-wrap .close",function(){$(".gha-modal-wrap").addClass("hidden"),$("body").removeClass("overflow-hidden")}),$("body").on("click","#toggleMapBtn",function(){$(".gha-modal-wrap.map").removeClass("hidden"),$("body").addClass("overflow-hidden"),e.buildMap()}),$("body").on("click","#toggleThumbsBtn",function(){$(".gha-modal-wrap.thumbs").removeClass("hidden"),$("body").addClass("overflow-hidden"),e.initModalThumbGlary()})}buildMap(){if(this.mapBuilded)return;this.mapBuilded=!0;let e=window.$hotel;mapboxgl.accessToken="pk.eyJ1IjoiaWlpc2xlZSIsImEiOiJjbHJoN2Z3djMwbjY0MmptampmODRlcWdvIn0.yak7m5pJUycZ58aJUst7ag";const i=new mapboxgl.Map({container:"modalMap",style:"mapbox://styles/mapbox/streets-v12",zoom:10,center:[e.longitude,e.latitude]});i.addControl(new MapboxLanguage({defaultLanguage:"zh-Hans"})),i.on("load",function(){$(".gha-mapbox").removeClass("loading"),o.fitBoundsMap(i,[[e.longitude,e.latitude]]);let t=e,a;a=document.createElement("div"),a.className="gha-mapbox-marker-invalid",$(a).append('<i class="iconfont icon-Union"></i>');const n=new mapboxgl.Marker(a).setLngLat([t.longitude,t.latitude]).addTo(i);n.getElement().addEventListener("click",function(){o.flyMarkerToCenter(i,[t.longitude,t.latitude]);const s=o.getPopup({hotelData:e,slug:"hotels",autoTriggerFav:!0});n.setPopup(s),n.togglePopup(),o.initPopupSwiper(e)})})}initFixedNavBar(){const e=".fixed-nav-bar";new ResizeObserver(s=>{for(let r of s){const d=r.contentRect.height;$(e).find(".fixed-holder")[0]&&($(e).find(".fixed-holder")[0].style.height=`${d}px`)}}).observe(document.querySelector(`${e} .bar-el`));const t=$(e).find(".fixed-sentinel-top")[0],a=$(e)[0];if(!a||!t)return;function n(){const s=$(window).width()>=1024;t.getBoundingClientRect().top<(s?168:146)?a.classList.add("gha-fixed"):a.classList.remove("gha-fixed")}window.addEventListener("scroll",n,!0),n()}initNavScrollActive(){function e(t){$(".brand-desc-tab-item").eq(t).find("a").hasClass("active")||($(".brand-desc-tab-item").find("a").removeClass("active"),$(".brand-desc-tab-item").eq(t).find("a").addClass("active"))}function i(){let a=new Array($(".brand-desc-tab-item").length).fill(0).map((n,s)=>document.querySelector(`#anchor${s}`).getBoundingClientRect().top).findIndex(n=>n>10);if(a===-1){e($(".brand-desc-tab-item").length-1);return}if([0,1].includes(a)){e(0);return}e(a-1)}window.addEventListener("scroll",i),i()}initRecommendedHotelSwiper(){new Swiper(".recommend-swiper .swiper-container",{slidesPerView:1.1,spaceBetween:24,breakpoints:{768:{slidesPerView:1}},navigation:{nextEl:".recommend-swiper .gha-swiper-button-next",prevEl:".recommend-swiper .gha-swiper-button-prev"},pagination:{el:".recommend-swiper .swiper-pagination",clickable:!0}})}initHotelRoomItemSwiper(){const e=".hotel-room";$(e).each(function(){const i=$(this).data("id");new Swiper(`${e}[data-id='${i}'] .cover-swiper .swiper-container`,{navigation:{nextEl:`${e}[data-id='${i}'] .cover-swiper .gha-swiper-button-next`,prevEl:`${e}[data-id='${i}'] .cover-swiper .gha-swiper-button-prev`},pagination:{el:`${e}[data-id='${i}'] .cover-swiper .swiper-pagination`,clickable:!0},on:{slideChange:function(){$(`${e}[data-id='${i}'] .cover-swiper .swiper-pagination-nums span.cur`).text(this.activeIndex+1)}}})})}initFacilityMore(){$("body").on("click",".facility-block .facility-toggle-btn",function(){$(".facility-block").toggleClass("show-more")})}initThumbSwiper(){new Swiper(".thumb-swiper .swiper-container",{navigation:{nextEl:".thumb-swiper .gha-swiper-button-next",prevEl:".thumb-swiper .gha-swiper-button-prev"},pagination:{el:".thumb-swiper .swiper-pagination",clickable:!0}})}initBrandDetailSwiper(e){new Swiper(`${e} .gha-swiper >.swiper-container`,{slidesPerView:1.1,spaceBetween:24,slidesPerGroup:1,breakpoints:{768:{slidesPerView:2},1024:{slidesPerView:3}},on:{slideChange:function(){i(this)},init:function(){i(this)}},navigation:{nextEl:`${e} .gha-swiper >.gha-swiper-button-next`,prevEl:`${e} .gha-swiper >.gha-swiper-button-prev`},pagination:{el:`${e} .gha-swiper >.swiper-pagination`,clickable:!0}});function i(t){$(`${e} .swiper-cur-idx`).text(t.activeIndex+1);let a=3;window.innerWidth<1024&&(a=2),window.innerWidth<768&&(a=1),t.slides.forEach((s,r)=>{s.classList.remove("transparent-slide-30"),s.classList.remove("transparent-slide-0"),r>t.activeIndex+a&&s.classList.add("transparent-slide-0"),r===t.activeIndex+a&&s.classList.add("transparent-slide-30"),r<t.activeIndex-1&&s.classList.add("transparent-slide-0"),r===t.activeIndex-1&&s.classList.add("transparent-slide-30")});const n=t.activeIndex+3;n<t.slides.length&&t.slides[n].classList.add("transparent-slide-30")}}}
