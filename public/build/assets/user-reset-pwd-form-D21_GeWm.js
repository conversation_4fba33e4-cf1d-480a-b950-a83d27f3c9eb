import{j as r}from"./hotel-item-D_zvdyIk.js";import{c as u}from"./client-CHYie6Ha.js";import{r as p}from"./PeopleSelectPopover-CDKo4AC-.js";import{G as f,u as w}from"./GhaConfigProvider-BB5IW5PM.js";import{$ as g}from"./_constants-CI6xcXYb.js";import{$ as x,a as h}from"./helper-D414uohx.js";import{F as e}from"./index-CC5HyPSW.js";import{I as t}from"./index-DNIuBGmG.js";import{B as j}from"./button-C2fNxKeA.js";import"./index-C_TizibV.js";import"./isVisible-Bd4H7hpW.js";import"./genStyleUtils-CI3YU7Yv.js";import"./CloseOutlined-CGWqYTdG.js";import"./KeyCode-lh1qUinJ.js";import"./pickAttrs-D1C8emUZ.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./index-DxcGRMc6.js";import"./index-DKvg8qd3.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./color-DKTup0-d.js";import"./useSize-CbUlsqBW.js";import"./zoom-BbRr1fkt.js";import"./extendsObject-78o_rR5W.js";import"./index-DR0pJ98a.js";import"./index-Dq7h7Pqt.js";import"./collapse-BbEVqHco.js";import"./useForm-DF1XNZWJ.js";import"./row-BiveCJJi.js";import"./useBreakpoint-D48_IWaH.js";import"./index-CJQq4WAU.js";import"./index-CaUFHQr4.js";import"./useMergedState-BDSe6zqT.js";import"./roundedArrow-DVJD-5zd.js";import"./index-wg7qNA5H.js";import"./Input-DCb0FIZx.js";function b(){const[a]=e.useForm(),[n,i]=p.useState(!1),{disabled:v,setDisabled:_}=p.useState(!1),m=w();function d(o){i(!0),h.userUpPassword(o).subscribe(s=>{if(i(!1),console.error("rrrr",s),s.status_code!==200){m.error(s.message);return}m.success("修改成功"),a.resetFields()})}function c(){}return r.jsx("div",{className:"p-10",children:r.jsxs(e,{layout:"vertical",form:a,requiredMark:!1,validateTrigger:"onBlur",onFinish:d,className:"gha-form",onValuesChange:c,initialValues:x.isLaravelLocal()?{new_password:"123456Abc$",old_password:"123456Abcd$",confirm_password:"123456Abc$"}:{},children:[r.jsxs("div",{className:"lg:w-[360px]",children:[r.jsx(e.Item,{label:"旧密码*",name:"old_password",className:"flex-1 lg:mr-5",rules:[{required:!0,message:"请输入您的旧密码"}],children:r.jsx(t.Password,{variant:"underlined",placeholder:"请输入您的旧密码"})}),r.jsx(e.Item,{label:"新密码*",name:"new_password",className:"flex-1 lg:mr-5",rules:[{required:!0,message:"请输入您的密码"},{pattern:g.passwordPattern,message:"密码至少8位，包含字母、数字和符号，且首尾不能有空格"}],children:r.jsx(t.Password,{variant:"underlined",placeholder:"请输入新密码"})}),r.jsx(e.Item,{label:"确认密码*",name:"confirm_password",className:"flex-1 lg:mr-5",dependencies:["new_password"],rules:[{required:!0,message:"请输入确认密码"},({getFieldValue:o})=>({validator(s,l){return!l||o("new_password")===l?Promise.resolve():Promise.reject(new Error("新密码与确认密码不一致!"))}})],children:r.jsx(t.Password,{variant:"underlined",placeholder:"请输入确认密码"})})]}),r.jsx(j,{loading:n,className:"gha-primary-btn !py-1 w-full lg:w-48",type:"primary",htmlType:"submit",children:"保存"})]})})}u.createRoot(document.querySelector("#user-reset-pwd-form")).render(r.jsx(f,{children:r.jsx(b,{})}));
