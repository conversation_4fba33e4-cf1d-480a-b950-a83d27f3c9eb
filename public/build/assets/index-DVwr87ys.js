import{u as F,C as I,F as S,P as j,r as v,M as E,a as M,c as a,w as $,b as N,d as _,e as V,f as P,m as W,g as A}from"./index-DxcGRMc6.js";import{r as i}from"./PeopleSelectPopover-CDKo4AC-.js";import{C as z,e as B}from"./genStyleUtils-CI3YU7Yv.js";import{w as D}from"./PurePanel-zvE2p4pp.js";import"./isVisible-Bd4H7hpW.js";import{u as G}from"./useCSSVarCls-DWPRWpfJ.js";var R=function(e,o){var l={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(l[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)o.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(l[t[r]]=e[t[r]]);return l};const T=e=>{const{prefixCls:o,className:l,closeIcon:t,closable:r,type:c,title:x,children:m,footer:h}=e,w=R(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:d}=i.useContext(z),C=d(),s=o||d("modal"),p=G(C),[y,g,O]=F(s,p),f=`${s}-confirm`;let u={};return c?u={closable:r??!1,title:"",footer:"",children:i.createElement(I,Object.assign({},e,{prefixCls:s,confirmPrefixCls:f,rootPrefixCls:C,content:m}))}:u={closable:r??!0,title:x,footer:h!==null&&i.createElement(S,Object.assign({},e)),children:m},y(i.createElement(j,Object.assign({prefixCls:s,className:B(g,`${s}-pure-panel`,c&&f,c&&`${f}-${c}`,l,O,p)},w,{closeIcon:v(s,t),closable:r},u)))},U=D(T);function b(e){return a(A(e))}const n=E;n.useModal=M;n.info=function(o){return a($(o))};n.success=function(o){return a(N(o))};n.error=function(o){return a(_(o))};n.warning=b;n.warn=b;n.confirm=function(o){return a(V(o))};n.destroyAll=function(){for(;P.length;){const o=P.pop();o&&o()}};n.config=W;n._InternalPanelDoNotUseOrYouWillBeFired=U;export{n as M};
