import{$ as s}from"./helper-D414uohx.js";import{$ as l}from"./_map_helper-CAWV87xN.js";import"./index-Dq7h7Pqt.js";$(function(){new c});class c{constructor(){this.bootstrap()}bootstrap(){this.initFixedBar(".fixed-bar"),this.initFixedBar(".fixed-bar-m"),this.bindEvents(),this.initScrollActive(),this.initModalMap()}initModalMap(){s.getGlobalSubject().on("updateItemFav",function(a){window.$hotel.is_collect=a.data.nextState;let e=$(`.trigger-fav-el[data-type="${a.data.type}"][data-id="${a.data.id}"]`);e.find("i").removeClass("icon-Heart").removeClass("icon-Heart-filled"),e.find("i").addClass(a.data.nextState?"icon-Heart-filled":"icon-Heart")});const t=this;$("body").on("click",".gha-modal-wrap .close",function(){$(".gha-modal-wrap").addClass("hidden"),$("body").removeClass("overflow-hidden")}),$("body").on("click","#toggleMapBtn",function(){$(".gha-modal-wrap.map").removeClass("hidden"),$("body").addClass("overflow-hidden"),t.buildMap()})}buildMap(){if(this.mapBuilded)return;this.mapBuilded=!0;let t=window.$hotel;mapboxgl.accessToken="pk.eyJ1IjoiaWlpc2xlZSIsImEiOiJjbHJoN2Z3djMwbjY0MmptampmODRlcWdvIn0.yak7m5pJUycZ58aJUst7ag";const a=new mapboxgl.Map({container:"modalMap",style:"mapbox://styles/mapbox/streets-v12",zoom:10,center:[t.longitude,t.latitude]});a.addControl(new MapboxLanguage({defaultLanguage:"zh-Hans"})),a.on("load",function(){$(".gha-mapbox").removeClass("loading"),l.fitBoundsMap(a,[[t.longitude,t.latitude]]);let e=t,i;i=document.createElement("div"),i.className="gha-mapbox-marker-invalid",$(i).append('<i class="iconfont icon-Union"></i>');const o=new mapboxgl.Marker(i).setLngLat([e.longitude,e.latitude]).addTo(a);o.getElement().addEventListener("click",function(){l.flyMarkerToCenter(a,[e.longitude,e.latitude]);const n=l.getPopup({hotelData:t,slug:"offers",autoTriggerFav:!0});o.setPopup(n),o.togglePopup(),l.initPopupSwiper(t)})})}bindEvents(){$("body").on("click","#togglePolicy",function(){$(this).find("i").hasClass("icon-down")?$(this).find("i").removeClass("icon-down").addClass("icon-up"):$(this).find("i").removeClass("icon-up").addClass("icon-down"),$("#togglePolicyContent").toggleClass("hidden")}),$("body").on("click",".anchor-item",function(){$(".anchor-item").removeClass("active"),$(this).addClass("active")})}initFixedBar(t){const a=$(t).find(".fixed-sentinel-top")[0],e=$(t)[0];if(!e||!a)return;function i(){a.getBoundingClientRect().top<90?e.classList.add("gha-fixed"):e.classList.remove("gha-fixed")}window.addEventListener("scroll",i,!0),i()}initScrollActive(){function t(e){$(".fixed-bar .anchor-item").eq(e).hasClass("active")||($(".fixed-bar .anchor-item").removeClass("active"),$(".fixed-bar .anchor-item").eq(e).addClass("active")),$(".fixed-bar-m .anchor-item").eq(e).hasClass("active")||($(".fixed-bar-m .anchor-item").removeClass("active"),$(".fixed-bar-m .anchor-item").eq(e).addClass("active"))}function a(){let e=document.querySelector("#anchor0").getBoundingClientRect().top,i=document.querySelector("#anchor1").getBoundingClientRect().top,o=document.querySelector("#anchor2").getBoundingClientRect().top,n=[e,i,o].findIndex(d=>d>10);n===-1&&t(2),n===2&&t(1),[0,1].includes(n)&&t(0)}window.addEventListener("scroll",a)}}
