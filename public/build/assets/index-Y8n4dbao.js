$(function(){new a});class a{constructor(){this.bootstrap()}bootstrap(){this.initMobileCardTab()}initMobileCardTab(){$(".mobile-card-rights").on("click",".level-item .tracker",function(){const e=$(this).data("key");$(".level-step-container").hasClass(`active-${e}`)||($(".level-step-container").removeClass("active-1").removeClass("active-2").removeClass("active-3").removeClass("active-4"),$(".level-step-container").addClass(`active-${e}`),$(".card-panel").addClass("hidden"),$(`.card-panel-${e}`).removeClass("hidden"))})}}
