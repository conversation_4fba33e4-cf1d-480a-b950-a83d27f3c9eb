import{r as o,R as W}from"./PeopleSelectPopover-CDKo4AC-.js";import{_ as ee,e as N,l as L,c as re,h as T,g as ae,m as te,r as M,a as G,j as oe,C as q,k as ne,n as F}from"./genStyleUtils-CI3YU7Yv.js";import{_ as le,w as H,D as se,o as ie}from"./isVisible-Bd4H7hpW.js";import{u as ce}from"./useMergedState-BDSe6zqT.js";import{T as de,W as ue}from"./index-DKvg8qd3.js";import{u as A}from"./useCSSVarCls-DWPRWpfJ.js";import{F as be}from"./context-Gzj2nObQ.js";var fe=["prefixCls","className","style","checked","disabled","defaultChecked","type","title","onChange"],pe=o.forwardRef(function(e,r){var a=e.prefixCls,t=a===void 0?"rc-checkbox":a,c=e.className,C=e.style,h=e.checked,p=e.disabled,_=e.defaultChecked,w=_===void 0?!1:_,d=e.type,O=d===void 0?"checkbox":d,I=e.title,i=e.onChange,R=le(e,fe),g=o.useRef(null),u=o.useRef(null),l=ce(w,{value:h}),P=ee(l,2),k=P[0],x=P[1];o.useImperativeHandle(r,function(){return{focus:function(s){var f;(f=g.current)===null||f===void 0||f.focus(s)},blur:function(){var s;(s=g.current)===null||s===void 0||s.blur()},input:g.current,nativeElement:u.current}});var b=N(t,c,L(L({},"".concat(t,"-checked"),k),"".concat(t,"-disabled"),p)),m=function(s){p||("checked"in e||x(s.target.checked),i==null||i({target:T(T({},e),{},{type:O,checked:s.target.checked}),stopPropagation:function(){s.stopPropagation()},preventDefault:function(){s.preventDefault()},nativeEvent:s.nativeEvent}))};return o.createElement("span",{className:b,title:I,style:C,ref:u},o.createElement("input",re({},R,{className:"".concat(t,"-input"),ref:g,onChange:m,disabled:p,checked:!!k,type:O})),o.createElement("span",{className:"".concat(t,"-inner")}))});function me(e){const r=W.useRef(null),a=()=>{H.cancel(r.current),r.current=null};return[()=>{a(),r.current=H(()=>{r.current=null})},C=>{r.current&&(C.stopPropagation(),a()),e==null||e(C)}]}const ve=e=>{const{checkboxCls:r}=e,a=`${r}-wrapper`;return[{[`${r}-group`]:Object.assign(Object.assign({},M(e)),{display:"inline-flex",flexWrap:"wrap",columnGap:e.marginXS,[`> ${e.antCls}-row`]:{flex:1}}),[a]:Object.assign(Object.assign({},M(e)),{display:"inline-flex",alignItems:"baseline",cursor:"pointer","&:after":{display:"inline-block",width:0,overflow:"hidden",content:"'\\a0'"},[`& + ${a}`]:{marginInlineStart:0},[`&${a}-in-form-item`]:{'input[type="checkbox"]':{width:14,height:14}}}),[r]:Object.assign(Object.assign({},M(e)),{position:"relative",whiteSpace:"nowrap",lineHeight:1,cursor:"pointer",borderRadius:e.borderRadiusSM,alignSelf:"center",[`${r}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0,margin:0,[`&:focus-visible + ${r}-inner`]:oe(e)},[`${r}-inner`]:{boxSizing:"border-box",display:"block",width:e.checkboxSize,height:e.checkboxSize,direction:"ltr",backgroundColor:e.colorBgContainer,border:`${G(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,borderCollapse:"separate",transition:`all ${e.motionDurationSlow}`,"&:after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"25%",display:"table",width:e.calc(e.checkboxSize).div(14).mul(5).equal(),height:e.calc(e.checkboxSize).div(14).mul(8).equal(),border:`${G(e.lineWidthBold)} solid ${e.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${e.motionDurationFast} ${e.motionEaseInBack}, opacity ${e.motionDurationFast}`}},"& + span":{paddingInlineStart:e.paddingXS,paddingInlineEnd:e.paddingXS}})},{[`
        ${a}:not(${a}-disabled),
        ${r}:not(${r}-disabled)
      `]:{[`&:hover ${r}-inner`]:{borderColor:e.colorPrimary}},[`${a}:not(${a}-disabled)`]:{[`&:hover ${r}-checked:not(${r}-disabled) ${r}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"},[`&:hover ${r}-checked:not(${r}-disabled):after`]:{borderColor:e.colorPrimaryHover}}},{[`${r}-checked`]:{[`${r}-inner`]:{backgroundColor:e.colorPrimary,borderColor:e.colorPrimary,"&:after":{opacity:1,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`all ${e.motionDurationMid} ${e.motionEaseOutBack} ${e.motionDurationFast}`}}},[`
        ${a}-checked:not(${a}-disabled),
        ${r}-checked:not(${r}-disabled)
      `]:{[`&:hover ${r}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"}}},{[r]:{"&-indeterminate":{"&":{[`${r}-inner`]:{backgroundColor:`${e.colorBgContainer}`,borderColor:`${e.colorBorder}`,"&:after":{top:"50%",insetInlineStart:"50%",width:e.calc(e.fontSizeLG).div(2).equal(),height:e.calc(e.fontSizeLG).div(2).equal(),backgroundColor:e.colorPrimary,border:0,transform:"translate(-50%, -50%) scale(1)",opacity:1,content:'""'}},[`&:hover ${r}-inner`]:{backgroundColor:`${e.colorBgContainer}`,borderColor:`${e.colorPrimary}`}}}}},{[`${a}-disabled`]:{cursor:"not-allowed"},[`${r}-disabled`]:{[`&, ${r}-input`]:{cursor:"not-allowed",pointerEvents:"none"},[`${r}-inner`]:{background:e.colorBgContainerDisabled,borderColor:e.colorBorder,"&:after":{borderColor:e.colorTextDisabled}},"&:after":{display:"none"},"& + span":{color:e.colorTextDisabled},[`&${r}-indeterminate ${r}-inner::after`]:{background:e.colorTextDisabled}}}]};function Ce(e,r){const a=te(r,{checkboxCls:`.${e}`,checkboxSize:r.controlInteractiveSize});return ve(a)}const X=ae("Checkbox",(e,{prefixCls:r})=>[Ce(r,e)]),K=W.createContext(null);var he=function(e,r){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,t=Object.getOwnPropertySymbols(e);c<t.length;c++)r.indexOf(t[c])<0&&Object.prototype.propertyIsEnumerable.call(e,t[c])&&(a[t[c]]=e[t[c]]);return a};const ge=(e,r)=>{var a;const{prefixCls:t,className:c,rootClassName:C,children:h,indeterminate:p=!1,style:_,onMouseEnter:w,onMouseLeave:d,skipGroup:O=!1,disabled:I}=e,i=he(e,["prefixCls","className","rootClassName","children","indeterminate","style","onMouseEnter","onMouseLeave","skipGroup","disabled"]),{getPrefixCls:R,direction:g,checkbox:u}=o.useContext(q),l=o.useContext(K),{isFormItemInput:P}=o.useContext(be),k=o.useContext(se),x=(a=(l==null?void 0:l.disabled)||I)!==null&&a!==void 0?a:k,b=o.useRef(i.value),m=o.useRef(null),E=ne(r,m);o.useEffect(()=>{l==null||l.registerValue(i.value)},[]),o.useEffect(()=>{if(!O)return i.value!==b.current&&(l==null||l.cancelValue(b.current),l==null||l.registerValue(i.value),b.current=i.value),()=>l==null?void 0:l.cancelValue(i.value)},[i.value]),o.useEffect(()=>{var v;!((v=m.current)===null||v===void 0)&&v.input&&(m.current.input.indeterminate=p)},[p]);const s=R("checkbox",t),f=A(s),[V,j,D]=X(s,f),$=Object.assign({},i);l&&!O&&($.onChange=(...v)=>{i.onChange&&i.onChange.apply(i,v),l.toggleOption&&l.toggleOption({label:h,value:i.value})},$.name=l.name,$.checked=l.value.includes(i.value));const z=N(`${s}-wrapper`,{[`${s}-rtl`]:g==="rtl",[`${s}-wrapper-checked`]:$.checked,[`${s}-wrapper-disabled`]:x,[`${s}-wrapper-in-form-item`]:P},u==null?void 0:u.className,c,C,D,f,j),n=N({[`${s}-indeterminate`]:p},de,j),[y,S]=me($.onClick);return V(o.createElement(ue,{component:"Checkbox",disabled:x},o.createElement("label",{className:z,style:Object.assign(Object.assign({},u==null?void 0:u.style),_),onMouseEnter:w,onMouseLeave:d,onClick:y},o.createElement(pe,Object.assign({},$,{onClick:S,prefixCls:s,className:n,disabled:x,ref:E})),h!=null&&o.createElement("span",{className:`${s}-label`},h))))},J=o.forwardRef(ge);var xe=function(e,r){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,t=Object.getOwnPropertySymbols(e);c<t.length;c++)r.indexOf(t[c])<0&&Object.prototype.propertyIsEnumerable.call(e,t[c])&&(a[t[c]]=e[t[c]]);return a};const $e=o.forwardRef((e,r)=>{const{defaultValue:a,children:t,options:c=[],prefixCls:C,className:h,rootClassName:p,style:_,onChange:w}=e,d=xe(e,["defaultValue","children","options","prefixCls","className","rootClassName","style","onChange"]),{getPrefixCls:O,direction:I}=o.useContext(q),[i,R]=o.useState(d.value||a||[]),[g,u]=o.useState([]);o.useEffect(()=>{"value"in d&&R(d.value||[])},[d.value]);const l=o.useMemo(()=>c.map(n=>typeof n=="string"||typeof n=="number"?{label:n,value:n}:n),[c]),P=n=>{u(y=>y.filter(S=>S!==n))},k=n=>{u(y=>[].concat(F(y),[n]))},x=n=>{const y=i.indexOf(n.value),S=F(i);y===-1?S.push(n.value):S.splice(y,1),"value"in d||R(S),w==null||w(S.filter(v=>g.includes(v)).sort((v,U)=>{const Y=l.findIndex(B=>B.value===v),Z=l.findIndex(B=>B.value===U);return Y-Z}))},b=O("checkbox",C),m=`${b}-group`,E=A(b),[s,f,V]=X(b,E),j=ie(d,["value","disabled"]),D=c.length?l.map(n=>o.createElement(J,{prefixCls:b,key:n.value.toString(),disabled:"disabled"in n?n.disabled:d.disabled,value:n.value,checked:i.includes(n.value),onChange:n.onChange,className:N(`${m}-item`,n.className),style:n.style,title:n.title,id:n.id,required:n.required},n.label)):t,$=o.useMemo(()=>({toggleOption:x,value:i,disabled:d.disabled,name:d.name,registerValue:k,cancelValue:P}),[x,i,d.disabled,d.name,k,P]),z=N(m,{[`${m}-rtl`]:I==="rtl"},h,p,V,E,f);return s(o.createElement("div",Object.assign({className:z,style:_},j,{ref:r}),o.createElement(K.Provider,{value:$},D)))}),Q=J;Q.Group=$e;Q.__ANT_CHECKBOX=!0;export{Q as C,pe as a,Ce as g,me as u};
