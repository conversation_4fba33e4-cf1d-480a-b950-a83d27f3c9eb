import{j as i}from"./hotel-item-D_zvdyIk.js";function c({step:s=1}){return i.jsxs("div",{className:"booking-step-wrap",children:[i.jsxs("div",{className:`step-item ${s===1?"active":""}`,children:[i.jsx("div",{className:"dot-line right icon",children:i.jsx("i",{className:"iconfont icon-Room"})}),i.jsx("h4",{children:"Step01"}),i.jsx("p",{children:"选择日期及客房"})]}),i.jsxs("div",{className:`step-item ${s===2?"active":""}`,children:[i.jsx("div",{className:"dot-line left right icon",children:i.jsx("i",{className:"iconfont icon-Pool"})}),i.jsx("h4",{children:"Step02"}),i.jsx("p",{children:"填写预订信息"})]}),i.jsxs("div",{className:`step-item ${s===3?"active":""}`,children:[i.jsx("div",{className:"dot-line left icon",children:i.jsx("i",{className:"iconfont icon-Our_Partners_Purple"})}),i.jsx("h4",{children:"Step03"}),i.jsx("p",{children:"确认预订"})]})]})}export{c as O};
