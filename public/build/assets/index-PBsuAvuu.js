import{r as s}from"./PeopleSelectPopover-CDKo4AC-.js";import{g as F,m as G,r as U,C as X,e as x,i as Y}from"./genStyleUtils-CI3YU7Yv.js";import{u as Z}from"./useMergedState-BDSe6zqT.js";import{K as q}from"./KeyCode-lh1qUinJ.js";import{i as J,g as Q}from"./zoom-BbRr1fkt.js";import{c as ee}from"./isVisible-Bd4H7hpW.js";import{g as te,a as oe,P as ne,T as re}from"./index-CJQq4WAU.js";import"./index-CaUFHQr4.js";import"./ContextIsolator-CVrwwDX4.js";import{g as ae}from"./roundedArrow-DVJD-5zd.js";import{P as se}from"./color-DKTup0-d.js";const h=e=>e?typeof e=="function"?e():e:null,le=e=>{const{componentCls:o,popoverColor:n,titleMinWidth:t,fontWeightStrong:r,innerPadding:a,boxShadowSecondary:d,colorTextHeading:p,borderRadiusLG:m,zIndexPopup:g,titleMarginBottom:l,colorBgElevated:u,popoverBg:v,titleBorderBottom:f,innerContentPadding:b,titlePadding:i}=e;return[{[o]:Object.assign(Object.assign({},U(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:g,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":u,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${o}-content`]:{position:"relative"},[`${o}-inner`]:{backgroundColor:v,backgroundClip:"padding-box",borderRadius:m,boxShadow:d,padding:a},[`${o}-title`]:{minWidth:t,marginBottom:l,color:p,fontWeight:r,borderBottom:f,padding:i},[`${o}-inner-content`]:{color:n,padding:b}})},te(e,"var(--antd-arrow-background-color)"),{[`${o}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${o}-content`]:{display:"inline-block"}}}]},ie=e=>{const{componentCls:o}=e;return{[o]:se.map(n=>{const t=e[`${n}6`];return{[`&${o}-${n}`]:{"--antd-arrow-background-color":t,[`${o}-inner`]:{backgroundColor:t},[`${o}-arrow`]:{background:"transparent"}}}})}},ce=e=>{const{lineWidth:o,controlHeight:n,fontHeight:t,padding:r,wireframe:a,zIndexPopupBase:d,borderRadiusLG:p,marginXS:m,lineType:g,colorSplit:l,paddingSM:u}=e,v=n-t,f=v/2,b=v/2-o,i=r;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:d+30},ae(e)),oe({contentRadius:p,limitVerticalRadius:!0})),{innerPadding:a?0:12,titleMarginBottom:a?0:m,titlePadding:a?`${f}px ${i}px ${b}px`:0,titleBorderBottom:a?`${o}px ${g} ${l}`:"none",innerContentPadding:a?`${u}px ${i}px`:0})},k=F("Popover",e=>{const{colorBgElevated:o,colorText:n}=e,t=G(e,{popoverBg:o,popoverColor:n});return[le(t),ie(t),J(t,"zoom-big")]},ce,{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]});var de=function(e,o){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)o.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(n[t[r]]=e[t[r]]);return n};const T=({title:e,content:o,prefixCls:n})=>!e&&!o?null:s.createElement(s.Fragment,null,e&&s.createElement("div",{className:`${n}-title`},e),o&&s.createElement("div",{className:`${n}-inner-content`},o)),pe=e=>{const{hashId:o,prefixCls:n,className:t,style:r,placement:a="top",title:d,content:p,children:m}=e,g=h(d),l=h(p),u=x(o,n,`${n}-pure`,`${n}-placement-${a}`,t);return s.createElement("div",{className:u,style:r},s.createElement("div",{className:`${n}-arrow`}),s.createElement(ne,Object.assign({},e,{className:o,prefixCls:n}),m||s.createElement(T,{prefixCls:n,title:g,content:l})))},me=e=>{const{prefixCls:o,className:n}=e,t=de(e,["prefixCls","className"]),{getPrefixCls:r}=s.useContext(X),a=r("popover",o),[d,p,m]=k(a);return d(s.createElement(pe,Object.assign({},t,{prefixCls:a,hashId:p,className:x(n,m)})))};var ge=function(e,o){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)o.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(n[t[r]]=e[t[r]]);return n};const ue=s.forwardRef((e,o)=>{var n,t;const{prefixCls:r,title:a,content:d,overlayClassName:p,placement:m="top",trigger:g="hover",children:l,mouseEnterDelay:u=.1,mouseLeaveDelay:v=.1,onOpenChange:f,overlayStyle:b={},styles:i,classNames:y}=e,w=ge(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:N,className:_,style:I,classNames:S,styles:$}=Y("popover"),O=N("popover",r),[W,z,D]=k(O),M=N(),R=x(p,z,D,_,S.root,y==null?void 0:y.root),V=x(S.body,y==null?void 0:y.body),[A,H]=Z(!1,{value:(n=e.open)!==null&&n!==void 0?n:e.visible,defaultValue:(t=e.defaultOpen)!==null&&t!==void 0?t:e.defaultVisible}),j=(c,C)=>{H(c,!0),f==null||f(c,C)},K=c=>{c.keyCode===q.ESC&&j(!1,c)},L=c=>{j(c)},E=h(a),B=h(d);return W(s.createElement(re,Object.assign({placement:m,trigger:g,mouseEnterDelay:u,mouseLeaveDelay:v},w,{prefixCls:O,classNames:{root:R,body:V},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},$.root),I),b),i==null?void 0:i.root),body:Object.assign(Object.assign({},$.body),i==null?void 0:i.body)},ref:o,open:A,onOpenChange:L,overlay:E||B?s.createElement(T,{prefixCls:O,title:E,content:B}):null,transitionName:Q(M,"zoom-big",w.transitionName),"data-popover-inject":!0}),ee(l,{onKeyDown:c=>{var C,P;s.isValidElement(l)&&((P=l==null?void 0:(C=l.props).onKeyDown)===null||P===void 0||P.call(C,c)),K(c)}})))}),fe=ue;fe._InternalPanelDoNotUseOrYouWillBeFired=me;export{fe as P};
