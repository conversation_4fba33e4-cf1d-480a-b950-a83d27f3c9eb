import{j as e}from"./hotel-item-D_zvdyIk.js";import{r}from"./PeopleSelectPopover-CDKo4AC-.js";import{M as d}from"./index-DVwr87ys.js";import"./index-DxcGRMc6.js";import"./genStyleUtils-CI3YU7Yv.js";import"./isVisible-Bd4H7hpW.js";import"./index-C_TizibV.js";import"./index-DKvg8qd3.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./button-C2fNxKeA.js";import"./useSize-CbUlsqBW.js";import"./color-DKTup0-d.js";import"./pickAttrs-D1C8emUZ.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./zoom-BbRr1fkt.js";import"./CloseOutlined-CGWqYTdG.js";import"./KeyCode-lh1qUinJ.js";import"./extendsObject-78o_rR5W.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./index-DR0pJ98a.js";import"./PurePanel-zvE2p4pp.js";import"./useMergedState-BDSe6zqT.js";function A({className:i}){const[t,s]=r.useState(!1),[n,c]=r.useState(!0);return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:`price-detail-wrap ${i||""}`,children:[e.jsxs("div",{className:"flex flex-row items-center justify-between font-16",children:[e.jsx("h3",{className:"font-16 font-semibold",children:"价格详情"}),e.jsx("a",{className:"underline font-14",href:"javascript:;",onClick:()=>s(!0),children:"查看价格明细"})]}),e.jsx("div",{className:"py-3 border-b border-[#c9c9c9]/20 mt-2",children:e.jsxs("div",{className:"flex flex-row items-center justify-between font-14",children:[e.jsx("p",{children:"小计"}),e.jsx("p",{children:"CNY 5,668.30"})]})}),e.jsx("div",{className:"py-3 border-b border-[#c9c9c9]/20",children:e.jsxs("div",{className:"flex flex-row items-center justify-between font-14",children:[e.jsx("p",{children:"小计"}),e.jsx("p",{children:"CNY 5,668.30"})]})}),e.jsx("div",{className:"pt-4",children:e.jsxs("div",{className:"flex flex-row items-center justify-between font-14",children:[e.jsx("p",{className:"font-medium",children:"总计"}),e.jsx("p",{className:"font-20 font-IvyMode-Reg",children:"CNY 6,668.30"})]})})]}),e.jsx(d,{open:t,footer:null,width:700,closable:!1,destroyOnHidden:!0,transitionName:"ant-fade",rootClassName:"gha-antd-modal",children:e.jsxs("div",{className:"gha-antd-modal-wrapper",children:[e.jsx("div",{className:"close-icon",onClick:()=>s(!1),children:e.jsx("i",{className:"iconfont icon-Close"})}),e.jsxs("div",{className:"gha-antd-modal-content",children:[e.jsxs("div",{className:"px-8",children:[e.jsx("div",{className:"gha-divider w-32 mx-auto",children:e.jsx("p",{className:"px-2 text-[#0c0509]/20",children:"价格明细"})}),e.jsx("h2",{className:"font-20 text-center mt-1",children:"长沙马赫酒店"}),e.jsx("div",{className:"mt-6",children:new Array(2).fill(0).map((a,l)=>e.jsxs("div",{className:"p-6 border border-[#919191]/20 shadow-md rounded-lg mt-3 first:mt-0 font-14",children:[e.jsx("h4",{className:"font-15",children:"M2尊贵客房（大床）"}),e.jsx("p",{className:"mt-1",children:"会员特价"}),e.jsxs("div",{className:"mt-1",children:[e.jsxs("div",{className:"py-1.5 border-b border-[#919191]/20 flex flex-row items-center justify-between",children:[e.jsx("p",{children:"2025-05-08"}),e.jsx("p",{className:"font-IvyMode-Reg",children:"CNY 2,834.15"})]}),e.jsxs("div",{className:"py-1.5 border-b border-[#919191]/20 flex flex-row items-center justify-between",children:[e.jsx("p",{children:"2025-05-08"}),e.jsx("p",{className:"font-IvyMode-Reg",children:"CNY 2,834.15"})]})]}),e.jsxs("div",{className:"mt-1 py-1.5 flex flex-row items-center justify-between",children:[e.jsx("p",{children:"房间1合计"}),e.jsx("p",{className:"font-IvyMode-Reg font-16",children:"CNY 2,834.15"})]})]},l))})]}),e.jsx("div",{className:"mt-8 pt-2 border-t border-[#919191]/20",children:e.jsxs("div",{className:"px-8",children:[e.jsxs("div",{className:"py-1.5 border-b border-[#919191]/20 flex flex-row items-center justify-between",children:[e.jsx("p",{children:"小计"}),e.jsx("p",{className:"font-IvyMode-Reg",children:"CNY 2,834.15"})]}),e.jsxs("div",{className:"py-1.5 border-b border-[#919191]/20 flex flex-row items-center justify-between",children:[e.jsx("p",{children:"税费"}),e.jsx("p",{className:"font-IvyMode-Reg",children:"CNY 2,834.15"})]}),e.jsxs("div",{className:"py-1.5 flex flex-row items-center justify-between",children:[e.jsx("p",{children:"总计"}),e.jsx("p",{className:"font-IvyMode-Reg font-18",children:"CNY 2,834.15"})]})]})})]})]})})]})}export{A as O};
