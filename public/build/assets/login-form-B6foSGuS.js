import{j as r}from"./hotel-item-D_zvdyIk.js";import{c}from"./client-CHYie6Ha.js";import{r as d}from"./PeopleSelectPopover-CDKo4AC-.js";import{G as h,u}from"./GhaConfigProvider-BB5IW5PM.js";import{$ as x}from"./_constants-CI6xcXYb.js";import{$ as f,a as j,b as g}from"./helper-D414uohx.js";import{F as t}from"./index-CC5HyPSW.js";import{I as o}from"./index-DNIuBGmG.js";import{B as b}from"./button-C2fNxKeA.js";import"./index-C_TizibV.js";import"./isVisible-Bd4H7hpW.js";import"./genStyleUtils-CI3YU7Yv.js";import"./CloseOutlined-CGWqYTdG.js";import"./KeyCode-lh1qUinJ.js";import"./pickAttrs-D1C8emUZ.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./index-DxcGRMc6.js";import"./index-DKvg8qd3.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./color-DKTup0-d.js";import"./useSize-CbUlsqBW.js";import"./zoom-BbRr1fkt.js";import"./extendsObject-78o_rR5W.js";import"./index-DR0pJ98a.js";import"./index-Dq7h7Pqt.js";import"./collapse-BbEVqHco.js";import"./useForm-DF1XNZWJ.js";import"./row-BiveCJJi.js";import"./useBreakpoint-D48_IWaH.js";import"./index-CJQq4WAU.js";import"./index-CaUFHQr4.js";import"./useMergedState-BDSe6zqT.js";import"./roundedArrow-DVJD-5zd.js";import"./index-wg7qNA5H.js";import"./Input-DCb0FIZx.js";function w(){const[i]=t.useForm(),[m,a]=d.useState(!1),s=u();function n(l){a(!0),j.authLogin(l).subscribe({next:e=>{if(a(!1),e.status_code!==200){s.error(e.message);return}e.data.token&&g.setToken(e.data.token);let p=new URLSearchParams(location.search.replace("?","")).get("redirect")||"/";location.href=p},error:e=>{console.error("登录请求错误:",e),a(!1),s.error("登录失败，请检查网络连接")},complete:()=>{console.log("请求完成")}})}return r.jsxs("div",{className:"auth-box login-box",children:[r.jsxs("div",{className:"form-wrap",children:[r.jsx("h3",{children:"请登录"}),r.jsxs(t,{layout:"vertical",form:i,requiredMark:!1,initialValues:f.isLaravelLocal()?{email:"<EMAIL>",password:"123456Abc$"}:{},onFinish:n,children:[r.jsx(t.Item,{label:"邮箱*",name:"email",rules:[{required:!0,message:"请输入您的邮箱"},{pattern:x.emailPattern,message:"邮箱格式不正确"}],children:r.jsx(o,{variant:"underlined",placeholder:"请输入您的邮箱"})}),r.jsx(t.Item,{label:"密码*",name:"password",rules:[{required:!0,message:"请输入密码"}],children:r.jsx(o.Password,{variant:"underlined",placeholder:"请输入密码"})}),r.jsx("p",{className:"text-right -mt-3.5 mb-5",children:r.jsx("a",{href:window.__ServerVars__.forgetPwdUri,className:"underline",children:"忘记密码?"})}),r.jsx(t.Item,{label:null,children:r.jsx(b,{className:"gha-primary-btn",loading:m,type:"primary",shape:"round",block:!0,htmlType:"submit",children:"登录"})}),r.jsx("p",{className:"text-center",children:r.jsx("a",{href:window.__ServerVars__.activeUri,className:"underline",children:"激活您的在线帐户"})})]})]}),r.jsxs("div",{className:"extra-wrap",children:[r.jsx("h3",{children:"免费加入GHA全球会员计划"}),r.jsxs("p",{children:["GHA代表着40个品牌,",r.jsx("br",{}),"800多家酒店,遍布100个国家,",r.jsx("br",{}),"服务会员25万",r.jsx("br",{})]}),r.jsx("a",{className:"gha-btn",href:window.__ServerVars__.registerUri,children:"立即加入GHA酒店忠诚计划"})]})]})}c.createRoot(document.querySelector(".auth-box-wrap")).render(r.jsx(h,{children:r.jsx(w,{})}));
