import{j as o}from"./hotel-item-D_zvdyIk.js";import{c as s}from"./client-CHYie6Ha.js";import{$ as t}from"./helper-D414uohx.js";import{L as a}from"./logout-modal-D0eJDxtt.js";import{G as n,u as c}from"./GhaConfigProvider-BB5IW5PM.js";import{r as f}from"./PeopleSelectPopover-CDKo4AC-.js";import"./index-C_TizibV.js";import"./index-Dq7h7Pqt.js";import"./index-DVwr87ys.js";import"./index-DxcGRMc6.js";import"./genStyleUtils-CI3YU7Yv.js";import"./isVisible-Bd4H7hpW.js";import"./index-DKvg8qd3.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./button-C2fNxKeA.js";import"./useSize-CbUlsqBW.js";import"./color-DKTup0-d.js";import"./pickAttrs-D1C8emUZ.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./zoom-BbRr1fkt.js";import"./CloseOutlined-CGWqYTdG.js";import"./KeyCode-lh1qUinJ.js";import"./extendsObject-78o_rR5W.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./index-DR0pJ98a.js";import"./PurePanel-zvE2p4pp.js";import"./useMergedState-BDSe6zqT.js";const g=t.createReactRootElement("__react__root__app__");function l(){f.useEffect(()=>(t.getGlobalSubject().on("showMessage",r,"APP"),()=>{t.getGlobalSubject().off("showMessage",r,"APP")}),[]);const e=c();function r(m){const{type:p,message:i}=m.data;e[p](i)}return o.jsx(o.Fragment,{children:o.jsx(a,{})})}s.createRoot(g).render(o.jsx(n,{children:o.jsx(l,{})}));
