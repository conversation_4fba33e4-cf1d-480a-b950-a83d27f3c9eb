class f{formatClusterPoint(i){return i.map(function(e){var t=e.id;return{type:"Feature",id:t,properties:{id:t},geometry:{type:"Point",coordinates:[+e.longitude,+e.latitude]}}})}getPopup(i){var r,p;const{hotelData:e,slug:t="hotels",autoTriggerFav:n=!1}=i,d={closeButton:!1,maxWidth:"320px",className:"gha-mapbox-hotel-popup-root",offset:[0,25],anchor:"top"},c=(e.images||[e.image]).map(s=>`<div class="swiper-slide !h-[213px]"><div class="relative w-full h-full"><img width="658" height="428" src="${s}"/></div></div>`).join("");let o;t==="hotels"&&(o=e.brand_img||""),t==="offers"&&(o=e.brand_img),o=o||"https://admin.dev.ghaloyalty.com/var/site/storage/images/0/8/3/0/90380-3-eng-GB/98b725aa0183-BR_Logo_140.png";let a="",l="";t==="hotels"&&(a=[e.country,(p=(r=e.city)==null?void 0:r[0])==null?void 0:p.name].filter(s=>s).join("，"),l="hotel"),t==="offers"&&(a=[e.country_name,e.city_name].filter(s=>s).join("，"),l="offer");const h=`
      <h5 class="brand"><a href="/about/brands/${e.brand_id}" class="hover:underline">${e.brand_name||"占位brand_name"}</a></h5>
      <h2 class="title"><a href="/hotel/${e.id}" class="hover:underline">${e.hotel_name}</a></h2>
      ${a?`<h5 class="local">${a}</h5>`:""}
      <div class="spt-line"></div>
      <div class="price-wrap">
        <div class="protrude">
          <p>会员价低至</p>
          <p class="price-num">CNY1999</p>
        </div>
        <div class="">
          <p>会员价低至</p>
          <p class="price-num">CNY2999</p>
        </div>
      </div>
    `,u=`
      <h5 class="brand"><a href="/brand/" class="hover:underline">${e.brand_name||"占位brand_name"}</a></h5>
      <h2 class="title"><a href="/offer/${e.id}" class="hover:underline">${e.title}</a></h2>
      <div class="spt-line"></div>
      ${a?`<h5 class="local">${a}</h5>`:""}
    `,v=`
        <div class="gha-mapbox-hotel-popup hotel-popup-${e.id}">
          <div class="swiper-el gha-swiper">
            <div class="absolute z-20 top-2.5 left-0 w-full">
              <img class="select-none max-w-[25%] max-h-[40%] filter invert ml-2.5" src="${o}" alt="" />
            </div>
            <div class="absolute z-20 top-2.5 right-2.5 ${n?"trigger-fav-el":""}" data-type="${l}" data-id="${e.id}">
              <i data-id="${e.id}" class="fav-icon cursor-pointer iconfont ${e.is_collect?"icon-Heart-filled":"icon-Heart"} text-white p-1 text-4xl"></i>
            </div>
            <div class="swiper-pagination px-4"></div>
            <div class="gha-swiper-button gha-swiper-button-prev"><i class="iconfont icon-a-Arrow-Left"></i></div>
            <div class="gha-swiper-button gha-swiper-button-next"><i class="iconfont icon-a-Arrow-Right"></i></div>
            <div class="swiper-container">
              <div class="swiper-wrapper">
                ${c}
              </div>
            </div>
          </div>
          <div class="hotel-item hotel-item-large">
            <div class="info-wrap">
              ${t==="hotels"?h:u}
            </div>
          </div>
        </div>
      `;return new mapboxgl.Popup(d).setHTML(v)}initPopupSwiper(i){setTimeout(()=>{new Swiper(`.hotel-popup-${i.id} .swiper-el .swiper-container`,{pagination:{el:`.hotel-popup-${i.id} .swiper-pagination`,clickable:!0},navigation:{nextEl:`.hotel-popup-${i.id} .gha-swiper-button-next`,prevEl:`.hotel-popup-${i.id} .gha-swiper-button-prev`}})},200)}fitBoundsMap(i,e){let t=new mapboxgl.LngLatBounds;e.forEach(function(n){t.extend(n)}),i.fitBounds(t,{padding:100,maxZoom:12})}flyMarkerToCenter(i,e){i.flyTo({center:e,offset:[0,-200],duration:300,curve:0})}}const m=new f;export{m as $};
