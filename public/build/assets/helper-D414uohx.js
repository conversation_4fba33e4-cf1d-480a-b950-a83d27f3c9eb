var f=Object.defineProperty;var S=(t,e,r)=>e in t?f(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var c=(t,e,r)=>S(t,typeof e!="symbol"?e+"":e,r);import{a as h}from"./index-Dq7h7Pqt.js";class b{constructor(){c(this,"keys",{token:"__TOKEN__"})}get(e){return localStorage.getItem(e)}set(e,r){return localStorage.setItem(e,r)}remove(e){localStorage.removeItem(e)}setToken(e){return this.set(this.keys.token,e)}getToken(){return this.get(this.keys.token)}setRegisterToken(e,r){return localStorage.set(`__REGISTER__${e}`,r)}getRegisterToken(e){return localStorage.get(`__REGISTER__${e}`)}removeToken(){return this.remove(this.keys.token)}}const s=new b,{Observable:H,catchError:d,defer:g,delay:i,map:a,of:u,concatMap:B}=window.rxjs;class v{buildUrl(e){return`/api/v1${e.url}`}buildHeaders(e){const r=s.getToken()?{Authorization:`Bearer ${s.getToken()}`}:{};return{"Content-Type":"application/json",lang:window.__currentLang__||"zh",...r,...e.headers}}request(e){const{url:r,method:n,data:o,headers:x,params:w}=e;return g(()=>{var l;return h({url:`${((l=window==null?void 0:window.__Server_Data__)==null?void 0:l.host)||window.location.origin}${this.buildUrl(e)}`,method:n||"GET",data:o||{},headers:this.buildHeaders(e),params:w||{},withCredentials:e.withCredentials||!1}).then(m=>m.data)}).pipe(i(500),a(this.handleResponse),d(this.handleError))}directRequest(e){return g(()=>h({url:`${window.location.origin}${e.url}`,method:e.method||"GET",data:e.data||{},headers:e.headers||{},params:e.params||{},withCredentials:e.withCredentials||!1}).then(r=>r.data)).pipe(d(this.handleError))}get(e={}){return this.request({...e,method:"GET",withCredentials:!0})}post(e={}){return this.request({...e,method:"POST",withCredentials:!0})}handle401(e){return e.status_code===401&&(s.removeToken(),location.href=window.__Server_Data__.homeUrl),e}handleError(e){return console.error("123131313",e),u({success:!1,message:"网络错误，请重试",status_code:500,data:null})}handleResponse(e){return e}register(e){return this.request({url:"/api/auth/register",method:"POST",data:e})}hook2000(){return u(null).pipe(i(2e3))}searchObscure(e){return this.get({url:`/hotel/searchObscure?keyword=${e}`})}searchOffers(e){return this.post({url:"/offers/list",data:{...e,page_size:1e4}}).pipe(a(r=>r))}searchHotels(e){return this.post({url:"/hotel/searchKey",data:{...e,page_size:1e4}}).pipe(a(r=>r))}collectItem(e){return this.post({url:"/user/add_collect",data:e})}search(e,r){return u(null).pipe(i(2e3),a(()=>({status:!0,data:e==="hotels"?window.$hotels:window.$offers})))}authLogout(e){return this.directRequest({url:"/logout",method:"POST",data:e,withCredentials:!0})}authRegister(e){return this.post({url:"/auth/register",data:e})}authLogin(e){return this.directRequest({url:"/auth/loginUser",method:"POST",data:e,withCredentials:!0})}authActiveAccount(e){return this.post({url:"/auth/active",data:{...e,ghaMarketingYn:!0}})}authFindAccount(e){return this.post({url:"/auth/find_member",data:e})}authSendResetPasswordEmail(e){return this.post({url:"/auth/reset_pwd",data:e})}authResetPassword(e){return this.post({url:"/auth/set_pwd",data:e})}getNewsList(e,r){return this.get({url:`/about/${e}`,params:{...r,page_size:20}})}userUpPassword(e){return this.post({url:"/up_pwd",data:e})}userGetCollectItems(e){return this.get({url:"/user/collect",params:{type:e.join(",")}})}}const J=new v,{Observable:k}=window.rxjs;function R(t,e){const r=document.createElement("div");return r.id=t||"__react__root__",(e||document.body).appendChild(r),r}function C(){return __Global_Subject__}function T(){$(".page-content").addClass("loading")}function E(){$(".page-content").removeClass("loading")}function O(t,e){_.getGlobalSubject().emit("showModal",{type:t,config:e})}function y(t,e="error"){_.getGlobalSubject().emit("showMessage",{type:e,message:t})}function L(){return Math.random().toString(36).substring(2,9)}function p(t){return window.__Server_Data__[t]}function j(t){return t.username}function G(t){return JSON.parse(JSON.stringify(t))}function M(){return p("isLogin")?!0:(window.loginRedirectUrl=location.href,$(".login-btn-trigger:not(.logined)").trigger("click"),!1)}function P(){const t="193823154";return new k(e=>{try{var r=new TencentCaptcha(t,o=>{e.next(o),e.complete()},{userLanguage:window.__currentLang__});r.show()}catch(o){console.error(o);var n="trerror_1001_"+t+"_"+Math.floor(new Date().getTime()/1e3);e.next({ret:-1,randstr:"@"+Math.random().toString(36).substr(2),ticket:n,errorCode:1001,errorMessage:"jsload_error"}),e.complete()}})}function U(t){try{JSON.parse(t)}catch{return!1}return!0}function I(){const t=document.querySelectorAll(".gha-popover-root");let e=!1;for(let r=0;r<t.length;r++){const n=t[r];if(!!(n&&n.getBoundingClientRect().width>0&&n.getBoundingClientRect().height>0)){e=!0;break}}return e}function q(t){return Object.keys(t).filter(e=>!!t[e]).map(e=>`${e}=${encodeURIComponent(t[e])}`).join("&")}function A(t){let e={};return new URLSearchParams(t).forEach((r,n)=>{e[n]=decodeURIComponent(r)}),e}function N(){return window.__Server_Data__.appEnv==="local1"}const _={createReactRootElement:R,getGlobalSubject:C,showLoading:T,hideLoading:E,showModal:O,showMessage:y,hash:L,getServerConfig:p,formatUsername:j,clone:G,loginProtect:M,verifyCaptcha:P,isJsonString:U,isAntdGhaPopoverRootVisible:I,encodeSearchHash:q,decodeSearchHash:A,isLaravelLocal:N};export{_ as $,J as a,s as b};
