import{r as Z}from"./PeopleSelectPopover-CDKo4AC-.js";import{u as ce}from"./context-Gzj2nObQ.js";import{g as fe}from"./isVisible-Bd4H7hpW.js";const ee=e=>typeof e=="object"&&e!=null&&e.nodeType===1,te=(e,o)=>(!o||e!=="hidden")&&e!=="visible"&&e!=="clip",B=(e,o)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){const r=getComputedStyle(e,null);return te(r.overflowY,o)||te(r.overflowX,o)||(l=>{const n=(t=>{if(!t.ownerDocument||!t.ownerDocument.defaultView)return null;try{return t.ownerDocument.defaultView.frameElement}catch{return null}})(l);return!!n&&(n.clientHeight<l.scrollHeight||n.clientWidth<l.scrollWidth)})(e)}return!1},C=(e,o,r,l,n,t,i,c)=>t<e&&i>o||t>e&&i<o?0:t<=e&&c<=r||i>=o&&c>=r?t-e-l:i>o&&c<r||t<e&&c>r?i-o+n:0,ae=e=>{const o=e.parentElement;return o??(e.getRootNode().host||null)},oe=(e,o)=>{var r,l,n,t;if(typeof document>"u")return[];const{scrollMode:i,block:c,inline:f,boundary:L,skipOverflowHiddenElements:ie}=o,se=typeof L=="function"?L:p=>p!==L;if(!ee(e))throw new TypeError("Invalid target");const P=document.scrollingElement||document.documentElement,I=[];let a=e;for(;ee(a)&&se(a);){if(a=ae(a),a===P){I.push(a);break}a!=null&&a===document.body&&B(a)&&!B(document.documentElement)||a!=null&&B(a,ie)&&I.push(a)}const m=(l=(r=window.visualViewport)==null?void 0:r.width)!=null?l:innerWidth,b=(t=(n=window.visualViewport)==null?void 0:n.height)!=null?t:innerHeight,{scrollX:w,scrollY:y}=window,{height:v,width:F,top:M,right:D,bottom:V,left:_}=e.getBoundingClientRect(),{top:z,right:G,bottom:J,left:K}=(p=>{const s=window.getComputedStyle(p);return{top:parseFloat(s.scrollMarginTop)||0,right:parseFloat(s.scrollMarginRight)||0,bottom:parseFloat(s.scrollMarginBottom)||0,left:parseFloat(s.scrollMarginLeft)||0}})(e);let d=c==="start"||c==="nearest"?M-z:c==="end"?V+J:M+v/2-z+J,u=f==="center"?_+F/2-K+G:f==="end"?D+G:_-K;const A=[];for(let p=0;p<I.length;p++){const s=I[p],{height:O,width:W,top:N,right:X,bottom:Y,left:H}=s.getBoundingClientRect();if(i==="if-needed"&&M>=0&&_>=0&&V<=b&&D<=m&&(s===P&&!B(s)||M>=N&&V<=Y&&_>=H&&D<=X))return A;const R=getComputedStyle(s),j=parseInt(R.borderLeftWidth,10),E=parseInt(R.borderTopWidth,10),S=parseInt(R.borderRightWidth,10),T=parseInt(R.borderBottomWidth,10);let h=0,g=0;const x="offsetWidth"in s?s.offsetWidth-s.clientWidth-j-S:0,k="offsetHeight"in s?s.offsetHeight-s.clientHeight-E-T:0,$="offsetWidth"in s?s.offsetWidth===0?0:W/s.offsetWidth:0,q="offsetHeight"in s?s.offsetHeight===0?0:O/s.offsetHeight:0;if(P===s)h=c==="start"?d:c==="end"?d-b:c==="nearest"?C(y,y+b,b,E,T,y+d,y+d+v,v):d-b/2,g=f==="start"?u:f==="center"?u-m/2:f==="end"?u-m:C(w,w+m,m,j,S,w+u,w+u+F,F),h=Math.max(0,h+y),g=Math.max(0,g+w);else{h=c==="start"?d-N-E:c==="end"?d-Y+T+k:c==="nearest"?C(N,Y,O,E,T+k,d,d+v,v):d-(N+O/2)+k/2,g=f==="start"?u-H-j:f==="center"?u-(H+W/2)+x/2:f==="end"?u-X+S+x:C(H,X,W,j,S+x,u,u+F,F);const{scrollLeft:Q,scrollTop:U}=s;h=q===0?0:Math.max(0,Math.min(U+h/q,s.scrollHeight-O/q+k)),g=$===0?0:Math.max(0,Math.min(Q+g/$,s.scrollWidth-W/$+x)),d+=U-h,u+=Q-g}A.push({el:s,top:h,left:g})}return A},de=e=>e===!1?{block:"end",inline:"nearest"}:(o=>o===Object(o)&&Object.keys(o).length!==0)(e)?e:{block:"start",inline:"nearest"};function ue(e,o){if(!e.isConnected||!(n=>{let t=n;for(;t&&t.parentNode;){if(t.parentNode===document)return!0;t=t.parentNode instanceof ShadowRoot?t.parentNode.host:t.parentNode}return!1})(e))return;const r=(n=>{const t=window.getComputedStyle(n);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e);if((n=>typeof n=="object"&&typeof n.behavior=="function")(o))return o.behavior(oe(e,o));const l=typeof o=="boolean"||o==null?void 0:o.behavior;for(const{el:n,top:t,left:i}of oe(e,de(o))){const c=t-r.top+r.bottom,f=i-r.left+r.right;n.scroll({top:c,left:f,behavior:l})}}const he=["parentNode"],ge="form_item";function le(e){return e===void 0||e===!1?[]:Array.isArray(e)?e:[e]}function pe(e,o){if(!e.length)return;const r=e.join("_");return o?`${o}_${r}`:he.includes(r)?`${ge}_${r}`:r}function ve(e,o,r,l,n,t){let i=l;return t!==void 0?i=t:r.validating?i="validating":e.length?i="error":o.length?i="warning":(r.touched||n&&r.validated)&&(i="success"),i}var me=function(e,o){var r={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&o.indexOf(l)<0&&(r[l]=e[l]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,l=Object.getOwnPropertySymbols(e);n<l.length;n++)o.indexOf(l[n])<0&&Object.prototype.propertyIsEnumerable.call(e,l[n])&&(r[l[n]]=e[l[n]]);return r};function ne(e){return le(e).join("_")}function re(e,o){const r=o.getFieldInstance(e),l=fe(r);if(l)return l;const n=pe(le(e),o.__INTERNAL__.name);if(n)return document.getElementById(n)}function Fe(e){const[o]=ce(),r=Z.useRef({}),l=Z.useMemo(()=>e??Object.assign(Object.assign({},o),{__INTERNAL__:{itemRef:n=>t=>{const i=ne(n);t?r.current[i]=t:delete r.current[i]}},scrollToField:(n,t={})=>{const{focus:i}=t,c=me(t,["focus"]),f=re(n,l);f&&(ue(f,Object.assign({scrollMode:"if-needed",block:"nearest"},c)),i&&l.focusField(n))},focusField:n=>{var t,i;const c=l.getFieldInstance(n);typeof(c==null?void 0:c.focus)=="function"?c.focus():(i=(t=re(n,l))===null||t===void 0?void 0:t.focus)===null||i===void 0||i.call(t)},getFieldInstance:n=>{const t=ne(n);return r.current[t]}}),[e,o]);return[l]}export{le as a,pe as b,ve as g,ne as t,Fe as u};
