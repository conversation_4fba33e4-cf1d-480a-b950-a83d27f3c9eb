import{j as s}from"./hotel-item-D_zvdyIk.js";import{r as m}from"./PeopleSelectPopover-CDKo4AC-.js";function x({hotel:a,className:c,onFav:n}){const e=m.useMemo(()=>{var i,r;return[a.country,(r=(i=a.city)==null?void 0:i[0])==null?void 0:r.name].filter(d=>d).join("，")},[a]),l=a.brand_img||"";return s.jsxs("div",{className:`hotel-item ${c}`,children:[s.jsxs("div",{className:"cover-wrap",children:[s.jsx("img",{className:"bg",src:a.image}),s.jsx("div",{className:"hotel-image-mask"}),s.jsx("div",{className:"logo-wrap",children:l&&s.jsx("img",{src:l,alt:""})}),s.jsx("div",{onClick:i=>{i.preventDefault(),n()},className:"fav-icon",children:s.jsx("i",{className:`iconfont ${a.is_collect?"icon-Heart-filled":"icon-Heart"} text-white`})})]}),s.jsx("div",{className:"tag-wrap",style:{backgroundColor:a.gradient_color},children:s.jsx("p",{children:a.head_line||"Unknown Series"})}),s.jsxs("div",{className:"info-wrap",children:[s.jsx("a",{href:`/about/brands/${a.brand_id}`,className:"hover:underline",children:s.jsx("div",{className:"brand",children:a.brand_name})}),s.jsx("a",{href:`/hotel/${a.id}`,className:"hover:underline",children:s.jsx("h2",{className:"title",children:a.hotel_name})}),e&&s.jsx("h5",{className:"local",children:e}),s.jsx("div",{className:"spt-line"}),s.jsx("div",{className:"flex-1"}),s.jsxs("div",{className:"price-wrap",children:[s.jsxs("div",{className:"protrude",children:[s.jsx("p",{children:"会员价低至"}),s.jsx("p",{className:"price-num",children:"CNY1999"})]}),s.jsxs("div",{className:"",children:[s.jsx("p",{children:"会员价低至"}),s.jsx("p",{className:"price-num",children:"CNY2999"})]})]}),s.jsxs("div",{className:"action-wrap",children:[s.jsx("a",{href:"",className:"gha-primary-btn",children:"立即预订"}),s.jsx("a",{href:`/hotel/${a.id}`,className:"gha-btn",children:"酒店详情"})]})]})]})}function j({offer:a,className:c,onFav:n}){return s.jsxs("a",{href:`/offer/${a.id}`,className:`hotel-item ${c}`,children:[s.jsxs("div",{className:"cover-wrap",children:[s.jsx("img",{className:"bg",src:a.image,alt:""}),s.jsx("div",{className:"hotel-image-mask"}),s.jsx("div",{className:"logo-wrap",children:s.jsx("img",{src:a.brand_img||"https://cms.ghadiscovery.com/content/download/281/1261?version=31&inline=1",alt:""})}),s.jsx("div",{onClick:e=>{e.preventDefault(),n()},className:"fav-icon",children:s.jsx("i",{className:`iconfont ${a.is_collect?"icon-Heart-filled":"icon-Heart"} text-white`})})]}),s.jsx("div",{className:"tag-wrap",children:s.jsx("p",{children:a.head_line||"占位"})}),s.jsxs("div",{className:"info-wrap",children:[s.jsx("div",{className:"brand",children:a.brand_name||"品牌-占位"}),s.jsx("h2",{className:"title",children:a.title}),s.jsx("div",{className:"spt-line"}),s.jsxs("div",{className:"mt-3",children:[s.jsxs("div",{className:"discount-line",children:[s.jsx("i",{className:"iconfont icon-Location"}),a.country_name||"国家-占位",", ",a.city_name||"城市-占位"]}),s.jsxs("div",{className:"discount-line",children:[s.jsx("i",{className:"iconfont icon-Calendar"}),"预订期限至：",a.end_date]}),s.jsxs("div",{className:"discount-line",children:[s.jsx("i",{className:"iconfont icon-Room"}),"住宿期限：",a.stay_end_date," - ",a.stay_start_date]})]})]})]})}export{x as H,j as O};
