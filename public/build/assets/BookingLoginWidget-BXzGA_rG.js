import{j as s}from"./hotel-item-D_zvdyIk.js";function a(){return s.jsxs("div",{className:"booking-login-wrap",children:[s.jsx("h2",{className:"font-16 font-medium",children:"还不是 GHA探索之旅会员?今天免费加入。"}),s.jsxs("p",{className:"font-14 mt-1",children:["注册即可解锁全球 800多 家酒店的认可和奖励，无论入住与否。",s.jsx("a",{className:"underline",href:"javascript:;",children:"了解更多"})]}),s.jsxs("p",{className:"font-14 mt-1",children:[s.jsx("span",{className:"font-20 font-IvyMode-Reg mr-2",children:"D$"}),"在 800 家酒店赚取和消费 D$"]}),s.jsxs("div",{className:"flex flex-row justify-end mt-4",children:[s.jsx("a",{className:"gha-primary-btn mr-2 !px-8 bg-white text-black border-white",href:"javascript:;",children:"以访客继续"}),s.jsx("a",{className:"gha-primary-btn !px-8",href:"javascript:;",children:"注册并预定"})]})]})}export{a as B};
