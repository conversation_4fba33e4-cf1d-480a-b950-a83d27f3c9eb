import{c as _,h as b,e as se,_ as g,f as He}from"./genStyleUtils-CI3YU7Yv.js";import{r as n,R as qe}from"./PeopleSelectPopover-CDKo4AC-.js";import{K as S,i as Je}from"./ContextIsolator-CVrwwDX4.js";import{_ as P,w as Ze,a as et,r as tt}from"./isVisible-Bd4H7hpW.js";import{R as _e}from"./index-CaUFHQr4.js";const rt=new S("antSlideUpIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1}}),nt=new S("antSlideUpOut",{"0%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0}}),at=new S("antSlideDownIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1}}),it=new S("antSlideDownOut",{"0%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0}}),st=new S("antSlideLeftIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1}}),ot=new S("antSlideLeftOut",{"0%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0}}),ft=new S("antSlideRightIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1}}),lt=new S("antSlideRightOut",{"0%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0}}),ut={"slide-up":{inKeyframes:rt,outKeyframes:nt},"slide-down":{inKeyframes:at,outKeyframes:it},"slide-left":{inKeyframes:st,outKeyframes:ot},"slide-right":{inKeyframes:ft,outKeyframes:lt}},Ot=(e,s)=>{const{antCls:l}=e,i=`${l}-${s}`,{inKeyframes:f,outKeyframes:a}=ut[s];return[Je(i,f,a,e.motionDurationMid),{[`
      ${i}-enter,
      ${i}-appear
    `]:{transform:"scale(0)",transformOrigin:"0% 0%",opacity:0,animationTimingFunction:e.motionEaseOutQuint,"&-prepare":{transform:"scale(1)"}},[`${i}-leave`]:{animationTimingFunction:e.motionEaseInQuint}}]};var ct=["prefixCls","invalidate","item","renderItem","responsive","responsiveDisabled","registerSize","itemKey","className","style","children","display","order","component"],x=void 0;function mt(e,s){var l=e.prefixCls,i=e.invalidate,f=e.item,a=e.renderItem,u=e.responsive,p=e.responsiveDisabled,d=e.registerSize,C=e.itemKey,I=e.className,B=e.style,H=e.children,q=e.display,c=e.order,$=e.component,U=$===void 0?"div":$,K=P(e,ct),v=u&&!q;function X(R){d(C,R)}n.useEffect(function(){return function(){X(null)}},[]);var J=a&&f!==x?a(f,{index:c}):H,N;i||(N={opacity:v?0:1,height:v?0:x,overflowY:v?"hidden":x,order:u?c:x,pointerEvents:v?"none":x,position:v?"absolute":x});var A={};v&&(A["aria-hidden"]=!0);var E=n.createElement(U,_({className:se(!i&&l,I),style:b(b({},N),B)},A,K,{ref:s}),J);return u&&(E=n.createElement(_e,{onResize:function(Z){var Y=Z.offsetWidth;X(Y)},disabled:p},E)),E}var W=n.forwardRef(mt);W.displayName="Item";function dt(e){if(typeof MessageChannel>"u")Ze(e);else{var s=new MessageChannel;s.port1.onmessage=function(){return e()},s.port2.postMessage(void 0)}}function vt(){var e=n.useRef(null),s=function(i){e.current||(e.current=[],dt(function(){tt.unstable_batchedUpdates(function(){e.current.forEach(function(f){f()}),e.current=null})})),e.current.push(i)};return s}function M(e,s){var l=n.useState(s),i=g(l,2),f=i[0],a=i[1],u=et(function(p){e(function(){a(p)})});return[f,u]}var G=qe.createContext(null),yt=["component"],gt=["className"],pt=["className"],Rt=function(s,l){var i=n.useContext(G);if(!i){var f=s.component,a=f===void 0?"div":f,u=P(s,yt);return n.createElement(a,_({},u,{ref:l}))}var p=i.className,d=P(i,gt),C=s.className,I=P(s,pt);return n.createElement(G.Provider,{value:null},n.createElement(W,_({ref:l,className:se(p,C)},d,I)))},Ce=n.forwardRef(Rt);Ce.displayName="RawItem";var St=["prefixCls","data","renderItem","renderRawItem","itemKey","itemWidth","ssr","style","className","maxCount","renderRest","renderRawRest","suffix","component","itemComponent","onVisibleChange"],Ie="responsive",Ne="invalidate";function Et(e){return"+ ".concat(e.length," ...")}function ht(e,s){var l=e.prefixCls,i=l===void 0?"rc-overflow":l,f=e.data,a=f===void 0?[]:f,u=e.renderItem,p=e.renderRawItem,d=e.itemKey,C=e.itemWidth,I=C===void 0?10:C,B=e.ssr,H=e.style,q=e.className,c=e.maxCount,$=e.renderRest,U=e.renderRawRest,K=e.suffix,v=e.component,X=v===void 0?"div":v,J=e.itemComponent,N=e.onVisibleChange,A=P(e,St),E=B==="full",R=vt(),Z=M(R,null),Y=g(Z,2),F=Y[0],Oe=Y[1],h=F||0,xe=M(R,new Map),oe=g(xe,2),fe=oe[0],be=oe[1],Ke=M(R,0),le=g(Ke,2),De=le[0],ze=le[1],Me=M(R,0),ue=g(Me,2),L=ue[0],Pe=ue[1],We=M(R,0),ce=g(We,2),T=ce[0],$e=ce[1],Ue=n.useState(null),me=g(Ue,2),ee=me[0],de=me[1],Xe=n.useState(null),ve=g(Xe,2),V=ve[0],Ae=ve[1],O=n.useMemo(function(){return V===null&&E?Number.MAX_SAFE_INTEGER:V||0},[V,F]),Ye=n.useState(!1),ye=g(Ye,2),Fe=ye[0],Le=ye[1],te="".concat(i,"-item"),ge=Math.max(De,L),re=c===Ie,y=a.length&&re,pe=c===Ne,Te=y||typeof c=="number"&&a.length>c,w=n.useMemo(function(){var t=a;return y?F===null&&E?t=a:t=a.slice(0,Math.min(a.length,h/I)):typeof c=="number"&&(t=a.slice(0,c)),t},[a,I,F,c,y]),ne=n.useMemo(function(){return y?a.slice(O+1):a.slice(w.length)},[a,w,y,O]),k=n.useCallback(function(t,r){var o;return typeof d=="function"?d(t):(o=d&&(t==null?void 0:t[d]))!==null&&o!==void 0?o:r},[d]),Ve=n.useCallback(u||function(t){return t},[u]);function j(t,r,o){V===t&&(r===void 0||r===ee)||(Ae(t),o||(Le(t<a.length-1),N==null||N(t)),r!==void 0&&de(r))}function ke(t,r){Oe(r.clientWidth)}function Re(t,r){be(function(o){var m=new Map(o);return r===null?m.delete(t):m.set(t,r),m})}function je(t,r){Pe(r),ze(L)}function Ge(t,r){$e(r)}function ae(t){return fe.get(k(w[t],t))}He(function(){if(h&&typeof ge=="number"&&w){var t=T,r=w.length,o=r-1;if(!r){j(0,null);return}for(var m=0;m<r;m+=1){var z=ae(m);if(E&&(z=z||0),z===void 0){j(m-1,void 0,!0);break}if(t+=z,o===0&&t<=h||m===o-1&&t+ae(o)<=h){j(o,null);break}else if(t+ge>h){j(m-1,t-z-T+L);break}}K&&ae(0)+T>h&&de(null)}},[h,fe,L,T,k,w]);var Se=Fe&&!!ne.length,Ee={};ee!==null&&y&&(Ee={position:"absolute",left:ee,top:0});var D={prefixCls:te,responsive:y,component:J,invalidate:pe},Qe=p?function(t,r){var o=k(t,r);return n.createElement(G.Provider,{key:o,value:b(b({},D),{},{order:r,item:t,itemKey:o,registerSize:Re,display:r<=O})},p(t,r))}:function(t,r){var o=k(t,r);return n.createElement(W,_({},D,{order:r,key:o,item:t,renderItem:Ve,itemKey:o,registerSize:Re,display:r<=O}))},he={order:Se?O:Number.MAX_SAFE_INTEGER,className:"".concat(te,"-rest"),registerSize:je,display:Se},ie=$||Et,Be=U?n.createElement(G.Provider,{value:b(b({},D),he)},U(ne)):n.createElement(W,_({},D,he),typeof ie=="function"?ie(ne):ie),we=n.createElement(X,_({className:se(!pe&&i,q),style:H,ref:s},A),w.map(Qe),Te?Be:null,K&&n.createElement(W,_({},D,{responsive:re,responsiveDisabled:!y,order:O,className:"".concat(te,"-suffix"),registerSize:Ge,display:!0,style:Ee}),K));return re?n.createElement(_e,{onResize:ke,disabled:!y},we):we}var Q=n.forwardRef(ht);Q.displayName="Overflow";Q.Item=Ce;Q.RESPONSIVE=Ie;Q.INVALIDATE=Ne;export{Q as F,nt as a,at as b,rt as c,Ot as i,it as s};
