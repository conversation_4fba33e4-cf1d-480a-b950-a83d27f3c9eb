import{m as S,p as H,a as m,c as b}from"./genStyleUtils-CI3YU7Yv.js";import{r as l}from"./PeopleSelectPopover-CDKo4AC-.js";import{K as c,i as M}from"./ContextIsolator-CVrwwDX4.js";import{I as w,R as E}from"./button-C2fNxKeA.js";import{R as C}from"./pickAttrs-D1C8emUZ.js";import{R as D}from"./CloseOutlined-CGWqYTdG.js";import{R as z}from"./index-wg7qNA5H.js";const L=new c("antMoveDownIn",{"0%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),P=new c("antMoveDownOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0}}),q=new c("antMoveLeftIn",{"0%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),B=new c("antMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),K=new c("antMoveRightIn",{"0%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),N=new c("antMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),W=new c("antMoveUpIn",{"0%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),T=new c("antMoveUpOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0}}),_={"move-up":{inKeyframes:W,outKeyframes:T},"move-down":{inKeyframes:L,outKeyframes:P},"move-left":{inKeyframes:q,outKeyframes:B},"move-right":{inKeyframes:K,outKeyframes:N}},ae=(e,i)=>{const{antCls:t}=e,n=`${t}-${i}`,{inKeyframes:r,outKeyframes:a}=_[i];return[M(n,r,a,e.motionDurationMid),{[`
        ${n}-enter,
        ${n}-appear
      `]:{opacity:0,animationTimingFunction:e.motionEaseOutCirc},[`${n}-leave`]:{animationTimingFunction:e.motionEaseInOutCirc}}]},A=e=>{const{multipleSelectItemHeight:i,paddingXXS:t,lineWidth:n,INTERNAL_FIXED_ITEM_MARGIN:r}=e,a=e.max(e.calc(t).sub(n).equal(),0),s=e.max(e.calc(a).sub(r).equal(),0);return{basePadding:a,containerPadding:s,itemHeight:m(i),itemLineHeight:m(e.calc(i).sub(e.calc(e.lineWidth).mul(2)).equal())}},F=e=>{const{multipleSelectItemHeight:i,selectHeight:t,lineWidth:n}=e;return e.calc(t).sub(i).div(2).sub(n).equal()},G=e=>{const{componentCls:i,iconCls:t,borderRadiusSM:n,motionDurationSlow:r,paddingXS:a,multipleItemColorDisabled:s,multipleItemBorderColorDisabled:d,colorIcon:o,colorIconHover:I,INTERNAL_FIXED_ITEM_MARGIN:u}=e;return{[`${i}-selection-overflow`]:{position:"relative",display:"flex",flex:"auto",flexWrap:"wrap",maxWidth:"100%","&-item":{flex:"none",alignSelf:"center",maxWidth:"calc(100% - 4px)",display:"inline-flex"},[`${i}-selection-item`]:{display:"flex",alignSelf:"center",flex:"none",boxSizing:"border-box",maxWidth:"100%",marginBlock:u,borderRadius:n,cursor:"default",transition:`font-size ${r}, line-height ${r}, height ${r}`,marginInlineEnd:e.calc(u).mul(2).equal(),paddingInlineStart:a,paddingInlineEnd:e.calc(a).div(2).equal(),[`${i}-disabled&`]:{color:s,borderColor:d,cursor:"not-allowed"},"&-content":{display:"inline-block",marginInlineEnd:e.calc(a).div(2).equal(),overflow:"hidden",whiteSpace:"pre",textOverflow:"ellipsis"},"&-remove":Object.assign(Object.assign({},H()),{display:"inline-flex",alignItems:"center",color:o,fontWeight:"bold",fontSize:10,lineHeight:"inherit",cursor:"pointer",[`> ${t}`]:{verticalAlign:"-0.2em"},"&:hover":{color:I}})}}}},X=(e,i)=>{const{componentCls:t,INTERNAL_FIXED_ITEM_MARGIN:n}=e,r=`${t}-selection-overflow`,a=e.multipleSelectItemHeight,s=F(e),d=i?`${t}-${i}`:"",o=A(e);return{[`${t}-multiple${d}`]:Object.assign(Object.assign({},G(e)),{[`${t}-selector`]:{display:"flex",alignItems:"center",width:"100%",height:"100%",paddingInline:o.basePadding,paddingBlock:o.containerPadding,borderRadius:e.borderRadius,[`${t}-disabled&`]:{background:e.multipleSelectorBgDisabled,cursor:"not-allowed"},"&:after":{display:"inline-block",width:0,margin:`${m(n)} 0`,lineHeight:m(a),visibility:"hidden",content:'"\\a0"'}},[`${t}-selection-item`]:{height:o.itemHeight,lineHeight:m(o.itemLineHeight)},[`${t}-selection-wrap`]:{alignSelf:"flex-start","&:after":{lineHeight:m(a),marginBlock:n}},[`${t}-prefix`]:{marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(o.basePadding).equal()},[`${r}-item + ${r}-item,
        ${t}-prefix + ${t}-selection-wrap
      `]:{[`${t}-selection-search`]:{marginInlineStart:0},[`${t}-selection-placeholder`]:{insetInlineStart:0}},[`${r}-item-suffix`]:{minHeight:o.itemHeight,marginBlock:n},[`${t}-selection-search`]:{display:"inline-flex",position:"relative",maxWidth:"100%",marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(s).equal(),"\n          &-input,\n          &-mirror\n        ":{height:a,fontFamily:e.fontFamily,lineHeight:m(a),transition:`all ${e.motionDurationSlow}`},"&-input":{width:"100%",minWidth:4.1},"&-mirror":{position:"absolute",top:0,insetInlineStart:0,insetInlineEnd:"auto",zIndex:999,whiteSpace:"pre",visibility:"hidden"}},[`${t}-selection-placeholder`]:{position:"absolute",top:"50%",insetInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(o.basePadding).equal(),insetInlineEnd:e.inputPaddingHorizontalBase,transform:"translateY(-50%)",transition:`all ${e.motionDurationSlow}`}})}};function $(e,i){const{componentCls:t}=e,n=i?`${t}-${i}`:"",r={[`${t}-multiple${n}`]:{fontSize:e.fontSize,[`${t}-selector`]:{[`${t}-show-search&`]:{cursor:"text"}},[`
        &${t}-show-arrow ${t}-selector,
        &${t}-allow-clear ${t}-selector
      `]:{paddingInlineEnd:e.calc(e.fontSizeIcon).add(e.controlPaddingHorizontal).equal()}}};return[X(e,i),r]}const le=e=>{const{componentCls:i}=e,t=S(e,{selectHeight:e.controlHeightSM,multipleSelectItemHeight:e.multipleItemHeightSM,borderRadius:e.borderRadiusSM,borderRadiusSM:e.borderRadiusXS}),n=S(e,{fontSize:e.fontSizeLG,selectHeight:e.controlHeightLG,multipleSelectItemHeight:e.multipleItemHeightLG,borderRadius:e.borderRadiusLG,borderRadiusSM:e.borderRadius});return[$(e),$(t,"sm"),{[`${i}-multiple${i}-sm`]:{[`${i}-selection-placeholder`]:{insetInline:e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal()},[`${i}-selection-search`]:{marginInlineStart:2}}},$(n,"lg")]};var U={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"}}]},name:"check",theme:"outlined"},j=function(i,t){return l.createElement(w,b({},i,{ref:t,icon:U}))},Y=l.forwardRef(j),J={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"},Q=function(i,t){return l.createElement(w,b({},i,{ref:t,icon:J}))},V=l.forwardRef(Q);function oe({suffixIcon:e,clearIcon:i,menuItemSelectedIcon:t,removeIcon:n,loading:r,multiple:a,hasFeedback:s,prefixCls:d,showSuffixIcon:o,feedbackIcon:I,showArrow:u,componentName:y}){const O=i??l.createElement(C,null),f=h=>e===null&&!s&&!u?null:l.createElement(l.Fragment,null,o!==!1&&h,s&&I);let g=null;if(e!==void 0)g=f(e);else if(r)g=f(l.createElement(E,{spin:!0}));else{const h=`${d}-suffix`;g=({open:R,showSearch:x})=>f(R&&x?l.createElement(z,{className:h}):l.createElement(V,{className:h}))}let p=null;t!==void 0?p=t:a?p=l.createElement(Y,null):p=null;let v=null;return n!==void 0?v=n:v=l.createElement(D,null),{clearIcon:O,suffixIcon:g,itemIcon:p,removeIcon:v}}export{Y as R,G as a,A as b,V as c,le as g,ae as i,oe as u};
