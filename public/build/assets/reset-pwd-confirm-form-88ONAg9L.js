import{j as r}from"./hotel-item-D_zvdyIk.js";import{c as x}from"./client-CHYie6Ha.js";import{r as i}from"./PeopleSelectPopover-CDKo4AC-.js";import{G as h,u as f}from"./GhaConfigProvider-BB5IW5PM.js";import{$ as j}from"./_constants-CI6xcXYb.js";import{$ as w,a as b}from"./helper-D414uohx.js";import{F as s}from"./index-CC5HyPSW.js";import{I as m}from"./index-DNIuBGmG.js";import{B as _}from"./button-C2fNxKeA.js";import"./index-C_TizibV.js";import"./isVisible-Bd4H7hpW.js";import"./genStyleUtils-CI3YU7Yv.js";import"./CloseOutlined-CGWqYTdG.js";import"./KeyCode-lh1qUinJ.js";import"./pickAttrs-D1C8emUZ.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./index-DxcGRMc6.js";import"./index-DKvg8qd3.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./color-DKTup0-d.js";import"./useSize-CbUlsqBW.js";import"./zoom-BbRr1fkt.js";import"./extendsObject-78o_rR5W.js";import"./index-DR0pJ98a.js";import"./index-Dq7h7Pqt.js";import"./collapse-BbEVqHco.js";import"./useForm-DF1XNZWJ.js";import"./row-BiveCJJi.js";import"./useBreakpoint-D48_IWaH.js";import"./index-CJQq4WAU.js";import"./index-CaUFHQr4.js";import"./useMergedState-BDSe6zqT.js";import"./roundedArrow-DVJD-5zd.js";import"./index-wg7qNA5H.js";import"./Input-DCb0FIZx.js";function g(){const[n,l]=i.useState(1),[p,o]=i.useState(!1),c=f(),[d]=s.useForm();function u(t){o(!0);const a={token:window.__ServerVars__.token,email:window.__ServerVars__.email};b.authResetPassword({...t,...a}).subscribe(e=>{if(o(!1),e.status_code!==200){c.error(e.msg);return}l(2)})}return r.jsx("div",{className:"auth-box auth-box-400",children:r.jsx("div",{className:"form-wrap",children:n===1?r.jsxs(r.Fragment,{children:[r.jsx("h3",{className:"text-center",children:"输入新密码进行重置"}),r.jsx("div",{className:"mt-5",children:r.jsxs(s,{layout:"vertical",form:d,requiredMark:!1,onFinish:u,initialValues:w.isLaravelLocal()?{password:"123456Abc$",confirm_password:"123456Abc$"}:{},children:[r.jsx(s.Item,{label:"密码*",name:"password",rules:[{required:!0,message:"请输入您的密码"},{pattern:j.passwordPattern,message:"密码至少8位，包含字母、数字和符号，且首尾不能有空格"}],children:r.jsx(m.Password,{variant:"underlined",placeholder:"请输入密码"})}),r.jsx(s.Item,{label:"确认密码*",name:"confirm_password",dependencies:["password"],rules:[{required:!0,message:"请输入确认密码"},({getFieldValue:t})=>({validator(a,e){return!e||t("password")===e?Promise.resolve():Promise.reject(new Error("密码与确认密码不一致!"))}})],children:r.jsx(m.Password,{variant:"underlined",placeholder:"请输入确认密码"})}),r.jsx(s.Item,{label:null,children:r.jsx(_,{loading:p,className:"gha-primary-btn",type:"primary",shape:"round",block:!0,htmlType:"submit",children:"提交"})})]})})]}):r.jsxs("div",{className:"flex flex-col items-center justify-center",children:[r.jsx("i",{className:"iconfont icon-a-14Benefits_brand_benefits text-primary text-4xl"}),r.jsx("h2",{className:"font-bold text-lg mt-1",children:"重置密码成功"}),r.jsx("p",{className:"mt-2.5 text-xs mb-8",children:"请使用新密码重新进行登录"}),r.jsx("a",{href:window.__ServerVars__.loginUri,className:"gha-primary-btn w-full mt-5",children:"登录"})]})})})}x.createRoot(document.querySelector(".auth-box-wrap")).render(r.jsx(h,{children:r.jsx(g,{})}));
