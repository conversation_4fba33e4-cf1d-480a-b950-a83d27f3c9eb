import{R as f,r as p}from"./PeopleSelectPopover-CDKo4AC-.js";import{u as v,f as h}from"./genStyleUtils-CI3YU7Yv.js";const x=(e,s)=>{typeof(e==null?void 0:e.addEventListener)<"u"?e.addEventListener("change",s):typeof(e==null?void 0:e.addListener)<"u"&&e.addListener(s)},l=(e,s)=>{typeof(e==null?void 0:e.removeEventListener)<"u"?e.removeEventListener("change",s):typeof(e==null?void 0:e.removeListener)<"u"&&e.removeListener(s)},$=["xxl","xl","lg","md","sm","xs"],b=e=>({xs:`(max-width: ${e.screenXSMax}px)`,sm:`(min-width: ${e.screenSM}px)`,md:`(min-width: ${e.screenMD}px)`,lg:`(min-width: ${e.screenLG}px)`,xl:`(min-width: ${e.screenXL}px)`,xxl:`(min-width: ${e.screenXXL}px)`}),M=e=>{const s=e,t=[].concat($).reverse();return t.forEach((a,i)=>{const r=a.toUpperCase(),n=`screen${r}Min`,o=`screen${r}`;if(!(s[n]<=s[o]))throw new Error(`${n}<=${o} fails : !(${s[n]}<=${s[o]})`);if(i<t.length-1){const c=`screen${r}Max`;if(!(s[o]<=s[c]))throw new Error(`${o}<=${c} fails : !(${s[o]}<=${s[c]})`);const d=`screen${t[i+1].toUpperCase()}Min`;if(!(s[c]<=s[d]))throw new Error(`${c}<=${d} fails : !(${s[c]}<=${s[d]})`)}}),e},w=()=>{const[,e]=v(),s=b(M(e));return f.useMemo(()=>{const t=new Map;let a=-1,i={};return{responsiveMap:s,matchHandlers:{},dispatch(r){return i=r,t.forEach(n=>n(i)),t.size>=1},subscribe(r){return t.size||this.register(),a+=1,t.set(a,r),r(i),a},unsubscribe(r){t.delete(r),t.size||this.unregister()},register(){Object.entries(s).forEach(([r,n])=>{const o=({matches:u})=>{this.dispatch(Object.assign(Object.assign({},i),{[r]:u}))},c=window.matchMedia(n);x(c,o),this.matchHandlers[n]={mql:c,listener:o},o(c)})},unregister(){Object.values(s).forEach(r=>{const n=this.matchHandlers[r];l(n==null?void 0:n.mql,n==null?void 0:n.listener)}),t.clear()}}},[e])};function L(){const[,e]=p.useReducer(s=>s+1,0);return e}function m(e=!0,s={}){const t=p.useRef(s),a=L(),i=w();return h(()=>{const r=i.subscribe(n=>{t.current=n,e&&a()});return()=>i.unsubscribe(r)},[]),t.current}export{L as a,$ as r,m as u};
