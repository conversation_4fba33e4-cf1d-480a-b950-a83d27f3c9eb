import{j as p}from"./hotel-item-D_zvdyIk.js";import{c as e}from"./client-CHYie6Ha.js";import{b as a}from"./GhaSearchBar-CE-42mVw.js";import{$ as s}from"./helper-D414uohx.js";import"./PeopleSelectPopover-CDKo4AC-.js";import"./index-C_TizibV.js";import"./index-CosgXXok.js";import"./PurePanel-zvE2p4pp.js";import"./useMergedState-BDSe6zqT.js";import"./genStyleUtils-CI3YU7Yv.js";import"./isVisible-Bd4H7hpW.js";import"./button-C2fNxKeA.js";import"./index-DKvg8qd3.js";import"./useSize-CbUlsqBW.js";import"./color-DKTup0-d.js";import"./pickAttrs-D1C8emUZ.js";import"./index-CaUFHQr4.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./Overflow-YL9RZFGj.js";import"./index-wg7qNA5H.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./useIcons-BIWMCRV_.js";import"./CloseOutlined-CGWqYTdG.js";import"./roundedArrow-DVJD-5zd.js";import"./index-BBp8sG_H.js";import"./KeyCode-lh1qUinJ.js";import"./zoom-BbRr1fkt.js";import"./react-ZNplgrTR.js";import"./index-PBsuAvuu.js";import"./index-CJQq4WAU.js";import"./zh-cn-DO3l3KOs.js";import"./index-DNIuBGmG.js";import"./Input-DCb0FIZx.js";import"./mock-OVdH_lkV.js";import"./index-Dq7h7Pqt.js";e.createRoot(document.querySelector(".discount-search-bar")).render(p.jsx(a,{sticky:!0,onSubmit:({keyword:r,offerType:o,date:t})=>{const m={keyword:r,offerType:o,date:t.map(i=>i.format("YYYY-MM-DD"))};location.href=`/search/offers#/?${s.encodeSearchHash(m)}`}}));
