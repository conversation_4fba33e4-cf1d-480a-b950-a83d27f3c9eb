import{j as e}from"./hotel-item-D_zvdyIk.js";import{c as s}from"./client-CHYie6Ha.js";import{O as r}from"./OrderStep-CbkYDCzs.js";import{O as i}from"./OrderDetailWidget-B4oVGJh8.js";import{O as t}from"./OrderPriceDetailWidget-fjlxL2dt.js";import{G as m}from"./GhaConfigProvider-BB5IW5PM.js";import{C as o}from"./index-DoO4tVvP.js";import"./PeopleSelectPopover-CDKo4AC-.js";import"./index-C_TizibV.js";import"./index-DVwr87ys.js";import"./index-DxcGRMc6.js";import"./genStyleUtils-CI3YU7Yv.js";import"./isVisible-Bd4H7hpW.js";import"./index-DKvg8qd3.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./button-C2fNxKeA.js";import"./useSize-CbUlsqBW.js";import"./color-DKTup0-d.js";import"./pickAttrs-D1C8emUZ.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./zoom-BbRr1fkt.js";import"./CloseOutlined-CGWqYTdG.js";import"./KeyCode-lh1qUinJ.js";import"./extendsObject-78o_rR5W.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./index-DR0pJ98a.js";import"./PurePanel-zvE2p4pp.js";import"./useMergedState-BDSe6zqT.js";function a(){return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"py-12",children:e.jsx(r,{step:3})}),e.jsx("div",{className:"",children:e.jsx("div",{className:"g-main-content-sm",children:e.jsx("div",{className:"text-[#999] font-14",children:e.jsxs("a",{href:"javascript:;",className:"flex flex-row items-center",children:[e.jsx("i",{className:"iconfont icon-left font-20"}),"返回"]})})})}),e.jsx("div",{className:"mt-3",children:e.jsx("div",{className:"g-main-content-sm",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx(i,{title:"请核对您的预订信息",hasUserInfo:!0}),e.jsx(t,{})]})})}),e.jsx("div",{className:"mt-4",children:e.jsx("div",{className:"g-main-content-sm",children:e.jsxs(o,{children:["按立即预订即表示您同意您已阅读",e.jsx("a",{href:"javascript:;",className:"underline",children:"条款和条件"}),"，您的信用卡将不会被收取费用，并且仅用于保证预订，除非房价说明中另有说明。"]})})}),e.jsx("div",{className:"mt-8",children:e.jsx("div",{className:"g-main-content-sm",children:e.jsxs("div",{className:"flex flex-col md:flex-row items-center justify-between",children:[e.jsxs("p",{className:"font-12 text-[#320e5e] mb-4 md:mb-0",children:["通过此次预订，您最多可赚取",e.jsx("span",{className:"text-[#e69d4a]",children:"D$219"})]}),e.jsx("a",{href:"/booking/booking-success",className:"gha-primary-btn w-full md:w-auto md:!px-10",children:"下一步,确认订单"})]})})})]})}s.createRoot(document.querySelector(".confirm-order-content")).render(e.jsx(m,{children:e.jsx(a,{})}));
