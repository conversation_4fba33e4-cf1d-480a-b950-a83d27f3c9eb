import{z as Q,y as K,l as b,u as X,D as _,_ as h,n as G,f as w,E as z,G as x,t as Y,H as Z,h as J}from"./genStyleUtils-CI3YU7Yv.js";import{R as C,r as u,b as V}from"./PeopleSelectPopover-CDKo4AC-.js";import{r as ee}from"./isVisible-Bd4H7hpW.js";import{N as te}from"./context-Gzj2nObQ.js";import{N as ne}from"./color-DKTup0-d.js";var $e=function(){function e(t,n){K(this,e),b(this,"name",void 0),b(this,"style",void 0),b(this,"_keyframe",!0),this.name=t,this.style=n}return Q(e,[{key:"getName",value:function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return n?"".concat(n,"-").concat(this.name):this.name}}]),e}();const ae=C.createContext(void 0),d=100,re=10,Ee=d*re,L={Modal:d,Drawer:d,Popover:d,Popconfirm:d,Tooltip:d,Tour:d,FloatButton:d},oe={SelectLike:50,Dropdown:50,DatePicker:50,Menu:50,ImagePreview:1};function ie(e){return e in L}const Ie=(e,t)=>{const[,n]=X(),a=C.useContext(ae),r=ie(e);let o;if(t!==void 0)o=[t,t];else{let i=a??0;r?i+=(a?0:n.zIndexPopupBase)+L[e]:i+=oe[e],o=[a===void 0?t:i,i]}return o},ue=e=>({animationDuration:e,animationFillMode:"both"}),ce=e=>({animationDuration:e,animationFillMode:"both"}),be=(e,t,n,a,r=!1)=>{const o=r?"&":"";return{[`
      ${o}${e}-enter,
      ${o}${e}-appear
    `]:Object.assign(Object.assign({},ue(a)),{animationPlayState:"paused"}),[`${o}${e}-leave`]:Object.assign(Object.assign({},ce(a)),{animationPlayState:"paused"}),[`
      ${o}${e}-enter${e}-enter-active,
      ${o}${e}-appear${e}-appear-active
    `]:{animationName:t,animationPlayState:"running"},[`${o}${e}-leave${e}-leave-active`]:{animationName:n,animationPlayState:"running",pointerEvents:"none"}}};var H=u.createContext(null),O=[];function se(e,t){var n=u.useState(function(){if(!_())return null;var c=document.createElement("div");return c}),a=h(n,1),r=a[0],o=u.useRef(!1),i=u.useContext(H),s=u.useState(O),f=h(s,2),l=f[0],p=f[1],S=i||(o.current?void 0:function(c){p(function(m){var $=[c].concat(G(m));return $})});function v(){r.parentElement||document.body.appendChild(r),o.current=!0}function g(){var c;(c=r.parentElement)===null||c===void 0||c.removeChild(r),o.current=!1}return w(function(){return e?i?i(v):v():g(),g},[e]),w(function(){l.length&&(l.forEach(function(c){return c()}),p(O))},[l]),[r,S]}var R;function W(e){var t="rc-scrollbar-measure-".concat(Math.random().toString(36).substring(7)),n=document.createElement("div");n.id=t;var a=n.style;a.position="absolute",a.left="0",a.top="0",a.width="100px",a.height="100px",a.overflow="scroll";var r,o;if(e){var i=getComputedStyle(e);a.scrollbarColor=i.scrollbarColor,a.scrollbarWidth=i.scrollbarWidth;var s=getComputedStyle(e,"::-webkit-scrollbar"),f=parseInt(s.width,10),l=parseInt(s.height,10);try{var p=f?"width: ".concat(s.width,";"):"",S=l?"height: ".concat(s.height,";"):"";z(`
#`.concat(t,`::-webkit-scrollbar {
`).concat(p,`
`).concat(S,`
}`),t)}catch(c){console.error(c),r=f,o=l}}document.body.appendChild(n);var v=e&&r&&!isNaN(r)?r:n.offsetWidth-n.clientWidth,g=e&&o&&!isNaN(o)?o:n.offsetHeight-n.clientHeight;return document.body.removeChild(n),x(t),{width:v,height:g}}function Re(e){return typeof document>"u"?0:(R===void 0&&(R=W()),R.width)}function le(e){return typeof document>"u"||!e||!(e instanceof Element)?{width:0,height:0}:W(e)}function de(){return document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight)&&window.innerWidth>document.body.offsetWidth}var fe="rc-util-locker-".concat(Date.now()),k=0;function ve(e){var t=!!e,n=u.useState(function(){return k+=1,"".concat(fe,"_").concat(k)}),a=h(n,1),r=a[0];w(function(){if(t){var o=le(document.body).width,i=de();z(`
html body {
  overflow-y: hidden;
  `.concat(i?"width: calc(100% - ".concat(o,"px);"):"",`
}`),r)}else x(r);return function(){x(r)}},[t,r])}var me=!1;function he(e){return me}var T=function(t){return t===!1?!1:!_()||!t?null:typeof t=="string"?document.querySelector(t):typeof t=="function"?t():t},we=u.forwardRef(function(e,t){var n=e.open,a=e.autoLock,r=e.getContainer;e.debug;var o=e.autoDestroy,i=o===void 0?!0:o,s=e.children,f=u.useState(n),l=h(f,2),p=l[0],S=l[1],v=p||n;u.useEffect(function(){(i||n)&&S(n)},[n,i]);var g=u.useState(function(){return T(r)}),c=h(g,2),m=c[0],$=c[1];u.useEffect(function(){var I=T(r);$(I??null)});var A=se(v&&!m),N=h(A,2),P=N[0],B=N[1],y=m??P;ve(a&&n&&_()&&(y===P||y===document.body));var D=null;if(s&&Y(s)&&t){var j=s;D=j.ref}var q=Z(D,t);if(!v||!_()||m===void 0)return null;var U=y===!1||he(),E=s;return t&&(E=u.cloneElement(s,{ref:q})),u.createElement(H.Provider,{value:B},U?E:ee.createPortal(E,y))});function pe(){var e=J({},V);return e.useId}var M=0,F=pe();const xe=F?function(t){var n=F();return t||n}:function(t){var n=u.useState("ssr-id"),a=h(n,2),r=a[0],o=a[1];return u.useEffect(function(){var i=M;M+=1,o("rc_unique_".concat(i))},[]),t||r},Ne=e=>{const{space:t,form:n,children:a}=e;if(a==null)return null;let r=a;return n&&(r=C.createElement(te,{override:!0,status:!0},r)),t&&(r=C.createElement(ne,null,r)),r};export{Ne as C,$e as K,we as P,Ie as a,Ee as b,le as c,Re as g,be as i,xe as u,ae as z};
