import{j as e}from"./hotel-item-D_zvdyIk.js";import{r}from"./PeopleSelectPopover-CDKo4AC-.js";import{M as i}from"./index-DVwr87ys.js";import{B as o}from"./button-C2fNxKeA.js";import"./index-DxcGRMc6.js";import"./genStyleUtils-CI3YU7Yv.js";import"./isVisible-Bd4H7hpW.js";import"./index-C_TizibV.js";import"./index-DKvg8qd3.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./pickAttrs-D1C8emUZ.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./color-DKTup0-d.js";import"./useSize-CbUlsqBW.js";import"./zoom-BbRr1fkt.js";import"./CloseOutlined-CGWqYTdG.js";import"./KeyCode-lh1qUinJ.js";import"./extendsObject-78o_rR5W.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./index-DR0pJ98a.js";import"./PurePanel-zvE2p4pp.js";import"./useMergedState-BDSe6zqT.js";const R=r.forwardRef(({},s)=>{const[a,t]=r.useState(!1);r.useImperativeHandle(s,()=>({open(){t(!0)}}));const m=e.jsxs(e.Fragment,{children:[e.jsx("h3",{className:"font-20 font-bold mb-3",children:"取消订单"}),e.jsx("p",{children:"不迟于抵达前24小时取消，否则将收取一晚的取消费用。"}),e.jsx("p",{className:"mt-1",children:"您确定要取消订单吗？"}),e.jsxs("div",{className:"flex flex-row items-center justify-center -mx-2 mt-6",children:[e.jsx(o,{onClick:()=>t(!1),className:"gha-btn medium w-40 mx-2",children:"再考虑一下"}),e.jsx(o,{type:"primary",className:"gha-primary-btn medium w-40 mx-2",children:"确定"})]})]});return e.jsx(i,{open:a,footer:null,width:500,closable:!1,destroyOnHidden:!0,transitionName:"ant-fade",rootClassName:"gha-antd-modal",centered:!0,children:e.jsxs("div",{className:"gha-antd-modal-wrapper",children:[e.jsx("div",{className:"close-icon",onClick:()=>t(!1),children:e.jsx("i",{className:"iconfont icon-Close"})}),e.jsx("div",{className:"gha-antd-modal-content",children:e.jsx("div",{className:"px-8 text-center font-14",children:m})})]})})});export{R as C};
