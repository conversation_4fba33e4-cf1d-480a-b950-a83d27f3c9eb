import{j as r}from"./hotel-item-D_zvdyIk.js";import{c as t}from"./client-CHYie6Ha.js";import"./App-lctOXV6G.js";import{c as s}from"./GhaSearchBar-CE-42mVw.js";import{O as o}from"./OrderStep-CbkYDCzs.js";import{R as m}from"./RoomItem-WAqGpIuw.js";import{r as a}from"./PeopleSelectPopover-CDKo4AC-.js";import"./index-C_TizibV.js";import"./index-CosgXXok.js";import"./PurePanel-zvE2p4pp.js";import"./useMergedState-BDSe6zqT.js";import"./genStyleUtils-CI3YU7Yv.js";import"./isVisible-Bd4H7hpW.js";import"./button-C2fNxKeA.js";import"./index-DKvg8qd3.js";import"./useSize-CbUlsqBW.js";import"./color-DKTup0-d.js";import"./pickAttrs-D1C8emUZ.js";import"./index-CaUFHQr4.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./Overflow-YL9RZFGj.js";import"./index-wg7qNA5H.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./useIcons-BIWMCRV_.js";import"./CloseOutlined-CGWqYTdG.js";import"./roundedArrow-DVJD-5zd.js";import"./zh-cn-DO3l3KOs.js";import"./index-BBp8sG_H.js";import"./KeyCode-lh1qUinJ.js";import"./zoom-BbRr1fkt.js";import"./react-ZNplgrTR.js";import"./index-PBsuAvuu.js";import"./index-CJQq4WAU.js";import"./helper-D414uohx.js";import"./index-Dq7h7Pqt.js";import"./index-DNIuBGmG.js";import"./Input-DCb0FIZx.js";import"./mock-OVdH_lkV.js";import"./RoomDetailModal-DcnbQpA2.js";import"./index-DVwr87ys.js";import"./index-DxcGRMc6.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./extendsObject-78o_rR5W.js";import"./index-DR0pJ98a.js";function c(){const[i]=a.useState(!0);return r.jsxs(r.Fragment,{children:[r.jsxs("div",{className:"hotel-top gha-bg-test",children:[r.jsx("div",{className:"absolute top-0 left-0 right-0 bottom-0",style:{backgroundImage:"linear-gradient(90deg, rgba(0, 93, 121, 1) 50%, rgba(111, 202, 221, 0) 80%)"}}),r.jsxs("div",{className:"g-main-content-sm relative z-20",children:[r.jsxs("div",{className:"flex flex-row items-center",children:[r.jsx("div",{className:"logo",children:r.jsx("img",{src:"https://cms.ghadiscovery.com/content/download/560/2541?version=27&inline=1",alt:""})}),r.jsx("h1",{children:"长沙玛珂酒店"})]}),i?r.jsxs("div",{className:"edit-tip",children:[r.jsx("p",{children:"重新选择房型"}),r.jsx("p",{children:"订单号：GHA12345678"})]}):r.jsx("div",{className:"back",children:r.jsxs("a",{href:"javascript:;",children:[r.jsx("i",{className:"iconfont icon-left"}),"返回"]})})]})]}),r.jsx("div",{className:"-mt-[39px] relative",children:r.jsx("div",{className:"g-main-content-sm",children:r.jsx("div",{className:"shadow-md border border-[#ebebeb]/20 rounded-xl overflow-hidden",children:r.jsx(s,{sticky:!0})})})}),r.jsx("div",{className:"py-12",children:r.jsx(o,{})}),r.jsx("div",{className:"",children:r.jsx("div",{className:"g-main-content-sm",children:r.jsxs("div",{className:"bg-[#bbb0dd]/80 rounded-xl text-white font-13 flex flex-row items-center px-3 md:px-8 py-3",children:[r.jsx("i",{className:"iconfont icon-a-FrontDesk font-40 mr-2"}),r.jsxs("div",{children:["为符合条件的会员和另一位入住同一房间的客人提供免费早餐，提升您的住宿体验。直接预订时，探索之旅Titanium会员专享。",r.jsx("br",{}),r.jsx("a",{href:"javascript:;",className:"underline",children:"条款和条件"}),"适用。"]})]})})}),r.jsx("div",{className:"py-12",children:r.jsx("div",{className:"g-main-content-sm",children:new Array(3).fill(0).map((n,e)=>r.jsx(m,{},e))})}),r.jsx("div",{className:"text-center",children:r.jsx("a",{href:"javascript:;",className:"gha-btn",children:"显示更多"})})]})}t.createRoot(document.querySelector(".select-room-content")).render(r.jsx(c,{}));
