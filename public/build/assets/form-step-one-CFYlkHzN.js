import{j as r}from"./hotel-item-D_zvdyIk.js";import{$ as n}from"./_constants-CI6xcXYb.js";import{$ as u,a as h}from"./helper-D414uohx.js";import{r as f}from"./PeopleSelectPopover-CDKo4AC-.js";import{u as x}from"./GhaConfigProvider-BB5IW5PM.js";import{F as e}from"./index-CC5HyPSW.js";import{I as s}from"./index-DNIuBGmG.js";import{B as j}from"./button-C2fNxKeA.js";import"./index-Dq7h7Pqt.js";import"./isVisible-Bd4H7hpW.js";import"./genStyleUtils-CI3YU7Yv.js";import"./index-C_TizibV.js";import"./CloseOutlined-CGWqYTdG.js";import"./KeyCode-lh1qUinJ.js";import"./pickAttrs-D1C8emUZ.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./index-DxcGRMc6.js";import"./index-DKvg8qd3.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./color-DKTup0-d.js";import"./useSize-CbUlsqBW.js";import"./zoom-BbRr1fkt.js";import"./extendsObject-78o_rR5W.js";import"./index-DR0pJ98a.js";import"./collapse-BbEVqHco.js";import"./useForm-DF1XNZWJ.js";import"./row-BiveCJJi.js";import"./useBreakpoint-D48_IWaH.js";import"./index-CJQq4WAU.js";import"./index-CaUFHQr4.js";import"./useMergedState-BDSe6zqT.js";import"./roundedArrow-DVJD-5zd.js";import"./index-wg7qNA5H.js";import"./Input-DCb0FIZx.js";function rr({onNext:a}){const[p]=e.useForm(),[l,o]=f.useState(!1),d=x();function c(t){o(!0),h.authActiveAccount(t).subscribe(i=>{if(o(!1),i.status_code!==200){d.error(i.message);return}a&&a()})}return r.jsxs(e,{layout:"vertical",form:p,requiredMark:!1,onFinish:c,initialValues:u.isLaravelLocal()?{membershipCardNo:"123456",email:"<EMAIL>",password:"123456Abc$",confirm_password:"123456Abc$"}:{},children:[r.jsx(e.Item,{label:r.jsx(r.Fragment,{children:"会员号*"}),name:"membershipCardNo",rules:[{required:!0,message:"请输入您的会员号"}],children:r.jsx(s,{variant:"underlined",placeholder:"请输入您的会员号"})}),r.jsxs("p",{className:"flex flex-row justify-between -mt-3.5 mb-5",children:[r.jsx("span",{className:"text-[#919191]",children:"会员号可在我们发的电子邮件顶部找到。"}),r.jsx("a",{href:window.__ServerVars__.forgetAccountUri,className:"underline flex-shrink-0",children:"忘记会员号？"})]}),r.jsx(e.Item,{label:"邮箱*",name:"email",rules:[{required:!0,message:"请输入您的邮箱"},{pattern:n.emailPattern,message:"邮箱格式不正确"}],children:r.jsx(s,{variant:"underlined",placeholder:"请输入您的邮箱"})}),r.jsx(e.Item,{label:"密码*",name:"password",rules:[{required:!0,message:"请输入您的密码"},{pattern:n.passwordPattern,message:"密码至少8位，包含字母、数字和符号，且首尾不能有空格"}],children:r.jsx(s.Password,{variant:"underlined",placeholder:"请输入您的密码"})}),r.jsx(e.Item,{label:"确认密码*",name:"confirm_password",dependencies:["password"],rules:[{required:!0,message:"请输入确认密码"},({getFieldValue:t})=>({validator(i,m){return!m||t("password")===m?Promise.resolve():Promise.reject(new Error("密码与确认密码不一致!"))}})],children:r.jsx(s.Password,{variant:"underlined",placeholder:"请输入确认密码"})}),r.jsx(e.Item,{label:null,children:r.jsx(j,{className:"gha-primary-btn",loading:l,type:"primary",shape:"round",block:!0,htmlType:"submit",children:"提交"})})]})}export{rr as F};
