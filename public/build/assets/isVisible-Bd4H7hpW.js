import{Y as Vn,q as X,A as Dn,Z as Fn,$ as In,a0 as Kn,a1 as Un,n as Nt,h as g,D as Le,E as Gn,F as ze,I as gt,S as it,_ as H,u as Vt,a2 as Yn,a3 as Wn,a4 as yt,P as Dt,w as Ft,x as It,y as Kt,z as Ut,l as V,N as zn,e as qn,t as Qn,v as Bn,T as ht,c as Hn,C as st,a5 as Gt,a6 as Zn,a7 as Jn,a8 as bt,a9 as Xn,aa as er,ab as tr,d as nr}from"./genStyleUtils-CI3YU7Yv.js";import{R as fe,g as rr,r as l,b as ar}from"./PeopleSelectPopover-CDKo4AC-.js";import{r as or}from"./index-C_TizibV.js";function ir(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const a in r)if(a!=="default"&&!(a in e)){const c=Object.getOwnPropertyDescriptor(r,a);c&&Object.defineProperty(e,a,c.get?c:{enumerable:!0,get:()=>r[a]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}function Et(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=[];return fe.Children.forEach(e,function(r){r==null&&!t.keepEmpty||(Array.isArray(r)?n=n.concat(Et(r)):Vn(r)&&r.props?n=n.concat(Et(r.props.children,t)):n.push(r))}),n}var Yt=or();const Be=rr(Yt),ha=ir({__proto__:null,default:Be},[Yt]);function St(e){return e instanceof HTMLElement||e instanceof SVGElement}function sr(e){return e&&X(e)==="object"&&St(e.nativeElement)?e.nativeElement:St(e)?e:null}function cr(e){var t=sr(e);if(t)return t;if(e instanceof fe.Component){var n;return(n=Be.findDOMNode)===null||n===void 0?void 0:n.call(Be,e)}return null}var Wt=function(t){return+setTimeout(t,16)},zt=function(t){return clearTimeout(t)};typeof window<"u"&&"requestAnimationFrame"in window&&(Wt=function(t){return window.requestAnimationFrame(t)},zt=function(t){return window.cancelAnimationFrame(t)});var Ct=0,ct=new Map;function qt(e){ct.delete(e)}var He=function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;Ct+=1;var r=Ct;function a(c){if(c===0)qt(r),t();else{var s=Wt(function(){a(c-1)});ct.set(r,s)}}return a(n),r};He.cancel=function(e){var t=ct.get(e);return qt(e),zt(t)};function ur(e,t){if(e==null)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)!==-1)continue;n[r]=e[r]}return n}function Ze(e,t){if(e==null)return{};var n,r,a=ur(e,t);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(e);for(r=0;r<c.length;r++)n=c[r],t.indexOf(n)===-1&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function lr(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,r=new Set;function a(c,s){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,u=r.has(c);if(Dn(!u,"Warning: There may be circular references"),u)return!1;if(c===s)return!0;if(n&&i>1)return!1;r.add(c);var o=i+1;if(Array.isArray(c)){if(!Array.isArray(s)||c.length!==s.length)return!1;for(var f=0;f<c.length;f++)if(!a(c[f],s[f],o))return!1;return!0}if(c&&s&&X(c)==="object"&&X(s)==="object"){var d=Object.keys(c);return d.length!==Object.keys(s).length?!1:d.every(function(v){return a(c[v],s[v],o)})}return!1}return a(e,t)}var fr=l.createContext({});function dr(e){return Fn(e)||In(e)||Kn(e)||Un()}function Je(e,t){for(var n=e,r=0;r<t.length;r+=1){if(n==null)return;n=n[t[r]]}return n}function Qt(e,t,n,r){if(!t.length)return n;var a=dr(t),c=a[0],s=a.slice(1),i;return!e&&typeof c=="number"?i=[]:Array.isArray(e)?i=Nt(e):i=g({},e),r&&n===void 0&&s.length===1?delete i[c][s[0]]:i[c]=Qt(i[c],s,n,r),i}function qe(e,t,n){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;return t.length&&r&&n===void 0&&!Je(e,t.slice(0,-1))?e:Qt(e,t,n,r)}function vr(e){return X(e)==="object"&&e!==null&&Object.getPrototypeOf(e)===Object.prototype}function xt(e){return Array.isArray(e)?[]:{}}var mr=typeof Reflect>"u"?Object.keys:Reflect.ownKeys;function pr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=xt(t[0]);return t.forEach(function(a){function c(s,i){var u=new Set(i),o=Je(a,s),f=Array.isArray(o);if(f||vr(o)){if(!u.has(o)){u.add(o);var d=Je(r,s);f?r=qe(r,s,[]):(!d||X(d)!=="object")&&(r=qe(r,s,xt(o))),mr(o).forEach(function(v){c([].concat(Nt(s),[v]),u)})}}else r=qe(r,s,o)}c([])}),r}function gr(){}const yr=l.createContext({}),hr=()=>{const e=()=>{};return e.deprecated=gr,e},br=l.createContext(void 0);var Er={items_per_page:"/ page",jump_to:"Go to",jump_to_confirm:"confirm",page:"Page",prev_page:"Previous Page",next_page:"Next Page",prev_5:"Previous 5 Pages",next_5:"Next 5 Pages",prev_3:"Previous 3 Pages",next_3:"Next 3 Pages",page_size:"Page Size"},Sr={yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0},Cr=g(g({},Sr),{},{locale:"en_US",today:"Today",now:"Now",backToToday:"Back to today",ok:"OK",clear:"Clear",week:"Week",month:"Month",year:"Year",timeSelect:"select time",dateSelect:"select date",weekSelect:"Choose a week",monthSelect:"Choose a month",yearSelect:"Choose a year",decadeSelect:"Choose a decade",dateFormat:"M/D/YYYY",dateTimeFormat:"M/D/YYYY HH:mm:ss",previousMonth:"Previous month (PageUp)",nextMonth:"Next month (PageDown)",previousYear:"Last year (Control + left)",nextYear:"Next year (Control + right)",previousDecade:"Last decade",nextDecade:"Next decade",previousCentury:"Last century",nextCentury:"Next century"});const Bt={placeholder:"Select time",rangePlaceholder:["Start time","End time"]},Pt={lang:Object.assign({placeholder:"Select date",yearPlaceholder:"Select year",quarterPlaceholder:"Select quarter",monthPlaceholder:"Select month",weekPlaceholder:"Select week",rangePlaceholder:["Start date","End date"],rangeYearPlaceholder:["Start year","End year"],rangeQuarterPlaceholder:["Start quarter","End quarter"],rangeMonthPlaceholder:["Start month","End month"],rangeWeekPlaceholder:["Start week","End week"]},Cr),timePickerLocale:Object.assign({},Bt)},F="${label} is not a valid ${type}",Ne={locale:"en",Pagination:Er,DatePicker:Pt,TimePicker:Bt,Calendar:Pt,global:{placeholder:"Please select",close:"Close"},Table:{filterTitle:"Filter menu",filterConfirm:"OK",filterReset:"Reset",filterEmptyText:"No filters",filterCheckAll:"Select all items",filterSearchPlaceholder:"Search in filters",emptyText:"No data",selectAll:"Select current page",selectInvert:"Invert current page",selectNone:"Clear all data",selectionAll:"Select all data",sortTitle:"Sort",expand:"Expand row",collapse:"Collapse row",triggerDesc:"Click to sort descending",triggerAsc:"Click to sort ascending",cancelSort:"Click to cancel sorting"},Tour:{Next:"Next",Previous:"Previous",Finish:"Finish"},Modal:{okText:"OK",cancelText:"Cancel",justOkText:"OK"},Popconfirm:{okText:"OK",cancelText:"Cancel"},Transfer:{titles:["",""],searchPlaceholder:"Search here",itemUnit:"item",itemsUnit:"items",remove:"Remove",selectCurrent:"Select current page",removeCurrent:"Remove current page",selectAll:"Select all data",deselectAll:"Deselect all data",removeAll:"Remove all data",selectInvert:"Invert current page"},Upload:{uploading:"Uploading...",removeFile:"Remove file",uploadError:"Upload error",previewFile:"Preview file",downloadFile:"Download file"},Empty:{description:"No data"},Icon:{icon:"icon"},Text:{edit:"Edit",copy:"Copy",copied:"Copied",expand:"Expand",collapse:"Collapse"},Form:{optional:"(optional)",defaultValidateMessages:{default:"Field validation error for ${label}",required:"Please enter ${label}",enum:"${label} must be one of [${enum}]",whitespace:"${label} cannot be a blank character",date:{format:"${label} date format is invalid",parse:"${label} cannot be converted to a date",invalid:"${label} is an invalid date"},types:{string:F,method:F,array:F,object:F,number:F,date:F,boolean:F,integer:F,float:F,regexp:F,email:F,url:F,hex:F},string:{len:"${label} must be ${len} characters",min:"${label} must be at least ${min} characters",max:"${label} must be up to ${max} characters",range:"${label} must be between ${min}-${max} characters"},number:{len:"${label} must be equal to ${len}",min:"${label} must be minimum ${min}",max:"${label} must be maximum ${max}",range:"${label} must be between ${min}-${max}"},array:{len:"Must be ${len} ${label}",min:"At least ${min} ${label}",max:"At most ${max} ${label}",range:"The amount of ${label} must be between ${min}-${max}"},pattern:{mismatch:"${label} does not match the pattern ${pattern}"}}},Image:{preview:"Preview"},QRCode:{expired:"QR code expired",refresh:"Refresh",scanned:"Scanned"},ColorPicker:{presetEmpty:"Empty",transparent:"Transparent",singleColor:"Single",gradientColor:"Gradient"}};let Re=Object.assign({},Ne.Modal),je=[];const Ot=()=>je.reduce((e,t)=>Object.assign(Object.assign({},e),t),Ne.Modal);function xr(e){if(e){const t=Object.assign({},e);return je.push(t),Re=Ot(),()=>{je=je.filter(n=>n!==t),Re=Ot()}}Re=Object.assign({},Ne.Modal)}function ba(){return Re}const Ht=l.createContext(void 0),Pr="internalMark",Or=e=>{const{locale:t={},children:n,_ANT_MARK__:r}=e;l.useEffect(()=>xr(t==null?void 0:t.Modal),[t]);const a=l.useMemo(()=>Object.assign(Object.assign({},t),{exist:!0}),[t]);return l.createElement(Ht.Provider,{value:a},n)},Ar=`-ant-${Date.now()}-${Math.random()}`;function wr(e,t){const n={},r=(s,i)=>{let u=s.clone();return u=(i==null?void 0:i(u))||u,u.toRgbString()},a=(s,i)=>{const u=new ze(s),o=gt(u.toRgbString());n[`${i}-color`]=r(u),n[`${i}-color-disabled`]=o[1],n[`${i}-color-hover`]=o[4],n[`${i}-color-active`]=o[6],n[`${i}-color-outline`]=u.clone().setA(.2).toRgbString(),n[`${i}-color-deprecated-bg`]=o[0],n[`${i}-color-deprecated-border`]=o[2]};if(t.primaryColor){a(t.primaryColor,"primary");const s=new ze(t.primaryColor),i=gt(s.toRgbString());i.forEach((o,f)=>{n[`primary-${f+1}`]=o}),n["primary-color-deprecated-l-35"]=r(s,o=>o.lighten(35)),n["primary-color-deprecated-l-20"]=r(s,o=>o.lighten(20)),n["primary-color-deprecated-t-20"]=r(s,o=>o.tint(20)),n["primary-color-deprecated-t-50"]=r(s,o=>o.tint(50)),n["primary-color-deprecated-f-12"]=r(s,o=>o.setA(o.a*.12));const u=new ze(i[0]);n["primary-color-active-deprecated-f-30"]=r(u,o=>o.setA(o.a*.3)),n["primary-color-active-deprecated-d-02"]=r(u,o=>o.darken(2))}return t.successColor&&a(t.successColor,"success"),t.warningColor&&a(t.warningColor,"warning"),t.errorColor&&a(t.errorColor,"error"),t.infoColor&&a(t.infoColor,"info"),`
  :root {
    ${Object.keys(n).map(s=>`--${e}-${s}: ${n[s]};`).join(`
`)}
  }
  `.trim()}function _r(e,t){const n=wr(e,t);Le()&&Gn(n,`${Ar}-dynamic-theme`)}const Xe=l.createContext(!1),kr=({children:e,disabled:t})=>{const n=l.useContext(Xe);return l.createElement(Xe.Provider,{value:t??n},e)};function Rr(){const e=l.useContext(Xe),t=l.useContext(it);return{componentDisabled:e,componentSize:t}}function et(e){var t=l.useRef();t.current=e;var n=l.useCallback(function(){for(var r,a=arguments.length,c=new Array(a),s=0;s<a;s++)c[s]=arguments[s];return(r=t.current)===null||r===void 0?void 0:r.call.apply(r,[t].concat(c))},[]);return n}function tt(e){var t=l.useRef(!1),n=l.useState(e),r=H(n,2),a=r[0],c=r[1];l.useEffect(function(){return t.current=!1,function(){t.current=!0}},[]);function s(i,u){u&&t.current||c(i)}return[a,s]}const jr=(e,t)=>{const[n,r]=Vt();return Yn({token:r,hashId:"",path:["ant-design-icons",e],nonce:()=>t==null?void 0:t.nonce,layer:{name:"antd"}},()=>Wn(e))},Tr=Object.assign({},ar),{useId:At}=Tr,Mr=()=>"",$r=typeof At>"u"?Mr:At;function Lr(e,t,n){var r;hr();const a=e||{},c=a.inherit===!1||!t?Object.assign(Object.assign({},yt),{hashed:(r=t==null?void 0:t.hashed)!==null&&r!==void 0?r:yt.hashed,cssVar:t==null?void 0:t.cssVar}):t,s=$r();return Dt(()=>{var i,u;if(!e)return t;const o=Object.assign({},c.components);Object.keys(e.components||{}).forEach(v=>{o[v]=Object.assign(Object.assign({},o[v]),e.components[v])});const f=`css-var-${s.replace(/:/g,"")}`,d=((i=a.cssVar)!==null&&i!==void 0?i:c.cssVar)&&Object.assign(Object.assign(Object.assign({prefix:n==null?void 0:n.prefixCls},typeof c.cssVar=="object"?c.cssVar:{}),typeof a.cssVar=="object"?a.cssVar:{}),{key:typeof a.cssVar=="object"&&((u=a.cssVar)===null||u===void 0?void 0:u.key)||f});return Object.assign(Object.assign(Object.assign({},c),a),{token:Object.assign(Object.assign({},c.token),a.token),components:o,cssVar:d})},[a,c],(i,u)=>i.some((o,f)=>{const d=u[f];return!lr(o,d,!0)}))}var Nr=["children"],Zt=l.createContext({});function Vr(e){var t=e.children,n=Ze(e,Nr);return l.createElement(Zt.Provider,{value:n},t)}var Dr=function(e){Ft(n,e);var t=It(n);function n(){return Kt(this,n),t.apply(this,arguments)}return Ut(n,[{key:"render",value:function(){return this.props.children}}]),n}(l.Component);function Fr(e){var t=l.useReducer(function(i){return i+1},0),n=H(t,2),r=n[1],a=l.useRef(e),c=et(function(){return a.current}),s=et(function(i){a.current=typeof i=="function"?i(a.current):i,r()});return[c,s]}var te="none",Ae="appear",we="enter",_e="leave",wt="none",W="prepare",ue="start",le="active",ut="end",Jt="prepared";function _t(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit".concat(e)]="webkit".concat(t),n["Moz".concat(e)]="moz".concat(t),n["ms".concat(e)]="MS".concat(t),n["O".concat(e)]="o".concat(t.toLowerCase()),n}function Ir(e,t){var n={animationend:_t("Animation","AnimationEnd"),transitionend:_t("Transition","TransitionEnd")};return e&&("AnimationEvent"in t||delete n.animationend.animation,"TransitionEvent"in t||delete n.transitionend.transition),n}var Kr=Ir(Le(),typeof window<"u"?window:{}),Xt={};if(Le()){var Ur=document.createElement("div");Xt=Ur.style}var ke={};function en(e){if(ke[e])return ke[e];var t=Kr[e];if(t)for(var n=Object.keys(t),r=n.length,a=0;a<r;a+=1){var c=n[a];if(Object.prototype.hasOwnProperty.call(t,c)&&c in Xt)return ke[e]=t[c],ke[e]}return""}var tn=en("animationend"),nn=en("transitionend"),rn=!!(tn&&nn),kt=tn||"animationend",Rt=nn||"transitionend";function jt(e,t){if(!e)return null;if(X(e)==="object"){var n=t.replace(/-\w/g,function(r){return r[1].toUpperCase()});return e[n]}return"".concat(e,"-").concat(t)}const Gr=function(e){var t=l.useRef();function n(a){a&&(a.removeEventListener(Rt,e),a.removeEventListener(kt,e))}function r(a){t.current&&t.current!==a&&n(t.current),a&&a!==t.current&&(a.addEventListener(Rt,e),a.addEventListener(kt,e),t.current=a)}return l.useEffect(function(){return function(){n(t.current)}},[]),[r,n]};var an=Le()?l.useLayoutEffect:l.useEffect;const Yr=function(){var e=l.useRef(null);function t(){He.cancel(e.current)}function n(r){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:2;t();var c=He(function(){a<=1?r({isCanceled:function(){return c!==e.current}}):n(r,a-1)});e.current=c}return l.useEffect(function(){return function(){t()}},[]),[n,t]};var Wr=[W,ue,le,ut],zr=[W,Jt],on=!1,qr=!0;function sn(e){return e===le||e===ut}const Qr=function(e,t,n){var r=tt(wt),a=H(r,2),c=a[0],s=a[1],i=Yr(),u=H(i,2),o=u[0],f=u[1];function d(){s(W,!0)}var v=t?zr:Wr;return an(function(){if(c!==wt&&c!==ut){var m=v.indexOf(c),h=v[m+1],M=n(c);M===on?s(h,!0):h&&o(function(_){function R(){_.isCanceled()||s(h,!0)}M===!0?R():Promise.resolve(M).then(R)})}},[e,c]),l.useEffect(function(){return function(){f()}},[]),[d,c]};function Br(e,t,n,r){var a=r.motionEnter,c=a===void 0?!0:a,s=r.motionAppear,i=s===void 0?!0:s,u=r.motionLeave,o=u===void 0?!0:u,f=r.motionDeadline,d=r.motionLeaveImmediately,v=r.onAppearPrepare,m=r.onEnterPrepare,h=r.onLeavePrepare,M=r.onAppearStart,_=r.onEnterStart,R=r.onLeaveStart,D=r.onAppearActive,N=r.onEnterActive,z=r.onLeaveActive,I=r.onAppearEnd,y=r.onEnterEnd,p=r.onLeaveEnd,E=r.onVisibleChanged,q=tt(),Y=H(q,2),x=Y[0],P=Y[1],S=Fr(te),C=H(S,2),b=C[0],O=C[1],A=tt(null),K=H(A,2),ee=K[0],ge=K[1],U=b(),re=l.useRef(!1),de=l.useRef(null);function ae(){return n()}var ye=l.useRef(!1);function he(){O(te),ge(null,!0)}var be=et(function($){var k=b();if(k!==te){var G=ae();if(!($&&!$.deadline&&$.target!==G)){var oe=ye.current,ie;k===Ae&&oe?ie=I==null?void 0:I(G,$):k===we&&oe?ie=y==null?void 0:y(G,$):k===_e&&oe&&(ie=p==null?void 0:p(G,$)),oe&&ie!==!1&&he()}}}),Ve=Gr(be),De=H(Ve,1),Fe=De[0],Ee=function(k){switch(k){case Ae:return V(V(V({},W,v),ue,M),le,D);case we:return V(V(V({},W,m),ue,_),le,N);case _e:return V(V(V({},W,h),ue,R),le,z);default:return{}}},ne=l.useMemo(function(){return Ee(U)},[U]),Ie=Qr(U,!e,function($){if($===W){var k=ne[W];return k?k(ae()):on}if(Z in ne){var G;ge(((G=ne[Z])===null||G===void 0?void 0:G.call(ne,ae(),null))||null)}return Z===le&&U!==te&&(Fe(ae()),f>0&&(clearTimeout(de.current),de.current=setTimeout(function(){be({deadline:!0})},f))),Z===Jt&&he(),qr}),Se=H(Ie,2),Ke=Se[0],Z=Se[1],Ue=sn(Z);ye.current=Ue;var Ce=l.useRef(null);an(function(){if(!(re.current&&Ce.current===t)){P(t);var $=re.current;re.current=!0;var k;!$&&t&&i&&(k=Ae),$&&t&&c&&(k=we),($&&!t&&o||!$&&d&&!t&&o)&&(k=_e);var G=Ee(k);k&&(e||G[W])?(O(k),Ke()):O(te),Ce.current=t}},[t]),l.useEffect(function(){(U===Ae&&!i||U===we&&!c||U===_e&&!o)&&O(te)},[i,c,o]),l.useEffect(function(){return function(){re.current=!1,clearTimeout(de.current)}},[]);var ve=l.useRef(!1);l.useEffect(function(){x&&(ve.current=!0),x!==void 0&&U===te&&((ve.current||x)&&(E==null||E(x)),ve.current=!0)},[x,U]);var me=ee;return ne[W]&&Z===ue&&(me=g({transition:"none"},me)),[U,Z,me,x??t]}function Hr(e){var t=e;X(e)==="object"&&(t=e.transitionSupport);function n(a,c){return!!(a.motionName&&t&&c!==!1)}var r=l.forwardRef(function(a,c){var s=a.visible,i=s===void 0?!0:s,u=a.removeOnLeave,o=u===void 0?!0:u,f=a.forceRender,d=a.children,v=a.motionName,m=a.leavedClassName,h=a.eventProps,M=l.useContext(Zt),_=M.motion,R=n(a,_),D=l.useRef(),N=l.useRef();function z(){try{return D.current instanceof HTMLElement?D.current:cr(N.current)}catch{return null}}var I=Br(R,i,z,a),y=H(I,4),p=y[0],E=y[1],q=y[2],Y=y[3],x=l.useRef(Y);Y&&(x.current=!0);var P=l.useCallback(function(K){D.current=K,zn(c,K)},[c]),S,C=g(g({},h),{},{visible:i});if(!d)S=null;else if(p===te)Y?S=d(g({},C),P):!o&&x.current&&m?S=d(g(g({},C),{},{className:m}),P):f||!o&&!m?S=d(g(g({},C),{},{style:{display:"none"}}),P):S=null;else{var b;E===W?b="prepare":sn(E)?b="active":E===ue&&(b="start");var O=jt(v,"".concat(p,"-").concat(b));S=d(g(g({},C),{},{className:qn(jt(v,p),V(V({},O,O&&b),v,typeof v=="string")),style:q}),P)}if(l.isValidElement(S)&&Qn(S)){var A=Bn(S);A||(S=l.cloneElement(S,{ref:P}))}return l.createElement(Dr,{ref:N},S)});return r.displayName="CSSMotion",r}const Zr=Hr(rn);var nt="add",rt="keep",at="remove",Qe="removed";function Jr(e){var t;return e&&X(e)==="object"&&"key"in e?t=e:t={key:e},g(g({},t),{},{key:String(t.key)})}function ot(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return e.map(Jr)}function Xr(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],n=[],r=0,a=t.length,c=ot(e),s=ot(t);c.forEach(function(o){for(var f=!1,d=r;d<a;d+=1){var v=s[d];if(v.key===o.key){r<d&&(n=n.concat(s.slice(r,d).map(function(m){return g(g({},m),{},{status:nt})})),r=d),n.push(g(g({},v),{},{status:rt})),r+=1,f=!0;break}}f||n.push(g(g({},o),{},{status:at}))}),r<a&&(n=n.concat(s.slice(r).map(function(o){return g(g({},o),{},{status:nt})})));var i={};n.forEach(function(o){var f=o.key;i[f]=(i[f]||0)+1});var u=Object.keys(i).filter(function(o){return i[o]>1});return u.forEach(function(o){n=n.filter(function(f){var d=f.key,v=f.status;return d!==o||v!==at}),n.forEach(function(f){f.key===o&&(f.status=rt)})}),n}var ea=["component","children","onVisibleChanged","onAllRemoved"],ta=["status"],na=["eventProps","visible","children","motionName","motionAppear","motionEnter","motionLeave","motionLeaveImmediately","motionDeadline","removeOnLeave","leavedClassName","onAppearPrepare","onAppearStart","onAppearActive","onAppearEnd","onEnterStart","onEnterActive","onEnterEnd","onLeaveStart","onLeaveActive","onLeaveEnd"];function ra(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Zr,n=function(r){Ft(c,r);var a=It(c);function c(){var s;Kt(this,c);for(var i=arguments.length,u=new Array(i),o=0;o<i;o++)u[o]=arguments[o];return s=a.call.apply(a,[this].concat(u)),V(ht(s),"state",{keyEntities:[]}),V(ht(s),"removeKey",function(f){s.setState(function(d){var v=d.keyEntities.map(function(m){return m.key!==f?m:g(g({},m),{},{status:Qe})});return{keyEntities:v}},function(){var d=s.state.keyEntities,v=d.filter(function(m){var h=m.status;return h!==Qe}).length;v===0&&s.props.onAllRemoved&&s.props.onAllRemoved()})}),s}return Ut(c,[{key:"render",value:function(){var i=this,u=this.state.keyEntities,o=this.props,f=o.component,d=o.children,v=o.onVisibleChanged;o.onAllRemoved;var m=Ze(o,ea),h=f||l.Fragment,M={};return na.forEach(function(_){M[_]=m[_],delete m[_]}),delete m.keys,l.createElement(h,m,u.map(function(_,R){var D=_.status,N=Ze(_,ta),z=D===nt||D===rt;return l.createElement(t,Hn({},M,{key:N.key,visible:z,eventProps:N,onVisibleChanged:function(y){v==null||v(y,{key:N.key}),y||i.removeKey(N.key)}}),function(I,y){return d(g(g({},I),{},{index:R}),y)})}))}}],[{key:"getDerivedStateFromProps",value:function(i,u){var o=i.keys,f=u.keyEntities,d=ot(o),v=Xr(f,d);return{keyEntities:v.filter(function(m){var h=f.find(function(M){var _=M.key;return m.key===_});return!(h&&h.status===Qe&&m.status===at)})}}}]),c}(l.Component);return V(n,"defaultProps",{component:"div"}),n}const Ea=ra(rn),Tt=l.createContext(!0);function aa(e){const t=l.useContext(Tt),{children:n}=e,[,r]=Vt(),{motion:a}=r,c=l.useRef(!1);return c.current||(c.current=t!==a),c.current?l.createElement(Tt.Provider,{value:a},l.createElement(Vr,{motion:a},n)):n}const oa=()=>null;var ia=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};const sa=["getTargetContainer","getPopupContainer","renderEmpty","input","pagination","form","select","button"];let Me,cn,un,ln;function Te(){return Me||nr}function ca(){return cn||Gt}function ua(e){return Object.keys(e).some(t=>t.endsWith("Color"))}const la=e=>{const{prefixCls:t,iconPrefixCls:n,theme:r,holderRender:a}=e;t!==void 0&&(Me=t),n!==void 0&&(cn=n),"holderRender"in e&&(ln=a),r&&(ua(r)?_r(Te(),r):un=r)},Sa=()=>({getPrefixCls:(e,t)=>t||(e?`${Te()}-${e}`:Te()),getIconPrefixCls:ca,getRootPrefixCls:()=>Me||Te(),getTheme:()=>un,holderRender:ln}),fa=e=>{const{children:t,csp:n,autoInsertSpaceInButton:r,alert:a,anchor:c,form:s,locale:i,componentSize:u,direction:o,space:f,splitter:d,virtual:v,dropdownMatchSelectWidth:m,popupMatchSelectWidth:h,popupOverflow:M,legacyLocale:_,parentContext:R,iconPrefixCls:D,theme:N,componentDisabled:z,segmented:I,statistic:y,spin:p,calendar:E,carousel:q,cascader:Y,collapse:x,typography:P,checkbox:S,descriptions:C,divider:b,drawer:O,skeleton:A,steps:K,image:ee,layout:ge,list:U,mentions:re,modal:de,progress:ae,result:ye,slider:he,breadcrumb:be,menu:Ve,pagination:De,input:Fe,textArea:Ee,empty:ne,badge:Ie,radio:Se,rate:Ke,switch:Z,transfer:Ue,avatar:Ce,message:ve,tag:me,table:$,card:k,tabs:G,timeline:oe,timePicker:ie,upload:vn,notification:mn,tree:pn,colorPicker:gn,datePicker:yn,rangePicker:hn,flex:bn,wave:En,dropdown:Sn,warning:Cn,tour:xn,tooltip:Pn,popover:On,popconfirm:An,floatButton:wn,floatButtonGroup:_n,variant:kn,inputNumber:Rn,treeSelect:jn}=e,ft=l.useCallback((w,T)=>{const{prefixCls:Q}=e;if(T)return T;const B=Q||R.getPrefixCls("");return w?`${B}-${w}`:B},[R.getPrefixCls,e.prefixCls]),xe=D||R.iconPrefixCls||Gt,Pe=n||R.csp;jr(xe,Pe);const Ge=Lr(N,R.theme,{prefixCls:ft("")}),Ye={csp:Pe,autoInsertSpaceInButton:r,alert:a,anchor:c,locale:i||_,direction:o,space:f,splitter:d,virtual:v,popupMatchSelectWidth:h??m,popupOverflow:M,getPrefixCls:ft,iconPrefixCls:xe,theme:Ge,segmented:I,statistic:y,spin:p,calendar:E,carousel:q,cascader:Y,collapse:x,typography:P,checkbox:S,descriptions:C,divider:b,drawer:O,skeleton:A,steps:K,image:ee,input:Fe,textArea:Ee,layout:ge,list:U,mentions:re,modal:de,progress:ae,result:ye,slider:he,breadcrumb:be,menu:Ve,pagination:De,empty:ne,badge:Ie,radio:Se,rate:Ke,switch:Z,transfer:Ue,avatar:Ce,message:ve,tag:me,table:$,card:k,tabs:G,timeline:oe,timePicker:ie,upload:vn,notification:mn,tree:pn,colorPicker:gn,datePicker:yn,rangePicker:hn,flex:bn,wave:En,dropdown:Sn,warning:Cn,tour:xn,tooltip:Pn,popover:On,popconfirm:An,floatButton:wn,floatButtonGroup:_n,variant:kn,inputNumber:Rn,treeSelect:jn},se=Object.assign({},R);Object.keys(Ye).forEach(w=>{Ye[w]!==void 0&&(se[w]=Ye[w])}),sa.forEach(w=>{const T=e[w];T&&(se[w]=T)}),typeof r<"u"&&(se.button=Object.assign({autoInsertSpace:r},se.button));const ce=Dt(()=>se,se,(w,T)=>{const Q=Object.keys(w),B=Object.keys(T);return Q.length!==B.length||Q.some(Oe=>w[Oe]!==T[Oe])}),{layer:dt}=l.useContext(Zn),Tn=l.useMemo(()=>({prefixCls:xe,csp:Pe,layer:dt?"antd":void 0}),[xe,Pe,dt]);let j=l.createElement(l.Fragment,null,l.createElement(oa,{dropdownMatchSelectWidth:m}),t);const vt=l.useMemo(()=>{var w,T,Q,B;return pr(((w=Ne.Form)===null||w===void 0?void 0:w.defaultValidateMessages)||{},((Q=(T=ce.locale)===null||T===void 0?void 0:T.Form)===null||Q===void 0?void 0:Q.defaultValidateMessages)||{},((B=ce.form)===null||B===void 0?void 0:B.validateMessages)||{},(s==null?void 0:s.validateMessages)||{})},[ce,s==null?void 0:s.validateMessages]);Object.keys(vt).length>0&&(j=l.createElement(br.Provider,{value:vt},j)),i&&(j=l.createElement(Or,{locale:i,_ANT_MARK__:Pr},j)),j=l.createElement(fr.Provider,{value:Tn},j),u&&(j=l.createElement(Jn,{size:u},j)),j=l.createElement(aa,null,j);const Mn=l.useMemo(()=>{const w=Ge||{},{algorithm:T,token:Q,components:B,cssVar:Oe}=w,$n=ia(w,["algorithm","token","components","cssVar"]),mt=T&&(!Array.isArray(T)||T.length>0)?bt(T):Xn,We={};Object.entries(B||{}).forEach(([Ln,Nn])=>{const J=Object.assign({},Nn);"algorithm"in J&&(J.algorithm===!0?J.theme=mt:(Array.isArray(J.algorithm)||typeof J.algorithm=="function")&&(J.theme=bt(J.algorithm)),delete J.algorithm),We[Ln]=J});const pt=Object.assign(Object.assign({},er),Q);return Object.assign(Object.assign({},$n),{theme:mt,token:pt,components:We,override:Object.assign({override:pt},We),cssVar:Oe})},[Ge]);return N&&(j=l.createElement(tr.Provider,{value:Mn},j)),ce.warning&&(j=l.createElement(yr.Provider,{value:ce.warning},j)),z!==void 0&&(j=l.createElement(kr,{disabled:z},j)),l.createElement(st.Provider,{value:ce},j)},pe=e=>{const t=l.useContext(st),n=l.useContext(Ht);return l.createElement(fa,Object.assign({parentContext:t,legacyLocale:n},e))};pe.ConfigContext=st;pe.SizeContext=it;pe.config=la;pe.useConfig=Rr;Object.defineProperty(pe,"SizeContext",{get:()=>it});function Ca(e){return e&&fe.isValidElement(e)&&e.type===fe.Fragment}const da=(e,t,n)=>fe.isValidElement(e)?fe.cloneElement(e,typeof n=="function"?n(e.props||{}):n):t;function xa(e,t){return da(e,e,t)}function fn(e,t){this.v=e,this.k=t}function L(e,t,n,r){var a=Object.defineProperty;try{a({},"",{})}catch{a=0}L=function(s,i,u,o){if(i)a?a(s,i,{value:u,enumerable:!o,configurable:!o,writable:!o}):s[i]=u;else{var f=function(v,m){L(s,v,function(h){return this._invoke(v,m,h)})};f("next",0),f("throw",1),f("return",2)}},L(e,t,n,r)}function lt(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,n=typeof Symbol=="function"?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function c(m,h,M,_){var R=h&&h.prototype instanceof i?h:i,D=Object.create(R.prototype);return L(D,"_invoke",function(N,z,I){var y,p,E,q=0,Y=I||[],x=!1,P={p:0,n:0,v:e,a:S,f:S.bind(e,4),d:function(b,O){return y=b,p=0,E=e,P.n=O,s}};function S(C,b){for(p=C,E=b,t=0;!x&&q&&!O&&t<Y.length;t++){var O,A=Y[t],K=P.p,ee=A[2];C>3?(O=ee===b)&&(E=A[(p=A[4])?5:(p=3,3)],A[4]=A[5]=e):A[0]<=K&&((O=C<2&&K<A[1])?(p=0,P.v=b,P.n=A[1]):K<ee&&(O=C<3||A[0]>b||b>ee)&&(A[4]=C,A[5]=b,P.n=ee,p=0))}if(O||C>1)return s;throw x=!0,b}return function(C,b,O){if(q>1)throw TypeError("Generator is already running");for(x&&b===1&&S(b,O),p=b,E=O;(t=p<2?e:E)||!x;){y||(p?p<3?(p>1&&(P.n=-1),S(p,E)):P.n=E:P.v=E);try{if(q=2,y){if(p||(C="next"),t=y[C]){if(!(t=t.call(y,E)))throw TypeError("iterator result is not an object");if(!t.done)return t;E=t.value,p<2&&(p=0)}else p===1&&(t=y.return)&&t.call(y),p<2&&(E=TypeError("The iterator does not provide a '"+C+"' method"),p=1);y=e}else if((t=(x=P.n<0)?E:N.call(z,P))!==s)break}catch(A){y=e,p=1,E=A}finally{q=1}}return{value:t,done:x}}}(m,M,_),!0),D}var s={};function i(){}function u(){}function o(){}t=Object.getPrototypeOf;var f=[][r]?t(t([][r]())):(L(t={},r,function(){return this}),t),d=o.prototype=i.prototype=Object.create(f);function v(m){return Object.setPrototypeOf?Object.setPrototypeOf(m,o):(m.__proto__=o,L(m,a,"GeneratorFunction")),m.prototype=Object.create(d),m}return u.prototype=o,L(d,"constructor",o),L(o,"constructor",u),u.displayName="GeneratorFunction",L(o,a,"GeneratorFunction"),L(d),L(d,a,"Generator"),L(d,r,function(){return this}),L(d,"toString",function(){return"[object Generator]"}),(lt=function(){return{w:c,m:v}})()}function $e(e,t){function n(a,c,s,i){try{var u=e[a](c),o=u.value;return o instanceof fn?t.resolve(o.v).then(function(f){n("next",f,s,i)},function(f){n("throw",f,s,i)}):t.resolve(o).then(function(f){u.value=f,s(u)},function(f){return n("throw",f,s,i)})}catch(f){i(f)}}var r;this.next||(L($e.prototype),L($e.prototype,typeof Symbol=="function"&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),L(this,"_invoke",function(a,c,s){function i(){return new t(function(u,o){n(a,s,u,o)})}return r=r?r.then(i,i):i()},!0)}function dn(e,t,n,r,a){return new $e(lt().w(e,t,n,r),a||Promise)}function va(e,t,n,r,a){var c=dn(e,t,n,r,a);return c.next().then(function(s){return s.done?s.value:c.next()})}function ma(e){var t=Object(e),n=[];for(var r in t)n.unshift(r);return function a(){for(;n.length;)if((r=n.pop())in t)return a.value=r,a.done=!1,a;return a.done=!0,a}}function Mt(e){if(e!=null){var t=e[typeof Symbol=="function"&&Symbol.iterator||"@@iterator"],n=0;if(t)return t.call(e);if(typeof e.next=="function")return e;if(!isNaN(e.length))return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}}}throw new TypeError(X(e)+" is not iterable")}function $t(){var e=lt(),t=e.m($t),n=(Object.getPrototypeOf?Object.getPrototypeOf(t):t.__proto__).constructor;function r(s){var i=typeof s=="function"&&s.constructor;return!!i&&(i===n||(i.displayName||i.name)==="GeneratorFunction")}var a={throw:1,return:2,break:3,continue:3};function c(s){var i,u;return function(o){i||(i={stop:function(){return u(o.a,2)},catch:function(){return o.v},abrupt:function(d,v){return u(o.a,a[d],v)},delegateYield:function(d,v,m){return i.resultName=v,u(o.d,Mt(d),m)},finish:function(d){return u(o.f,d)}},u=function(d,v,m){o.p=i.prev,o.n=i.next;try{return d(v,m)}finally{i.next=o.n}}),i.resultName&&(i[i.resultName]=o.v,i.resultName=void 0),i.sent=o.v,i.next=o.n;try{return s.call(this,i)}finally{o.p=i.prev,o.n=i.next}}}return($t=function(){return{wrap:function(u,o,f,d){return e.w(c(u),o,f,d&&d.reverse())},isGeneratorFunction:r,mark:e.m,awrap:function(u,o){return new fn(u,o)},AsyncIterator:$e,async:function(u,o,f,d,v){return(r(o)?dn:va)(c(u),o,f,d,v)},keys:ma,values:Mt}})()}function Lt(e,t,n,r,a,c,s){try{var i=e[c](s),u=i.value}catch(o){return void n(o)}i.done?t(u):Promise.resolve(u).then(r,a)}function Pa(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var c=e.apply(t,n);function s(u){Lt(c,r,a,s,i,"next",u)}function i(u){Lt(c,r,a,s,i,"throw",u)}s(void 0)})}}function Oa(e,t){var n=Object.assign({},e);return Array.isArray(t)&&t.forEach(function(r){delete n[r]}),n}const Aa=function(e){if(!e)return!1;if(e instanceof Element){if(e.offsetParent)return!0;if(e.getBBox){var t=e.getBBox(),n=t.width,r=t.height;if(n||r)return!0}if(e.getBoundingClientRect){var a=e.getBoundingClientRect(),c=a.width,s=a.height;if(c||s)return!0}}return!1};export{Pt as A,Be as B,pe as C,Xe as D,Er as E,fr as I,Ht as L,ha as R,br as V,Ze as _,et as a,Pa as b,xa as c,$t as d,Zr as e,hr as f,sr as g,Ca as h,Aa as i,cr as j,St as k,Ne as l,Ea as m,kr as n,Oa as o,Je as p,lr as q,Yt as r,qe as s,Et as t,tt as u,ba as v,He as w,Sa as x,pr as y,dr as z};
