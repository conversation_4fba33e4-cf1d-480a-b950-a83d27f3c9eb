import{r as e}from"./PeopleSelectPopover-CDKo4AC-.js";import{u as y}from"./useMergedState-BDSe6zqT.js";import{C as E}from"./isVisible-Bd4H7hpW.js";import{C as w}from"./genStyleUtils-CI3YU7Yv.js";function z(r){return s=>e.createElement(E,{theme:{token:{motion:!1,zIndexPopupBase:0}}},e.createElement(r,Object.assign({},s)))}const $=(r,s,R,m,a)=>z(o=>{const{prefixCls:g,style:v}=o,i=e.useRef(null),[P,b]=e.useState(0),[h,C]=e.useState(0),[c,O]=y(!1,{value:o.open}),{getPrefixCls:j}=e.useContext(w),l=j(m||"select",g);e.useEffect(()=>{if(O(!0),typeof ResizeObserver<"u"){const f=new ResizeObserver(t=>{const n=t[0].target;b(n.offsetHeight+8),C(n.offsetWidth)}),p=setInterval(()=>{var t;const n=a?`.${a(l)}`:`.${l}-dropdown`,d=(t=i.current)===null||t===void 0?void 0:t.querySelector(n);d&&(clearInterval(p),f.observe(d))},10);return()=>{clearInterval(p),f.disconnect()}}},[]);let u=Object.assign(Object.assign({},o),{style:Object.assign(Object.assign({},v),{margin:0}),open:c,visible:c,getPopupContainer:()=>i.current});s&&Object.assign(u,{[s]:{overflow:{adjustX:!1,adjustY:!1}}});const x={paddingBottom:P,position:"relative",minWidth:h};return e.createElement("div",{ref:i,style:x},e.createElement(r,Object.assign({},u)))});export{$ as g,z as w};
