$(function(){new r});class r{constructor(){this.bootstrap()}bootstrap(){this.initBrandDetailDescTab(),this.initBrandDetailSwiper(".brands-hotel-block"),this.initBrandDetailSwiper(".brands-discount-block")}initBrandDetailSwiper(t){new Swiper(`${t} .swiper-container`,{slidesPerView:1.1,spaceBetween:24,slidesPerGroup:1,breakpoints:{768:{slidesPerView:2},1024:{slidesPerView:3}},on:{slideChange:function(){n(this)},init:function(){n(this)}},navigation:{nextEl:`${t} .gha-swiper-button-next`,prevEl:`${t} .gha-swiper-button-prev`},pagination:{el:`${t} .swiper-pagination`,clickable:!0}});function n(i){$(`${t} .swiper-cur-idx`).text(i.activeIndex+1);let e=3;window.innerWidth<1024&&(e=2),window.innerWidth<768&&(e=1),i.slides.forEach((a,s)=>{a.classList.remove("transparent-slide-30"),a.classList.remove("transparent-slide-0"),s>i.activeIndex+e&&a.classList.add("transparent-slide-0"),s===i.activeIndex+e&&a.classList.add("transparent-slide-30"),s<i.activeIndex-1&&a.classList.add("transparent-slide-0"),s===i.activeIndex-1&&a.classList.add("transparent-slide-30")});const d=i.activeIndex+3;d<i.slides.length&&i.slides[d].classList.add("transparent-slide-30")}}initBrandDetailDescTab(){$(".brand-desc-tab-item a").on("click",function(){if($(this).hasClass("active"))return;const t=$(this).parent().index();$(".brand-desc-tab-item a").removeClass("active"),$(this).addClass("active"),$(".brand-desc-tab-panel").addClass("hidden"),$(".brand-desc-tab-panel").eq(t).removeClass("hidden")})}}
