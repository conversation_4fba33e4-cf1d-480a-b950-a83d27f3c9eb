import{j as r}from"./hotel-item-D_zvdyIk.js";import{$ as a}from"./_constants-CI6xcXYb.js";import"./helper-D414uohx.js";import{r as n}from"./PeopleSelectPopover-CDKo4AC-.js";import{F as o}from"./index-CC5HyPSW.js";import{I as i}from"./index-DNIuBGmG.js";import{B as l}from"./button-C2fNxKeA.js";import"./index-Dq7h7Pqt.js";import"./context-Gzj2nObQ.js";import"./genStyleUtils-CI3YU7Yv.js";import"./isVisible-Bd4H7hpW.js";import"./index-C_TizibV.js";import"./zoom-BbRr1fkt.js";import"./ContextIsolator-CVrwwDX4.js";import"./color-DKTup0-d.js";import"./useSize-CbUlsqBW.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./collapse-BbEVqHco.js";import"./useForm-DF1XNZWJ.js";import"./row-BiveCJJi.js";import"./index-DR0pJ98a.js";import"./useBreakpoint-D48_IWaH.js";import"./index-CJQq4WAU.js";import"./index-CaUFHQr4.js";import"./useMergedState-BDSe6zqT.js";import"./roundedArrow-DVJD-5zd.js";import"./pickAttrs-D1C8emUZ.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./index-wg7qNA5H.js";import"./Input-DCb0FIZx.js";import"./index-DKvg8qd3.js";function M({onNext:m}){const[d,u]=n.useState(!1),[s]=o.useForm();function p(t){m(t)}return r.jsxs(o,{layout:"vertical",form:s,onFinish:p,children:[r.jsx(o.Item,{label:"密码",name:"password",rules:[{required:!0,message:"请输入您的密码"},{pattern:a.passwordPattern,message:"密码至少8位，包含字母、数字和符号，且首尾不能有空格"}],children:r.jsx(i.Password,{variant:"underlined",placeholder:"请输入您的密码"})}),r.jsx(o.Item,{label:"确认密码",name:"confirm_password",dependencies:["password"],rules:[{required:!0,message:"请输入确认密码"},({getFieldValue:t})=>({validator(c,e){return!e||t("password")===e?Promise.resolve():Promise.reject(new Error("密码与确认密码不一致!"))}})],children:r.jsx(i.Password,{variant:"underlined",placeholder:"请输入确认密码"})}),r.jsx(o.Item,{label:null,children:r.jsx(l,{className:"gha-primary-btn",type:"primary",shape:"round",block:!0,htmlType:"submit",children:"提交"})})]})}export{M as F};
