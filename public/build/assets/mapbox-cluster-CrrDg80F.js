(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3654],{53547:function(Y,Q,r){r.d(Q,{Z:function(){return T},u:function(){return N}});var v=r(85893),l=r(67294),G=r(6158),y=r.n(G),D=r(73935);r(81634);var X=r(84381),m=r.n(X),E=r(79466),$=r(60461),H=r(81043),K=r(29806),ee=r(29090),R=r(67262),P=r(73385),te=r(1959),h=r(71626);y().accessToken="pk.eyJ1IjoibWFrc3ltLW1hbHRzZXYiLCJhIjoiY2tweTVkbG1tMDQ3dTJzcnJxMHV0enJ4MCJ9.2TfFQqNNty9MDAFxZriNaA";var C=function(c){return c.map(function(p){var t=p.id;return{type:"Feature",id:t,properties:{id:t},geometry:{type:"Point",coordinates:[p.longitude,p.latitude]}}})};function N(c){var p=c.path;switch(p){case"/search/hotels":return P.By.mapViewHotelSearch;case"/search/offers":return P.By.mapViewStayOffersSearch;case"/destinations-map":return"Destinations Map";default:return P.By.mapViewHotelPage}}function T(c){var p,t,Z,i=c.markers,V=c.isLoading,k=c.onPinClick,w=c.initialPopupHotelId,O=c.heightScreen,re=c.hotelsSearchPage,S=c.onClickContainerMap,W=(0,l.useState)(),e=W[0],J=W[1],j=(0,l.useState)(!1),z=j[0],U=j[1],_=(0,l.useRef)(null),L=(0,l.useState)([]),A=L[0],oe=L[1],u=(0,l.useRef)(),B=(0,l.useState)(),s=B[0],b=B[1],f=(0,l.useRef)(),M=(0,ee.Z)(),fe=(0,R.Z)(),ve=(0,te.Z)(),ie=(0,$.Z)(),me=(0,H.v9)(function(o){return o.global.currency.rates}),ae=(0,H.v9)(function(o){return o.global.currency.selectedCurrency}),he=(0,K.Z)(ae,me),le=(0,H.v9)(function(o){return o.booking.hotel.params}),d=(p=i.find(function(o){return o.id===s}))===null||p===void 0?void 0:p.hotel,ue=(0,h.eg)(d==null?void 0:d.sabreTaxSetup),ce=d==null?void 0:d.priceLoaded,ge=d==null||(t=d.priceInfo)===null||t===void 0?void 0:t[ue.dailyPrice],be=d==null||(Z=d.priceInfo)===null||Z===void 0?void 0:Z[ue.dailyMemberPrice];(0,l.useEffect)(function(){if(u.current&&e){var o,n=document.createElement("div");n.className=m().popup,d&&D.render((0,v.jsx)(E.Z,{hotel:d,favoriteUse:ie,converter:he,bookingParams:le,trackAddToWishList:M,trackSelectItem:ve}),n),(o=u.current)===null||o===void 0||o.setDOMContent(n)}d&&ce&&fe.trackViewItemList({listName:N({path:window.location.pathname}),hotels:[d],hotelSearchData:le})},[s,ae,ce,ge,be,d==null?void 0:d.priceLoading,s&&ie.isFavorite(s)]),(0,l.useEffect)(function(){var o=!1,n=null,g=function(a){var I=function(){o=!0,n&&(clearTimeout(n),n=null)},de=function(){o=!1,n=setTimeout(function(){if(!o){var ne;(ne=u.current)===null||ne===void 0||ne.remove(),b(void 0)}},100)};if(e){for(var pe=a.features[0].properties.id,q=a.features[0].geometry.coordinates.slice();Math.abs(a.lngLat.lng-q[0])>180;)q[0]+=a.lngLat.lng>q[0]?360:-360;u.current||(u.current=new(y()).Popup({closeButton:!1,className:m().popup,closeOnMove:!1,offset:18,maxWidth:"320px",focusAfterOpen:!0}).on("close",function(){return b(void 0)})),u.current.setLngLat(q).addTo(e);var F=u.current.getElement();F&&(F.removeEventListener("mouseenter",I),F.removeEventListener("mouseleave",de),F.addEventListener("mouseenter",I),F.addEventListener("mouseleave",de)),k==null||k(pe),b(pe)}},x=function(){n=setTimeout(function(){if(!o){var a;(a=u.current)===null||a===void 0||a.remove(),b(void 0)}},100)};return e==null||e.on("mouseenter","unclustered-point",g),e==null||e.on("mouseleave","unclustered-point",x),function(){e==null||e.off("mouseenter","unclustered-point",g),e==null||e.off("mouseleave","unclustered-point",x)}},[i]),(0,l.useEffect)(function(){e?ye():J(xe())},[e]),(0,l.useEffect)(function(){if(e){var o=e==null?void 0:e.getSource("hotel_markers");o&&o.setData({type:"FeatureCollection",features:C(i)})}},[e]),(0,l.useEffect)(function(){return function(){e==null||e.remove()}},[]),(0,l.useEffect)(function(){if(!(i!=null&&i.length)||A.length!==i.length){se();return}var o=function(x,a){return x+a.id},n=A.reduce(o,""),g=i.reduce(o,"");n!==g&&se()},[e,i,z]),(0,l.useEffect)(function(){e&&(s&&e.setFeatureState({source:"hotel_markers",id:s},{isActive:!0}),f.current&&e.setFeatureState({source:"hotel_markers",id:f.current},{isActive:!1}),f.current=s)},[e,s]);var xe=function(){if(_.current){var o={projection:{name:"mercator"},container:_.current,style:"mapbox://styles/maksym-maltsev/clgkmhjmg007h01qq4u4834ob",zoom:2,attributionControl:!1};i&&i.length>0&&(o.center=[i[0].longitude,i[0].latitude]);var n=new(y()).Map(o);return n.addControl(new(y()).FullscreenControl,"top-right"),n}},ye=function(){e&&(e.on("load",function(){e.addSource("hotel_markers",{type:"geojson",data:{type:"FeatureCollection",features:C(i)},cluster:!0,clusterMaxZoom:14,clusterRadius:50}),e.addLayer({id:"clusters",type:"circle",source:"hotel_markers",filter:["has","point_count"],paint:{"circle-color":"#300B5C","circle-radius":18,"circle-opacity":.8}}),e.addLayer({id:"cluster-count",type:"symbol",source:"hotel_markers",filter:["has","point_count"],paint:{"text-color":"#fff"},layout:{"text-field":"{point_count}","text-font":["Jost Regular"],"text-size":18}}),e.addLayer({id:"unclustered-point",type:"circle",source:"hotel_markers",filter:["!",["has","point_count"]],paint:{"circle-color":["case",["boolean",["feature-state","isActive"],!1],"#bbb0dc","#300B5C"],"circle-radius":18,"circle-opacity":["case",["boolean",["feature-state","isActive"],!1],1,.8]}}),e.addLayer({id:"unclustered-letter",type:"symbol",source:"hotel_markers",filter:["!",["has","point_count"]],paint:{"icon-opacity":1,"icon-translate":[1.3,-.7]},layout:{"icon-image":"dIcon","icon-allow-overlap":!0}}),U(!0);var o=["clusters","cluster-count","unclustered-point","unclustered-letter"];e.on("mouseover",o,function(){return e.getCanvas().style.cursor="pointer"}),e.on("mouseleave",o,function(){return e.getCanvas().style.cursor=""})}),e.on("click","clusters",function(o){var n,g=e.queryRenderedFeatures(o.point,{layers:["clusters"]}),x=(n=g[0].properties)===null||n===void 0?void 0:n.cluster_id;(e==null?void 0:e.getSource("hotel_markers")).getClusterExpansionZoom(x,function(a,I){a||e.easeTo({center:g[0].geometry.coordinates,zoom:I+1})})}))},se=function(){if(e&&z){if(!V&&i.length){var o=i.map(function(a){return[a.longitude,a.latitude]}).reduce(function(a,I){return a.extend(I)},new(y()).LngLatBounds);if(e==null||e.fitBounds(o,{padding:100,maxZoom:12}),w){var n=i.find(function(a){return a.id===w});if(!n)return;var g=[n.longitude,n.latitude];u.current=u.current||new(y()).Popup({closeButton:!1,className:m().popup,closeOnMove:!1,offset:18,anchor:"top",maxWidth:"320px",focusAfterOpen:!0}).on("close",function(){return b(void 0)}),u.current.setLngLat(g).addTo(e),e.flyTo({center:g,offset:[0,-100],duration:300,curve:0}),b(w),k==null||k(w)}}var x=e==null?void 0:e.getSource("hotel_markers");x==null||x.setData({type:"FeatureCollection",features:C(i)}),oe(i)}};return re?(0,v.jsx)("div",{className:"w-full absolute z-20 left-0 md:relative md:px-6 md:w-full ".concat(m().containerMapBoxHotel," ").concat(O&&m().heightScreen),onClick:S,children:(0,v.jsx)("div",{ref:_,className:"h-full w-full shadow-lg ".concat(m().mapbox)})}):(0,v.jsx)("div",{ref:_,className:"h-full w-full "+m().mapbox,style:{borderRadius:"14px"}})}},79466:function(Y,Q,r){r.d(Q,{Z:function(){return te}});var v=r(85893),l=r(67294),G=r(77058),y=r(61695),D=r(33779),X=r(60023),m=r(96879),E=r(63419),$=r(43369),H=r(59007),K=r(93942),ee=r(52294),R=r(53547),P=r(71626);function te(h){var C,N,T,c,p,t=h.hotel,Z=h.className,i=h.favoriteUse,V=h.converter,k=h.bookingParams,w=h.newHotel,O=h.trackAddToWishList,re=h.trackSelectItem,S=(0,G.$)().t,W=i.isFavorite,e=i.favoriteClick,J=(0,l.useState)(!0),j=J[0],z=J[1];if(!t)return null;var U=(0,P.eg)(t==null?void 0:t.sabreTaxSetup),_=(C=t.priceInfo)===null||C===void 0?void 0:C[U.dailyPrice],L=t==null||(N=t.priceInfo)===null||N===void 0?void 0:N[U.dailyMemberPrice],A=t.priceLoaded&&(!t.priceInfo||!_&&!L),oe=t.priceLoaded&&!!k.promoCode&&t.priceInfo&&!t.priceInfo.promoValid,u=[];t.priceLoaded&&t.priceInfo&&(_&&u.push({label:S(L?"price.non_member":"price.from"),price:V.getPriceWithCode(_,t.priceInfo.currency,void 0,void 0,void 0,!0),color:"dark"}),L&&u.push({label:S("price.member"),price:V.getPriceWithCode(L,t.priceInfo.currency,void 0,void 0,void 0,!0),color:"gold"}));var B=t._location.parentLocation.content,s=void 0;t.suppressIbe?s=(0,v.jsx)(ee.C,{text:t.suppressIbeMessage,textType:"body2",className:"px-3 pt-1 pb-5 tid-hotelSuppressed"}):oe?s=(0,v.jsx)(E.Z,{validationHint:S("error.promo_unavailable"),className:"px-3 tid-promoUnavailable pt-1 pb-5"}):A&&B.isCustomNoAvailErrorMessage&&(!((T=B.customNoAvailErrorMessage)===null||T===void 0)&&T.html5)?s=(0,v.jsx)(E.Z,{validationHintHtml:B.customNoAvailErrorMessage.html5,className:"px-5 tid-promoUnavailable pt-1 pb-4"}):A?s=(0,v.jsx)(E.Z,{validationHint:S("error.hotel_unavailable"),className:"px-3 pt-1 pb-5 tid-hotelUnavailable"}):t.priceLoading&&(s=(0,v.jsx)($.Z,{center:!0,className:"pb-4 transform scale-75 -mt-3"})),(0,l.useEffect)(function(){!j&&O&&O.trackAddToWishList({hotel:t,listName:(0,R.u)({path:window.location.pathname}),bookingParams:k})},[j]);var b=function(f){var M=f.ctaName;t&&re.trackSelectItem({hotel:t,listName:(0,R.u)({path:window.location.pathname}),bookingParams:k,ctaName:M})};return(0,v.jsx)("div",{className:"mb-5",children:(0,v.jsx)(y.Z,{cardImageRound:{topLeft:!0,topRight:!0},onClick:function(){return b({ctaName:"Map View Card-title"})},onImageClick:function(){return b({ctaName:"Map View Card"})},className:(Z||"")+" h-full",shadowOnHoverEnabled:!0,overlay:{backgroundColor:"subtleBlack",degrees:180},cardDirection:void 0,favoriteProps:{showFavorite:!0,favorite:W(t._info.id),onFavoriteClick:function(){return z(e(t._info.id,H.r.hotel))},fill:"text-white"},label:void 0,cardClickHref:t._url,images:t.photos?t.photos.map(function(f){var M;return!(f==null||(M=f.image)===null||M===void 0)&&M.uri?{image:(0,D.bl)(f.image.uri,m.ie),imageAlt:f.image.alternativeText}:{image:""}}).filter(function(f){return!!(f!=null&&f.image)}).slice(0,5):void 0,image:t.photos?(0,D.bl)((c=t.photos[0])===null||c===void 0?void 0:c.image.uri,m.ie):void 0,imageAlt:(p=t.photos[0])===null||p===void 0?void 0:p.image.alternativeText,hotelBrandLogo:(0,K.UH)(t),superHeading:void 0,mainHeading:t.name,place:(0,X.AC)(t.city),prices:u.length>0?u:void 0,children:s,pinPopupView:!0,newHotel:w!==void 0&&w})})}},84381:function(Y){Y.exports={popup:"DestinationsMapBox_popup__Cs3Hu",mapbox:"DestinationsMapBox_mapbox__53L4x",containerMapBoxHotel:"DestinationsMapBox_containerMapBoxHotel___tlB0",heightScreen:"DestinationsMapBox_heightScreen__Begtj",containerMapBoxOffer:"DestinationsMapBox_containerMapBoxOffer__kbgY8"}}}]);
