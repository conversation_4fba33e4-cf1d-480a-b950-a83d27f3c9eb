import{j as r}from"./hotel-item-D_zvdyIk.js";import{c as m}from"./client-CHYie6Ha.js";import{r as a}from"./PeopleSelectPopover-CDKo4AC-.js";import{P as s}from"./panel-travel-DVI-w3iv.js";import{P as l}from"./panel-hotel-DVI-w3iv.js";import{P as p}from"./panel-communication-DVI-w3iv.js";import{G as n}from"./GhaConfigProvider-BB5IW5PM.js";import{T as c}from"./index-CMZw1aDs.js";import"./index-C_TizibV.js";import"./index-DoO4tVvP.js";import"./genStyleUtils-CI3YU7Yv.js";import"./isVisible-Bd4H7hpW.js";import"./useMergedState-BDSe6zqT.js";import"./index-DKvg8qd3.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./context-Gzj2nObQ.js";import"./row-BiveCJJi.js";import"./index-DR0pJ98a.js";import"./useBreakpoint-D48_IWaH.js";import"./CloseOutlined-CGWqYTdG.js";import"./button-C2fNxKeA.js";import"./useSize-CbUlsqBW.js";import"./color-DKTup0-d.js";import"./KeyCode-lh1qUinJ.js";import"./pickAttrs-D1C8emUZ.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./index-DxcGRMc6.js";import"./ContextIsolator-CVrwwDX4.js";import"./zoom-BbRr1fkt.js";import"./extendsObject-78o_rR5W.js";import"./EllipsisOutlined-cRWH5_fX.js";import"./index-CaUFHQr4.js";import"./Overflow-YL9RZFGj.js";function d(){const[i,e]=a.useState("travel"),o=[{key:"travel",label:"旅行喜好",children:r.jsx(s,{})},{key:"hotel",label:"住宿喜好",children:r.jsx(l,{})},{key:"communication",label:"通讯喜好",children:r.jsx(p,{})}];return r.jsxs("div",{className:"user-dashboard-wrap",children:[r.jsx("div",{className:"flex flex-row items-center lg:hidden -mx-1.5 min-w-0",children:o.map(t=>r.jsx("div",{className:"flex-1 mx-1.5",children:r.jsx("a",{href:"javascript:void;",onClick:()=>e(t.key),className:`${i===t.key?"gha-primary-btn":"gha-btn"} !py-1.5 inline-block w-full`,children:t.label},t.key)}))}),r.jsx("div",{className:"mt-4 lg:mt-0",children:r.jsx(c,{items:o,onChange:e,activeKey:i})})]})}m.createRoot(document.querySelector("#settings")).render(r.jsx(n,{children:r.jsx(d,{})}));
