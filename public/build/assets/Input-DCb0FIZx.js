import{R as i,r as f}from"./PeopleSelectPopover-CDKo4AC-.js";import{e as p,q as ye,l as j,c as ge,h as X,_ as me,n as Ne,i as Re,k as _e}from"./genStyleUtils-CI3YU7Yv.js";import{_ as Se,o as Fe,D as Ie}from"./isVisible-Bd4H7hpW.js";import{u as Ae}from"./useMergedState-BDSe6zqT.js";import{C as pe}from"./ContextIsolator-CVrwwDX4.js";import{R as Oe}from"./pickAttrs-D1C8emUZ.js";import{u as $e,a as Be,b as je,g as xe,c as ke}from"./index-wg7qNA5H.js";import{u as Pe}from"./useCSSVarCls-DWPRWpfJ.js";import{u as De}from"./useSize-CbUlsqBW.js";import{F as ze}from"./context-Gzj2nObQ.js";import{u as Ke}from"./color-DKTup0-d.js";function Te(e){return!!(e.addonBefore||e.addonAfter)}function Me(e){return!!(e.prefix||e.suffix||e.allowClear)}function he(e,a,r){var n=a.cloneNode(!0),t=Object.create(e,{target:{value:n},currentTarget:{value:n}});return n.value=r,typeof a.selectionStart=="number"&&typeof a.selectionEnd=="number"&&(n.selectionStart=a.selectionStart,n.selectionEnd=a.selectionEnd),n.setSelectionRange=function(){a.setSelectionRange.apply(a,arguments)},t}function Ce(e,a,r,n){if(r){var t=a;if(a.type==="click"){t=he(a,e,""),r(t);return}if(e.type!=="file"&&n!==void 0){t=he(a,e,n),r(t);return}r(t)}}function Ve(e,a){if(e){e.focus(a);var r=a||{},n=r.cursor;if(n){var t=e.value.length;switch(n){case"start":e.setSelectionRange(0,0);break;case"end":e.setSelectionRange(t,t);break;default:e.setSelectionRange(0,t)}}}}var We=i.forwardRef(function(e,a){var r,n,t,x=e.inputElement,h=e.children,v=e.prefixCls,A=e.prefix,O=e.suffix,b=e.addonBefore,$=e.addonAfter,Y=e.className,se=e.style,T=e.disabled,M=e.readOnly,Z=e.focused,k=e.triggerFocus,C=e.allowClear,U=e.value,H=e.handleReset,_=e.hidden,u=e.classes,l=e.classNames,P=e.dataAttrs,w=e.styles,y=e.components,D=e.onClear,ee=h??x,s=(y==null?void 0:y.affixWrapper)||"span",V=(y==null?void 0:y.groupWrapper)||"span",E=(y==null?void 0:y.wrapper)||"span",d=(y==null?void 0:y.groupAddon)||"span",F=f.useRef(null),G=function(z){var L;(L=F.current)!==null&&L!==void 0&&L.contains(z.target)&&(k==null||k())},te=Me(e),N=f.cloneElement(ee,{value:U,className:p((r=ee.props)===null||r===void 0?void 0:r.className,!te&&(l==null?void 0:l.variant))||null}),W=f.useRef(null);if(i.useImperativeHandle(a,function(){return{nativeElement:W.current||F.current}}),te){var R=null;if(C){var q=!T&&!M&&U,J="".concat(v,"-clear-icon"),ne=ye(C)==="object"&&C!==null&&C!==void 0&&C.clearIcon?C.clearIcon:"✖";R=i.createElement("button",{type:"button",tabIndex:-1,onClick:function(z){H==null||H(z),D==null||D()},onMouseDown:function(z){return z.preventDefault()},className:p(J,j(j({},"".concat(J,"-hidden"),!q),"".concat(J,"-has-suffix"),!!O))},ne)}var S="".concat(v,"-affix-wrapper"),ue=p(S,j(j(j(j(j({},"".concat(v,"-disabled"),T),"".concat(S,"-disabled"),T),"".concat(S,"-focused"),Z),"".concat(S,"-readonly"),M),"".concat(S,"-input-with-clear-btn"),O&&C&&U),u==null?void 0:u.affixWrapper,l==null?void 0:l.affixWrapper,l==null?void 0:l.variant),m=(O||C)&&i.createElement("span",{className:p("".concat(v,"-suffix"),l==null?void 0:l.suffix),style:w==null?void 0:w.suffix},R,O);N=i.createElement(s,ge({className:ue,style:w==null?void 0:w.affixWrapper,onClick:G},P==null?void 0:P.affixWrapper,{ref:F}),A&&i.createElement("span",{className:p("".concat(v,"-prefix"),l==null?void 0:l.prefix),style:w==null?void 0:w.prefix},A),N,m)}if(Te(e)){var I="".concat(v,"-group"),B="".concat(I,"-addon"),ae="".concat(I,"-wrapper"),oe=p("".concat(v,"-wrapper"),I,u==null?void 0:u.wrapper,l==null?void 0:l.wrapper),ie=p(ae,j({},"".concat(ae,"-disabled"),T),u==null?void 0:u.group,l==null?void 0:l.groupWrapper);N=i.createElement(V,{className:ie,ref:W},i.createElement(E,{className:oe},b&&i.createElement(d,{className:B},b),N,$&&i.createElement(d,{className:B},$)))}return i.cloneElement(N,{className:p((n=N.props)===null||n===void 0?void 0:n.className,Y)||null,style:X(X({},(t=N.props)===null||t===void 0?void 0:t.style),se),hidden:_})}),Le=["show"];function Ue(e,a){return f.useMemo(function(){var r={};a&&(r.show=ye(a)==="object"&&a.formatter?a.formatter:!!a),r=X(X({},r),e);var n=r,t=n.show,x=Se(n,Le);return X(X({},x),{},{show:!!t,showFormatter:typeof t=="function"?t:void 0,strategy:x.strategy||function(h){return h.length}})},[e,a])}var He=["autoComplete","onChange","onFocus","onBlur","onPressEnter","onKeyDown","onKeyUp","prefixCls","disabled","htmlSize","className","maxLength","suffix","showCount","count","type","classes","classNames","styles","onCompositionStart","onCompositionEnd"],Ge=f.forwardRef(function(e,a){var r=e.autoComplete,n=e.onChange,t=e.onFocus,x=e.onBlur,h=e.onPressEnter,v=e.onKeyDown,A=e.onKeyUp,O=e.prefixCls,b=O===void 0?"rc-input":O,$=e.disabled,Y=e.htmlSize,se=e.className,T=e.maxLength,M=e.suffix,Z=e.showCount,k=e.count,C=e.type,U=C===void 0?"text":C,H=e.classes,_=e.classNames,u=e.styles,l=e.onCompositionStart,P=e.onCompositionEnd,w=Se(e,He),y=f.useState(!1),D=me(y,2),ee=D[0],s=D[1],V=f.useRef(!1),E=f.useRef(!1),d=f.useRef(null),F=f.useRef(null),G=function(o){d.current&&Ve(d.current,o)},te=Ae(e.defaultValue,{value:e.value}),N=me(te,2),W=N[0],R=N[1],q=W==null?"":String(W),J=f.useState(null),ne=me(J,2),S=ne[0],ue=ne[1],m=Ue(k,Z),I=m.max||T,B=m.strategy(q),ae=!!I&&B>I;f.useImperativeHandle(a,function(){var c;return{focus:G,blur:function(){var g;(g=d.current)===null||g===void 0||g.blur()},setSelectionRange:function(g,de,le){var Q;(Q=d.current)===null||Q===void 0||Q.setSelectionRange(g,de,le)},select:function(){var g;(g=d.current)===null||g===void 0||g.select()},input:d.current,nativeElement:((c=F.current)===null||c===void 0?void 0:c.nativeElement)||d.current}}),f.useEffect(function(){E.current&&(E.current=!1),s(function(c){return c&&$?!1:c})},[$]);var oe=function(o,g,de){var le=g;if(!V.current&&m.exceedFormatter&&m.max&&m.strategy(g)>m.max){if(le=m.exceedFormatter(g,{max:m.max}),g!==le){var Q,ve;ue([((Q=d.current)===null||Q===void 0?void 0:Q.selectionStart)||0,((ve=d.current)===null||ve===void 0?void 0:ve.selectionEnd)||0])}}else if(de.source==="compositionEnd")return;R(le),d.current&&Ce(d.current,o,n,le)};f.useEffect(function(){if(S){var c;(c=d.current)===null||c===void 0||c.setSelectionRange.apply(c,Ne(S))}},[S]);var ie=function(o){oe(o,o.target.value,{source:"change"})},re=function(o){V.current=!1,oe(o,o.currentTarget.value,{source:"compositionEnd"}),P==null||P(o)},z=function(o){h&&o.key==="Enter"&&!E.current&&(E.current=!0,h(o)),v==null||v(o)},L=function(o){o.key==="Enter"&&(E.current=!1),A==null||A(o)},fe=function(o){s(!0),t==null||t(o)},K=function(o){E.current&&(E.current=!1),s(!1),x==null||x(o)},ce=function(o){R(""),G(),d.current&&Ce(d.current,o,n)},be=ae&&"".concat(b,"-out-of-range"),we=function(){var o=Fe(e,["prefixCls","onPressEnter","addonBefore","addonAfter","prefix","suffix","allowClear","defaultValue","showCount","count","classes","htmlSize","styles","classNames","onClear"]);return i.createElement("input",ge({autoComplete:r},o,{onChange:ie,onFocus:fe,onBlur:K,onKeyDown:z,onKeyUp:L,className:p(b,j({},"".concat(b,"-disabled"),$),_==null?void 0:_.input),style:u==null?void 0:u.input,ref:d,size:Y,type:U,onCompositionStart:function(de){V.current=!0,l==null||l(de)},onCompositionEnd:re}))},Ee=function(){var o=Number(I)>0;if(M||m.show){var g=m.showFormatter?m.showFormatter({value:q,count:B,maxLength:I}):"".concat(B).concat(o?" / ".concat(I):"");return i.createElement(i.Fragment,null,m.show&&i.createElement("span",{className:p("".concat(b,"-show-count-suffix"),j({},"".concat(b,"-show-count-has-suffix"),!!M),_==null?void 0:_.count),style:X({},u==null?void 0:u.count)},g),M)}return null};return i.createElement(We,ge({},w,{prefixCls:b,className:p(se,be),handleReset:ce,value:q,focused:ee,triggerFocus:G,suffix:Ee(),disabled:$,classes:H,classNames:_,styles:u,ref:F}),we())});const qe=e=>{let a;return typeof e=="object"&&(e!=null&&e.clearIcon)?a=e:e&&(a={clearIcon:i.createElement(Oe,null)}),a};function Je(e,a){const r=f.useRef([]),n=()=>{r.current.push(setTimeout(()=>{var t,x,h,v;!((t=e.current)===null||t===void 0)&&t.input&&((x=e.current)===null||x===void 0?void 0:x.input.getAttribute("type"))==="password"&&(!((h=e.current)===null||h===void 0)&&h.input.hasAttribute("value"))&&((v=e.current)===null||v===void 0||v.input.removeAttribute("value"))}))};return f.useEffect(()=>(a&&n(),()=>r.current.forEach(t=>{t&&clearTimeout(t)})),[]),n}function Qe(e){return!!(e.prefix||e.suffix||e.allowClear||e.showCount)}var Xe=function(e,a){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&a.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var t=0,n=Object.getOwnPropertySymbols(e);t<n.length;t++)a.indexOf(n[t])<0&&Object.prototype.propertyIsEnumerable.call(e,n[t])&&(r[n[t]]=e[n[t]]);return r};const it=f.forwardRef((e,a)=>{const{prefixCls:r,bordered:n=!0,status:t,size:x,disabled:h,onBlur:v,onFocus:A,suffix:O,allowClear:b,addonAfter:$,addonBefore:Y,className:se,style:T,styles:M,rootClassName:Z,onChange:k,classNames:C,variant:U}=e,H=Xe(e,["prefixCls","bordered","status","size","disabled","onBlur","onFocus","suffix","allowClear","addonAfter","addonBefore","className","style","styles","rootClassName","onChange","classNames","variant"]),{getPrefixCls:_,direction:u,allowClear:l,autoComplete:P,className:w,style:y,classNames:D,styles:ee}=Re("input"),s=_("input",r),V=f.useRef(null),E=Pe(s),[d,F,G]=$e(s,Z),[te]=Be(s,E),{compactSize:N,compactItemClassnames:W}=Ke(s,u),R=De(K=>{var ce;return(ce=x??N)!==null&&ce!==void 0?ce:K}),q=i.useContext(Ie),J=h??q,{status:ne,hasFeedback:S,feedbackIcon:ue}=f.useContext(ze),m=ke(ne,t),I=Qe(e)||!!S;f.useRef(I);const B=Je(V,!0),ae=K=>{B(),v==null||v(K)},oe=K=>{B(),A==null||A(K)},ie=K=>{B(),k==null||k(K)},re=(S||O)&&i.createElement(i.Fragment,null,O,S&&ue),z=qe(b??l),[L,fe]=je("input",U,n);return d(te(i.createElement(Ge,Object.assign({ref:_e(a,V),prefixCls:s,autoComplete:P},H,{disabled:J,onBlur:ae,onFocus:oe,style:Object.assign(Object.assign({},y),T),styles:Object.assign(Object.assign({},ee),M),suffix:re,allowClear:z,className:p(se,Z,G,E,W,w),onChange:ie,addonBefore:Y&&i.createElement(pe,{form:!0,space:!0},Y),addonAfter:$&&i.createElement(pe,{form:!0,space:!0},$),classNames:Object.assign(Object.assign(Object.assign({},C),D),{input:p({[`${s}-sm`]:R==="small",[`${s}-lg`]:R==="large",[`${s}-rtl`]:u==="rtl"},C==null?void 0:C.input,D.input,F),variant:p({[`${s}-${L}`]:fe},xe(s,m)),affixWrapper:p({[`${s}-affix-wrapper-sm`]:R==="small",[`${s}-affix-wrapper-lg`]:R==="large",[`${s}-affix-wrapper-rtl`]:u==="rtl"},F),wrapper:p({[`${s}-group-rtl`]:u==="rtl"},F),groupWrapper:p({[`${s}-group-wrapper-sm`]:R==="small",[`${s}-group-wrapper-lg`]:R==="large",[`${s}-group-wrapper-rtl`]:u==="rtl",[`${s}-group-wrapper-${L}`]:fe},xe(`${s}-group-wrapper`,m,S),F)})}))))});export{We as B,it as I,Ue as a,qe as g,Ce as r,Ve as t,Je as u};
