import{j as r}from"./hotel-item-D_zvdyIk.js";import{c as d}from"./client-CHYie6Ha.js";import{r as i}from"./PeopleSelectPopover-CDKo4AC-.js";import{G as x,u as h}from"./GhaConfigProvider-BB5IW5PM.js";import{$ as f}from"./_constants-CI6xcXYb.js";import{$ as j,a as b}from"./helper-D414uohx.js";import{F as e}from"./index-CC5HyPSW.js";import{I as a}from"./index-DNIuBGmG.js";import{B as g}from"./button-C2fNxKeA.js";import"./index-C_TizibV.js";import"./isVisible-Bd4H7hpW.js";import"./genStyleUtils-CI3YU7Yv.js";import"./CloseOutlined-CGWqYTdG.js";import"./KeyCode-lh1qUinJ.js";import"./pickAttrs-D1C8emUZ.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./index-DxcGRMc6.js";import"./index-DKvg8qd3.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./color-DKTup0-d.js";import"./useSize-CbUlsqBW.js";import"./zoom-BbRr1fkt.js";import"./extendsObject-78o_rR5W.js";import"./index-DR0pJ98a.js";import"./index-Dq7h7Pqt.js";import"./collapse-BbEVqHco.js";import"./useForm-DF1XNZWJ.js";import"./row-BiveCJJi.js";import"./useBreakpoint-D48_IWaH.js";import"./index-CJQq4WAU.js";import"./index-CaUFHQr4.js";import"./useMergedState-BDSe6zqT.js";import"./roundedArrow-DVJD-5zd.js";import"./index-wg7qNA5H.js";import"./Input-DCb0FIZx.js";function v(){const[m,o]=i.useState(1),[n]=e.useForm(),[l,t]=i.useState(!1),c=h();function p(u){t(!0),b.authFindAccount(u).subscribe(s=>{if(t(!1),s.status_code!==200){c.error(s.message);return}o(2)})}return r.jsx("div",{className:"auth-box auth-box-400",children:r.jsx("div",{className:"form-wrap",children:m===1?r.jsxs(r.Fragment,{children:[r.jsx("h3",{className:"text-center",children:"找回您的在线账户"}),r.jsxs("h4",{className:"text-center font-12 -mt-2.5",children:["请输入用户姓氏和电子邮件地址",r.jsx("br",{}),"我们将会发送含有您的GHA DISCOVERY会籍号的邮件到您的邮箱"]}),r.jsx("div",{className:"mt-5",children:r.jsxs(e,{layout:"vertical",form:n,requiredMark:!1,onFinish:p,initialValues:j.isLaravelLocal()?{last_name:"wchy",email:"<EMAIL>"}:{},children:[r.jsx(e.Item,{label:"姓氏*",name:"last_name",rules:[{required:!0,message:"请输入您的姓"}],children:r.jsx(a,{variant:"underlined",placeholder:"请输入您的姓氏"})}),r.jsx(e.Item,{label:"邮箱*",name:"email",rules:[{required:!0,message:"请输入您的邮箱"},{pattern:f.emailPattern,message:"邮箱格式不正确"}],children:r.jsx(a,{variant:"underlined",placeholder:"请输入您的邮箱"})}),r.jsx(e.Item,{label:null,children:r.jsx(g,{loading:l,className:"gha-primary-btn",type:"primary",shape:"round",block:!0,htmlType:"submit",children:"下一步"})})]})})]}):r.jsxs("div",{className:"flex flex-col items-center justify-center",children:[r.jsxs("p",{className:"font-12 mb-5 text-center",children:["我们已向您发送了一封",r.jsx("br",{}),"含有GHA DISCOVERY会籍号的电子邮件",r.jsx("br",{}),"请继续激活您的在线账户"]}),r.jsx("a",{href:window.__ServerVars__.activeUri,className:"gha-primary-btn w-full mt-5",children:"激活在线账户"})]})})})}d.createRoot(document.querySelector(".auth-box-wrap")).render(r.jsx(x,{children:r.jsx(v,{})}));
