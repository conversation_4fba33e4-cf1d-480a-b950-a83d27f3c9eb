import{j as r}from"./hotel-item-D_zvdyIk.js";import"./mock-OVdH_lkV.js";import{r as c}from"./PeopleSelectPopover-CDKo4AC-.js";import{$ as v}from"./helper-D414uohx.js";import{c as $}from"./react-ZNplgrTR.js";import"./index-Dq7h7Pqt.js";const h=[{type:"categories",name:"酒店类型",tags:[{id:"all",name:"显示全部"}].concat(window.__ServerVars__.categories)},{type:"brands",name:"酒店品牌",tags:[{id:"all",name:"显示全部"}].concat(window.__ServerVars__.brands)},{type:"series",name:"酒店系列",tags:[{id:"all",name:"显示全部"}].concat(window.__ServerVars__.series)},{type:"facts",name:"酒店设施",tags:[{id:"all",name:"显示全部"}].concat(window.__ServerVars__.facts)}];function k({onCondChange:d}){const[i,o]=c.useState(!1),[m,_]=c.useState(5),[p]=c.useState(()=>[...h]),n=f(e=>e.selectTags),u=c.useMemo(()=>n.map(e=>{var a;const[s,t]=e.split("_");return{...(((a=p.find(l=>`${l.type}`===s))==null?void 0:a.tags)||[]).find(l=>`${l.id}`===t),type:s}}).filter(e=>`${e.id}`!="all"),[n,p]);c.useEffect(()=>{function e(){let s=0,t=0;const a=document.querySelectorAll(".virtual");for(let l=0;l<a.length;l++){if(a[l].offsetHeight>s&&s>0){t=l;break}s=a[l].offsetHeight}_(t)}window.onresize=function(){e()},e()},[]);function g(e,s){let t=[...n];s.id==="all"?(t=t.filter(a=>!a.startsWith(`${e.type}_`)),t.push(`${e.type}_all`)):t.includes(`${e.type}_${s.id}`)?(t=t.filter(a=>a!==`${e.type}_${s.id}`),t.filter(a=>a.startsWith(`${e.type}_`)).length===0&&t.push(`${e.type}_all`)):(t=t.filter(a=>a!==`${e.type}_all`),t.push(`${e.type}_${s.id}`)),f.setState({selectTags:t}),d({type:"tags",value:t})}function y(e){let s=[...n];s=s.filter(t=>t!==`${e.type}_${e.id}`),s.filter(t=>t.startsWith(`${e.type}_`)).length===0&&s.push(`${e.type}_all`),f.setState({selectTags:s}),d({type:"tags",value:s})}function x(){let e=p.map(s=>`${s.type}_all`);f.setState({selectTags:e}),d({type:"tags",value:e})}return r.jsx("div",{className:"pt-12",children:r.jsxs("div",{className:"g-main-content",children:[r.jsx("div",{className:"tag-filter-wrap",children:p.map((e,s)=>r.jsxs("div",{className:"filter-row",children:[r.jsx("div",{className:"filter-row-name",children:e.name}),r.jsxs("div",{className:"filter-row-tags-list relative",children:[e.tags.slice(0,i?9999:m).map(t=>r.jsx("div",{onClick:()=>g(e,t),className:`tag-item ${n.includes(`${e.type}_${t.id}`)?"active":""}`,children:t.name},t.id)),e.type==="brands"&&r.jsx("div",{className:"tag-item more",onClick:()=>o(!i),children:i?"收起":`显示全部品牌(${e.tags.length-1}个)`}),e.type==="brands"&&r.jsx(r.Fragment,{children:Array.from({length:e.tags.length}).map((t,a)=>r.jsxs("div",{className:`virtual virtual-${a} absolute top-0 left-0 right-0 opacity-0 -translate-x-[9999px]`,children:[e.tags.slice(0,a+2).map(l=>r.jsx("div",{className:"tag-item",children:l.name},l.id)),e.type==="brands"&&r.jsx("div",{className:"tag-item more",children:"显示全部品牌(28个)"})]},a))})]})]},e.type))}),u.length>0&&r.jsx("div",{className:"mt-2",children:r.jsxs("div",{className:"flex flex-row flex-wrap -mx-1",children:[u.map(e=>r.jsxs("p",{className:"mt-2 mx-1 py-1 pl-2.5 pr-2 rounded-full border border-primary text-primary font-12 flex items-center",children:[e.name,r.jsx("i",{onClick:()=>y(e),className:"iconfont icon-Close cursor-pointer ml-1"})]},e.id)),r.jsx("p",{onClick:()=>x(),className:"mt-2 mx-1 py-1 pl-2.5 pr-2 rounded-full text-primary font-12 flex items-center cursor-pointer",children:"清除"})]})})]})})}const f=$(d=>({slug:window.__ServerVars__.slug,selectTags:function(){const i=location.hash.split("?")[1],o=v.decodeSearchHash(i);return h.map(m=>m.type==="brands"&&o.brand_id?`brands_${o.brand_id}`:`${m.type}_all`)}(),order:"default",list:[],loading:!0,showType:"list"}));export{k as T,h as f,f as u};
