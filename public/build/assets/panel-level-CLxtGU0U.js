import{j as e}from"./hotel-item-D_zvdyIk.js";import{r as x}from"./PeopleSelectPopover-CDKo4AC-.js";import{g as L,m as T,r as H,a as m,i as G,e as R}from"./genStyleUtils-CI3YU7Yv.js";import{u as A}from"./useSize-CbUlsqBW.js";const V=t=>{const{componentCls:r}=t;return{[r]:{"&-horizontal":{[`&${r}`]:{"&-sm":{marginBlock:t.marginXS},"&-md":{marginBlock:t.margin}}}}}},F=t=>{const{componentCls:r,sizePaddingEdgeHorizontal:s,colorSplit:n,lineWidth:i,textPaddingInline:a,orientationMargin:o,verticalMarginInline:d}=t;return{[r]:Object.assign(Object.assign({},H(t)),{borderBlockStart:`${m(i)} solid ${n}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:d,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${m(i)} solid ${n}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${m(t.marginLG)} 0`},[`&-horizontal${r}-with-text`]:{display:"flex",alignItems:"center",margin:`${m(t.dividerHorizontalWithTextGutterMargin)} 0`,color:t.colorTextHeading,fontWeight:500,fontSize:t.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${n}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${m(i)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${r}-with-text-start`]:{"&::before":{width:`calc(${o} * 100%)`},"&::after":{width:`calc(100% - ${o} * 100%)`}},[`&-horizontal${r}-with-text-end`]:{"&::before":{width:`calc(100% - ${o} * 100%)`},"&::after":{width:`calc(${o} * 100%)`}},[`${r}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:a},"&-dashed":{background:"none",borderColor:n,borderStyle:"dashed",borderWidth:`${m(i)} 0 0`},[`&-horizontal${r}-with-text${r}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${r}-dashed`]:{borderInlineStartWidth:i,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:n,borderStyle:"dotted",borderWidth:`${m(i)} 0 0`},[`&-horizontal${r}-with-text${r}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${r}-dotted`]:{borderInlineStartWidth:i,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${r}-with-text`]:{color:t.colorText,fontWeight:"normal",fontSize:t.fontSize},[`&-horizontal${r}-with-text-start${r}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${r}-inner-text`]:{paddingInlineStart:s}},[`&-horizontal${r}-with-text-end${r}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${r}-inner-text`]:{paddingInlineEnd:s}}})}},X=t=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:t.marginXS}),Y=L("Divider",t=>{const r=T(t,{dividerHorizontalWithTextGutterMargin:t.margin,sizePaddingEdgeHorizontal:0});return[F(r),V(r)]},X,{unitless:{orientationMargin:!0}});var K=function(t,r){var s={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&r.indexOf(n)<0&&(s[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,n=Object.getOwnPropertySymbols(t);i<n.length;i++)r.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(t,n[i])&&(s[n[i]]=t[n[i]]);return s};const Q={small:"sm",middle:"md"},q=t=>{const{getPrefixCls:r,direction:s,className:n,style:i}=G("divider"),{prefixCls:a,type:o="horizontal",orientation:d="center",orientationMargin:c,className:S,rootClassName:N,children:h,dashed:z,variant:g="solid",plain:C,style:I,size:k}=t,M=K(t,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),l=r("divider",a),[E,O,B]=Y(l),D=A(k),p=Q[D],b=!!h,f=x.useMemo(()=>d==="left"?s==="rtl"?"end":"start":d==="right"?s==="rtl"?"start":"end":d,[s,d]),u=f==="start"&&c!=null,_=f==="end"&&c!=null,P=R(l,n,O,B,`${l}-${o}`,{[`${l}-with-text`]:b,[`${l}-with-text-${f}`]:b,[`${l}-dashed`]:!!z,[`${l}-${g}`]:g!=="solid",[`${l}-plain`]:!!C,[`${l}-rtl`]:s==="rtl",[`${l}-no-default-orientation-margin-start`]:u,[`${l}-no-default-orientation-margin-end`]:_,[`${l}-${p}`]:!!p},S,N),v=x.useMemo(()=>typeof c=="number"?c:/^\d+$/.test(c)?Number(c):c,[c]),W={marginInlineStart:u?v:void 0,marginInlineEnd:_?v:void 0};return E(x.createElement("div",Object.assign({className:P,style:Object.assign(Object.assign({},i),I)},M,{role:"separator"}),h&&o!=="vertical"&&x.createElement("span",{className:`${l}-inner-text`,style:W},h)))},w="/build/assets/card-1-DEicWzQM.png",$="/build/assets/card-2-DYfVLtWh.png",j="/build/assets/card-3-BG7sfDh0.png",y="/build/assets/card-4-5L_F9sKo.png";function te(){var n,i;const[t,r]=x.useState(window.__$userCardLevel__),[s]=x.useState(()=>{const a=[w,$,j,y];return window.memberShipRights.map((o,d)=>({...o,image:a[d]}))});return e.jsxs("div",{className:"pt-4 lg:pt-0 px-4 lg:px-10",children:[e.jsxs("div",{className:`level-step-container active-${t}`,children:[e.jsx("div",{className:"line-track",children:e.jsx("span",{})}),e.jsx("div",{className:"level-list",children:s.map(a=>e.jsxs("div",{className:"level-item",children:[e.jsx("h3",{children:a.title}),e.jsx("div",{className:"tracker !cursor-auto",onClick:()=>{},children:e.jsx("span",{})}),e.jsx("div",{className:"level-card",children:e.jsx("img",{src:a.image,alt:""})})]},a.key))}),e.jsxs("div",{className:"level-card-m",children:[e.jsx("img",{src:w,alt:""}),e.jsx("img",{src:$,alt:""}),e.jsx("img",{src:j,alt:""}),e.jsx("img",{src:y,alt:""})]})]}),e.jsxs("div",{className:"mt-8 pb-8 border-b border-[#919191]/20",children:[e.jsx(q,{plain:!0,className:"driver-919191-20",children:e.jsxs("h4",{className:"font-bold font-12",children:[(n=s.find(a=>a.key===t))==null?void 0:n.title,"会员福利"]})}),e.jsx("div",{className:"flex flex-row flex-wrap -mx-1.5 lg:-mx-2.5 -mt-2 lg:mt-8 px-4 lg:px-16",children:(((i=s.find(a=>a.key===t))==null?void 0:i.rights)||[]).map((a,o)=>e.jsxs("div",{className:"w-1/2 lg:w-1/4 flex flex-col items-center mt-4 px-1.5 lg:px-2.5",children:[e.jsx("div",{className:`w-11 h-11 rounded-full shadow-[0px_2px_4px_2px_rgba(0,0,0,0.05)] flex items-center justify-center ${a.active?"bg-primary text-white":""}`,children:e.jsx("i",{className:`iconfont ${a.icon} text-2xl`})}),e.jsx("p",{className:"text-center mt-2.5 font-12",children:a.text})]},o))})]}),e.jsxs("div",{className:"pt-8 flex flex-row items-stretch justify-center pb-8 lg:pb-2.5 -mx-2.5",children:[e.jsxs("div",{className:"flex-1 lg:w-60 py-7.5 mx-2.5 rounded-3xl border border-[#f2f2f2] shadow-[0_2px_4px_0px_rgba(0,0,0,0.08)] flex flex-col items-center justify-center",children:[e.jsxs("div",{className:"font-IvyMode-Reg rounded-full w-20 h-20 border-4 border-[#919191]/20 flex flex-row items-center justify-center font-12",children:[e.jsx("span",{className:"font-20",children:window.__Server_Data__.gha_user.member_balance.stays_needed_keep_status}),"/",window.__Server_Data__.gha_user.member_balance.stays_needed_next_tier]}),e.jsxs("p",{className:"mt-2.5 font-12",children:["再预订",window.__Server_Data__.gha_user.member_balance.stays_needed_next_tier,"次住宿即可升级"]})]}),e.jsxs("div",{className:"flex-1 lg:w-60 py-7.5 mx-2.5 rounded-3xl border border-[#f2f2f2] shadow-[0_2px_4px_0px_rgba(0,0,0,0.08)] flex flex-col items-center justify-center",children:[e.jsxs("div",{className:"font-IvyMode-Reg rounded-full w-20 h-20 border-4 border-[#919191]/20 flex flex-col items-center justify-center font-12 text-[#919191]",children:[e.jsxs("span",{className:"font-20 text-black leading-none",children:["$",window.__Server_Data__.gha_user.member_balance.revenue_needed_keep_status]}),"/$",window.__Server_Data__.gha_user.member_balance.revenue_needed_next_tier]}),e.jsxs("p",{className:"mt-2.5 font-12",children:["再消费",window.__Server_Data__.gha_user.member_balance.revenue_needed_next_tier,"美元即可升级"]})]})]})]})}export{te as P};
