import{j as e}from"./hotel-item-D_zvdyIk.js";import{c as s}from"./client-CHYie6Ha.js";import{O as r}from"./OrderDetailWidget-B4oVGJh8.js";import{O as t}from"./OrderPriceDetailWidget-fjlxL2dt.js";import{G as i}from"./GhaConfigProvider-BB5IW5PM.js";import"./PeopleSelectPopover-CDKo4AC-.js";import"./index-C_TizibV.js";import"./index-DVwr87ys.js";import"./index-DxcGRMc6.js";import"./genStyleUtils-CI3YU7Yv.js";import"./isVisible-Bd4H7hpW.js";import"./index-DKvg8qd3.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./button-C2fNxKeA.js";import"./useSize-CbUlsqBW.js";import"./color-DKTup0-d.js";import"./pickAttrs-D1C8emUZ.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./zoom-BbRr1fkt.js";import"./CloseOutlined-CGWqYTdG.js";import"./KeyCode-lh1qUinJ.js";import"./extendsObject-78o_rR5W.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./index-DR0pJ98a.js";import"./PurePanel-zvE2p4pp.js";import"./useMergedState-BDSe6zqT.js";function m(){return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"pt-12",children:e.jsx("div",{className:"g-main-content-sm",children:e.jsx("div",{className:"text-[#999] font-14",children:e.jsxs("a",{href:"javascript:;",className:"flex flex-row items-center",children:[e.jsx("i",{className:"iconfont icon-left font-20"}),"返回"]})})})}),e.jsx("div",{className:"mt-3",children:e.jsx("div",{className:"g-main-content-sm",children:e.jsxs("div",{className:"flex flex-row justify-stretch",children:[e.jsxs("div",{className:"border border-[#ebebeb] rounded-lg shadow-lg shadow-black/10 p-6 flex-1 flex flex-col",children:[e.jsxs("div",{className:"flex flex-col items-center font-14 font-medium pt-14 flex-1",children:[e.jsx("i",{className:"iconfont icon-a-14Benefits_brand_benefits text-5xl"}),e.jsx("h2",{className:"font-18 font-medium",children:"订单预约成功"}),e.jsx("p",{className:"mt-3",children:"订单确认函已发送至邮箱"}),e.jsx("p",{children:"<EMAIL>"}),e.jsx("p",{className:"mt-3",children:"订单号：GHA25556576454"}),e.jsx("p",{className:"text-[#999] font-normal",children:"请妥善保留单号以便日后查询订单"}),e.jsxs("p",{className:"text-primary mt-24",children:["通过此次预订，您可赚取",e.jsx("span",{className:"text-[#e69d4a]",children:"D$216.22"})]}),e.jsx("a",{href:"/booking/order-detail",className:"gha-primary-btn mt-4 !px-14 !py-1.5",children:"查看订单"}),e.jsx("a",{href:"/",className:"underline text-primary mt-4",children:"返回首页"})]}),e.jsxs("div",{className:"py-8 border-t border-[#c9c9c9]/50 text-center",children:[e.jsx("h4",{className:"font-semibold font-16",children:"取消及押金政策说明"}),e.jsxs("p",{className:"font-13 mt-2",children:["取消房价不可取消，押金预订时需缴纳 1049.40 元押金，以保证您的预订。",e.jsx("br",{}),"*押金金额基于您选择的金额币种：人民币税费含税 税费 服务费"]})]})]}),e.jsxs("div",{className:"flex flex-col lg:w-[320px] lg:ml-8",children:[e.jsx(r,{}),e.jsx(t,{})]})]})})})]})}s.createRoot(document.querySelector(".booking-success-content")).render(e.jsx(i,{children:e.jsx(m,{})}));
