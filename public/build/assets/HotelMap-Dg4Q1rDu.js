import{j as p}from"./hotel-item-D_zvdyIk.js";import{r as c}from"./PeopleSelectPopover-CDKo4AC-.js";import{H as b}from"./HotelMapBar-CGEY0e3y.js";import{u as k}from"./TagFilter-CavzMCRg.js";import"./helper-D414uohx.js";import{$ as s}from"./_map_helper-CAWV87xN.js";import"./GhaSearchBar-CE-42mVw.js";import"./index-CosgXXok.js";import"./PurePanel-zvE2p4pp.js";import"./useMergedState-BDSe6zqT.js";import"./genStyleUtils-CI3YU7Yv.js";import"./isVisible-Bd4H7hpW.js";import"./index-C_TizibV.js";import"./button-C2fNxKeA.js";import"./index-DKvg8qd3.js";import"./useSize-CbUlsqBW.js";import"./color-DKTup0-d.js";import"./pickAttrs-D1C8emUZ.js";import"./index-CaUFHQr4.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./Overflow-YL9RZFGj.js";import"./index-wg7qNA5H.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./useIcons-BIWMCRV_.js";import"./CloseOutlined-CGWqYTdG.js";import"./roundedArrow-DVJD-5zd.js";import"./index-BBp8sG_H.js";import"./KeyCode-lh1qUinJ.js";import"./zoom-BbRr1fkt.js";import"./react-ZNplgrTR.js";import"./index-PBsuAvuu.js";import"./index-CJQq4WAU.js";import"./zh-cn-DO3l3KOs.js";import"./index-DNIuBGmG.js";import"./Input-DCb0FIZx.js";import"./mock-OVdH_lkV.js";import"./index-Dq7h7Pqt.js";const H="/build/assets/d-icon-CG8PgV-2.png",ce=c.forwardRef(({dataSource:v,onHotelLike:f},h)=>{c.useRef(Math.random().toString(36).substr(2,9)).current;const a=c.useRef(v),m=k(e=>e.slug),x=c.useRef(null);c.useImperativeHandle(h,()=>({updateMapData(e){a.current=e,g()}})),c.useEffect(()=>{g()},[]),c.useEffect(()=>{$("body").on("click",".gha-mapbox-hotel-popup .fav-icon",function(){const e=$(this),t=e.data("id");if(e.hasClass("loading"))return;e.addClass("loading");const i=$(`.gha-mapbox-marker-${t} .fav-icon`);f(t,function(r,o){e.removeClass("loading").removeClass("icon-Heart").removeClass("icon-Heart-filled"),e.addClass(r?"icon-Heart-filled":"icon-Heart"),i.removeClass("icon-Heart").removeClass("icon-Heart-filled"),i.addClass(r?"icon-Heart-filled":"icon-Heart"),a.current=o})})},[]);function g(){$(".gha-mapbox").addClass("loading"),mapboxgl.accessToken="pk.eyJ1IjoiaWlpc2xlZSIsImEiOiJjbHJoN2Z3djMwbjY0MmptampmODRlcWdvIn0.yak7m5pJUycZ58aJUst7ag";const e=new mapboxgl.Map({container:"hotelMap",style:"mapbox://styles/mapbox/streets-v12",projection:{name:"mercator"},zoom:2,center:[a.current[0].longitude,a.current[0].latitude]});x.current=e,e.addControl(new MapboxLanguage({defaultLanguage:"zh-Hans"})),e.on("load",function(){$(".gha-mapbox").removeClass("loading"),e.loadImage(H,function(t,i){if(t)throw t;e.addImage("dIcon",i)}),a.current.length>10?y(e):C(e)}),s.fitBoundsMap(e,a.current.map(t=>[+t.longitude,+t.latitude]))}function y(e){e.addSource("hotel_markers",{type:"geojson",data:{type:"FeatureCollection",features:s.formatClusterPoint(a.current)},cluster:!0,clusterMaxZoom:14,clusterRadius:50}),e.addLayer({id:"clusters",type:"circle",source:"hotel_markers",filter:["has","point_count"],paint:{"circle-color":"#300B5C","circle-radius":18,"circle-opacity":.8}}),e.addLayer({id:"cluster-count",type:"symbol",source:"hotel_markers",filter:["has","point_count"],paint:{"text-color":"#fff"},layout:{"text-field":"{point_count}","text-size":18}}),e.addLayer({id:"unclustered-point",type:"circle",source:"hotel_markers",filter:["!",["has","point_count"]],paint:{"circle-color":["case",["boolean",["feature-state","isActive"],!1],"#bbb0dc","#300B5C"],"circle-radius":18,"circle-opacity":["case",["boolean",["feature-state","isActive"],!1],1,.8]}}),e.addLayer({id:"unclustered-letter",type:"symbol",source:"hotel_markers",filter:["!",["has","point_count"]],paint:{"icon-opacity":1,"icon-translate":[1.3,-.7]},layout:{"icon-image":"dIcon","icon-allow-overlap":!0}});var t=["clusters","cluster-count","unclustered-point","unclustered-letter"];e.on("mouseover",t,function(){return e.getCanvas().style.cursor="pointer"}),e.on("mouseleave",t,function(){return e.getCanvas().style.cursor=""}),e.on("click","unclustered-point",function(i){const r=e.queryRenderedFeatures(i.point,{layers:["unclustered-point"]});if(!r.length)return;const o=r[0],l=o.properties.id;if(!l)return;s.flyMarkerToCenter(e,o.geometry.coordinates);const n=a.current.find(u=>`${u.id}`==`${l}`);s.getPopup({hotelData:n,slug:m}).setLngLat(o.geometry.coordinates).addTo(e),s.initPopupSwiper(n)}),e.on("click","clusters",function(i){var r,o=e.queryRenderedFeatures(i.point,{layers:["clusters"]}),l=(r=o[0].properties)===null||r===void 0?void 0:r.cluster_id;(e==null?void 0:e.getSource("hotel_markers")).getClusterExpansionZoom(l,function(n,d){n||e.easeTo({center:o[0].geometry.coordinates,zoom:d+1})})}),e.addControl(new mapboxgl.NavigationControl,"top-right")}function C(e){a.current.forEach((t,i)=>{var l;let r;m==="offers"?(r=document.createElement("div"),r.className="gha-mapbox-marker-invalid",$(r).append('<i class="iconfont icon-Union"></i>')):(r=document.createElement("div"),r.className=`gha-mapbox-marker-normal gha-mapbox-marker-${t.id}`,$(r).append(`
          <div class="">
            <p class="font-18">${t.hotel_name}</p>
            <p class="font-18 hidden">CNY ${Math.floor(666)}</p>
            <p class="font-12 mt-1 hidden">最低费率</p>
          </div>
          <i class="iconfont ${t.is_collect?"icon-Heart-filled":"icon-Heart"} fav-icon"></i>
        `));const o=new mapboxgl.Marker(r).setLngLat([t.longitude,t.latitude]).addTo(e);o.getElement().addEventListener("click",function(){s.flyMarkerToCenter(e,[t.longitude,t.latitude]);const n=a.current.find(u=>`${u.id}`==`${t.id}`),d=s.getPopup({hotelData:n,slug:m});o.setPopup(d),o.togglePopup(),s.initPopupSwiper(n)}),(l=$(o.getElement()).find("i.fav-icon")[0])==null||l.addEventListener("click",n=>{n.stopPropagation(),o.togglePopup(),o.getPopup()&&o.getPopup().remove(),!$(o.getElement()).find("i.fav-icon").hasClass("loading")&&($(o.getElement()).find("i.fav-icon").addClass("loading"),f(t.id,(d,u)=>{$(o.getElement()).find("i.fav-icon").removeClass("loading").removeClass("icon-Heart").removeClass("icon-Heart-filled"),$(o.getElement()).find("i.fav-icon").addClass(d?"icon-Heart-filled":"icon-Heart"),a.current=u}),console.log("Marker clicked, map click event will not fire"))})})}return p.jsx("div",{className:"g-main-content",children:p.jsxs("div",{className:"fixed lg:static top-[90px] left-0 right-0 bottom-0 z-[1999] lg:z-[1000]",children:[p.jsx("div",{id:"hotelMap",className:"hotel-map-wrap h-[calc(100vh-90px)] relative z-[998] gha-mapbox loading",children:p.jsx("div",{className:"gha-mapbox-loading-el",children:p.jsx("i",{className:"iconfont icon-loading"})})}),p.jsx(b,{})]})})});export{ce as H};
