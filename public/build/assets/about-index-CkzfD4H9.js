import{a}from"./helper-D414uohx.js";import"./index-Dq7h7Pqt.js";$(function(){new t});class t{constructor(){this.bootstrap()}bootstrap(){this.initBrandSwiper(),this.initShowMoreBrand(),this.initBrandDetailDescTab(),this.initBrandDetailSwiper(),this.initNewsLoadMore()}initNewsLoadMore(){$("body").on("click",".news-load-more a",function(){console.error("load more"),$(".loading-el").removeClass("hidden"),$(".news-load-more").addClass("hidden"),a.getNewsList(window.source||"news_list",{year:window.curYear,page:window.page+1}).subscribe(i=>{if($(".loading-el").addClass("hidden"),i.status_code!==200){$(".news-load-more").removeClass("hidden");return}window.curCount=window.curCount+i.data.list.length,window.curCount<i.data.total&&$(".news-load-more").removeClass("hidden");let n="";i.data.list.forEach(e=>{n+=`
            <a href="${e.url}" class="news-item">
              <div class="info">
                <h1>${e.title}</h1>
                <p>${e.msg}</p>
                <div class="extra">
                  <span>${e.author}</span>
                  <span>${e.created_at}</span>
                </div>
              </div>
              ${e.thumb?`<div class="thumb" style="background-image: url(${e.thumb})"></div>`:""}
            </a>
          `}),$(".news-list").append(n)})})}initBrandDetailSwiper(){}initBrandDetailDescTab(){$(".brand-desc-tab-item a").on("click",function(){if($(this).hasClass("active"))return;const i=$(this).parent().index();$(".brand-desc-tab-item a").removeClass("active"),$(this).addClass("active"),$(".brand-desc-tab-panel").addClass("hidden"),$(".brand-desc-tab-panel").eq(i).removeClass("hidden")})}initShowMoreBrand(){$(".show-more-brand-btn").on("click",function(){$(".brand-list-toggle").addClass("show-more"),$(this).hide()})}initBrandSwiper(){new Swiper(".lg-swiper-el .swiper-container",{navigation:{nextEl:".lg-swiper-el .gha-swiper-button-next",prevEl:".lg-swiper-el .gha-swiper-button-prev"},pagination:{el:".swiper-pagination",clickable:!0}}),new Swiper("#md-swiper")}initAccountSwiper(){new Swiper(".account-section-wrap .swiper-lg",{slidesPerView:"auto",spaceBetween:16,centeredSlides:!0,navigation:{nextEl:".account-section-wrap .gha-swiper-button-next",prevEl:".account-section-wrap .gha-swiper-button-prev"}}),new Swiper(".account-section-wrap .swiper-m",{slidesPerView:"1.2",spaceBetween:16,slidesOffsetBefore:16})}}
