import{j as r}from"./hotel-item-D_zvdyIk.js";import{r as x}from"./PeopleSelectPopover-CDKo4AC-.js";import{u as t}from"./TagFilter-CavzMCRg.js";import{P as h}from"./index-PBsuAvuu.js";import"./mock-OVdH_lkV.js";import"./helper-D414uohx.js";import"./index-Dq7h7Pqt.js";import"./react-ZNplgrTR.js";import"./genStyleUtils-CI3YU7Yv.js";import"./useMergedState-BDSe6zqT.js";import"./isVisible-Bd4H7hpW.js";import"./index-C_TizibV.js";import"./KeyCode-lh1qUinJ.js";import"./zoom-BbRr1fkt.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./color-DKTup0-d.js";import"./useSize-CbUlsqBW.js";import"./index-CJQq4WAU.js";import"./index-CaUFHQr4.js";import"./roundedArrow-DVJD-5zd.js";const n=[{id:"default",name:"相关度"},{id:"brand",name:"品牌"},{id:"desc",name:"价格：由低到高"},{id:"asc",name:"价格：由高到低"}];function q({onCondChange:c}){var a;const o=t(e=>e.order),d=t(e=>e.list),m=t(e=>e.loading),s=t(e=>e.showType),[l,i]=x.useState(!1),p=r.jsx("div",{className:"px-4",children:n.map(e=>r.jsx("div",{onClick:()=>{c({type:"order",value:e.id}),t.setState({order:e.id}),i(!1)},className:`py-2 border-b last:border-b-0 text-center cursor-pointer ${o===e.id?"text-primary":""}`,children:e.name},e.id))});return r.jsx("div",{className:`py-4 ${m?"hidden":""}`,children:r.jsx("div",{className:"g-main-content",children:r.jsxs("div",{className:"filter-state-wrap",children:[r.jsxs("p",{className:"font-12 text-[#696969]",children:["找到",d.length,"个结果"]}),r.jsxs("div",{className:"flex flex-row items-center",children:[r.jsx(h,{placement:"bottom",open:l,onOpenChange:i,content:p,trigger:["click"],children:r.jsxs("div",{className:"shadow-md font-12 shadow-[#070102]/10 rounded-full border border-[#eeeeee] flex flex-row items-center pl-4 pr-2.5 h-8 cursor-pointer",children:[(a=n.find(e=>e.id===o))==null?void 0:a.name,r.jsx("i",{className:"iconfont icon-xiala font-20 !leading-none"})]})}),r.jsxs("div",{className:"map-select-wrap",children:[r.jsxs("div",{onClick:()=>t.setState({showType:"list"}),className:s==="list"?"active":"",children:[r.jsx("i",{className:"iconfont icon-List"}),"列表"]}),r.jsxs("div",{onClick:()=>t.setState({showType:"map"}),className:s==="map"?"active":"",children:[r.jsx("i",{className:"iconfont icon-Map"}),"地图"]})]})]})]})})})}export{q as F,n as o};
