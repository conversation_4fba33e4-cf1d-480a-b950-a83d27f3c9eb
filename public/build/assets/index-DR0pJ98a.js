import{g as l,m as d,a as $}from"./genStyleUtils-CI3YU7Yv.js";import"./PeopleSelectPopover-CDKo4AC-.js";const p=t=>{const{componentCls:e}=t;return{[e]:{display:"flex",flexFlow:"row wrap",minWidth:0,"&::before, &::after":{display:"flex"},"&-no-wrap":{flexWrap:"nowrap"},"&-start":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"flex-end"},"&-space-between":{justifyContent:"space-between"},"&-space-around":{justifyContent:"space-around"},"&-space-evenly":{justifyContent:"space-evenly"},"&-top":{alignItems:"flex-start"},"&-middle":{alignItems:"center"},"&-bottom":{alignItems:"flex-end"}}}},c=t=>{const{componentCls:e}=t;return{[e]:{position:"relative",maxWidth:"100%",minHeight:1}}},m=(t,e)=>{const{prefixCls:s,componentCls:o,gridColumns:i}=t,r={};for(let n=i;n>=0;n--)n===0?(r[`${o}${e}-${n}`]={display:"none"},r[`${o}-push-${n}`]={insetInlineStart:"auto"},r[`${o}-pull-${n}`]={insetInlineEnd:"auto"},r[`${o}${e}-push-${n}`]={insetInlineStart:"auto"},r[`${o}${e}-pull-${n}`]={insetInlineEnd:"auto"},r[`${o}${e}-offset-${n}`]={marginInlineStart:0},r[`${o}${e}-order-${n}`]={order:0}):(r[`${o}${e}-${n}`]=[{"--ant-display":"block",display:"block"},{display:"var(--ant-display)",flex:`0 0 ${n/i*100}%`,maxWidth:`${n/i*100}%`}],r[`${o}${e}-push-${n}`]={insetInlineStart:`${n/i*100}%`},r[`${o}${e}-pull-${n}`]={insetInlineEnd:`${n/i*100}%`},r[`${o}${e}-offset-${n}`]={marginInlineStart:`${n/i*100}%`},r[`${o}${e}-order-${n}`]={order:n});return r[`${o}${e}-flex`]={flex:`var(--${s}${e}-flex)`},r},a=(t,e)=>m(t,e),u=(t,e,s)=>({[`@media (min-width: ${$(e)})`]:Object.assign({},a(t,s))}),f=()=>({}),g=()=>({}),M=l("Grid",p,f),y=t=>({xs:t.screenXSMin,sm:t.screenSMMin,md:t.screenMDMin,lg:t.screenLGMin,xl:t.screenXLMin,xxl:t.screenXXLMin}),w=l("Grid",t=>{const e=d(t,{gridColumns:24}),s=y(e);return delete s.xs,[c(e),a(e,""),a(e,"-xs"),Object.keys(s).map(o=>u(e,s[o],`-${o}`)).reduce((o,i)=>Object.assign(Object.assign({},o),i),{})]},g);export{M as a,y as g,w as u};
