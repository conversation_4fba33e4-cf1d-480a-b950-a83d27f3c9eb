import{j as i}from"./hotel-item-D_zvdyIk.js";import{a as m,b as e}from"./GhaSearchBar-CE-42mVw.js";import{u as o}from"./TagFilter-CavzMCRg.js";import"./PeopleSelectPopover-CDKo4AC-.js";import"./index-CosgXXok.js";import"./PurePanel-zvE2p4pp.js";import"./useMergedState-BDSe6zqT.js";import"./genStyleUtils-CI3YU7Yv.js";import"./isVisible-Bd4H7hpW.js";import"./index-C_TizibV.js";import"./button-C2fNxKeA.js";import"./index-DKvg8qd3.js";import"./useSize-CbUlsqBW.js";import"./color-DKTup0-d.js";import"./pickAttrs-D1C8emUZ.js";import"./index-CaUFHQr4.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./Overflow-YL9RZFGj.js";import"./index-wg7qNA5H.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./useIcons-BIWMCRV_.js";import"./CloseOutlined-CGWqYTdG.js";import"./roundedArrow-DVJD-5zd.js";import"./index-BBp8sG_H.js";import"./KeyCode-lh1qUinJ.js";import"./zoom-BbRr1fkt.js";import"./react-ZNplgrTR.js";import"./index-PBsuAvuu.js";import"./index-CJQq4WAU.js";import"./zh-cn-DO3l3KOs.js";import"./helper-D414uohx.js";import"./index-Dq7h7Pqt.js";import"./index-DNIuBGmG.js";import"./Input-DCb0FIZx.js";import"./mock-OVdH_lkV.js";function P({onCondChange:t}){const s=o(r=>r.slug);return i.jsx("div",{className:"top-filter-bar",children:i.jsx("div",{className:"g-main-content",children:i.jsx("div",{className:"home-filter-wrap",children:i.jsxs("div",{className:"inner",children:[i.jsxs("div",{className:"tab-wrap !hidden",children:[i.jsxs("div",{className:"tab-list active-1",children:[i.jsx("div",{className:"item",children:i.jsx("p",{children:"精选酒店"})}),i.jsx("div",{className:"item",children:i.jsx("p",{children:"本地生活"})}),i.jsx("div",{className:"item",children:i.jsx("p",{children:"体验"})})]}),i.jsx("div",{className:"tip",children:i.jsx("p",{children:"GHA代表着45个品牌，850多家酒店，遍布 100多个国家，服务会员3000万"})})]}),i.jsxs("div",{className:"hotel-list-search-bar",children:[s==="hotels"&&i.jsx(m,{onSubmit:r=>t({type:"search",value:r}),sticky:!0}),s==="offers"&&i.jsx(e,{onSubmit:r=>t({type:"search",value:r}),sticky:!0})]})]})})})})}export{P as T};
