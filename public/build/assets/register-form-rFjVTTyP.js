import{j as e}from"./hotel-item-D_zvdyIk.js";import{c as y}from"./client-CHYie6Ha.js";import{G as I,u as S,a as q}from"./GhaConfigProvider-BB5IW5PM.js";import{r as t}from"./PeopleSelectPopover-CDKo4AC-.js";import{$ as p}from"./_constants-CI6xcXYb.js";import{$ as R,a as F}from"./helper-D414uohx.js";import{F as r}from"./index-CC5HyPSW.js";import{I as a}from"./index-DNIuBGmG.js";import{S as x}from"./index-BBp8sG_H.js";import{R as m}from"./index-Bkcwbepo.js";import{C as P}from"./index-DoO4tVvP.js";import{B as A}from"./button-C2fNxKeA.js";import"./index-C_TizibV.js";import"./isVisible-Bd4H7hpW.js";import"./genStyleUtils-CI3YU7Yv.js";import"./CloseOutlined-CGWqYTdG.js";import"./KeyCode-lh1qUinJ.js";import"./pickAttrs-D1C8emUZ.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./index-DxcGRMc6.js";import"./index-DKvg8qd3.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./color-DKTup0-d.js";import"./useSize-CbUlsqBW.js";import"./zoom-BbRr1fkt.js";import"./extendsObject-78o_rR5W.js";import"./index-DR0pJ98a.js";import"./index-Dq7h7Pqt.js";import"./collapse-BbEVqHco.js";import"./useForm-DF1XNZWJ.js";import"./row-BiveCJJi.js";import"./useBreakpoint-D48_IWaH.js";import"./index-CJQq4WAU.js";import"./index-CaUFHQr4.js";import"./useMergedState-BDSe6zqT.js";import"./roundedArrow-DVJD-5zd.js";import"./index-wg7qNA5H.js";import"./Input-DCb0FIZx.js";import"./Overflow-YL9RZFGj.js";import"./PurePanel-zvE2p4pp.js";import"./useIcons-BIWMCRV_.js";function E(){var d;const[f,h]=t.useState(1),[j]=r.useForm(),[g,c]=t.useState(!1),v=S(),w=q(),[n,_]=t.useState(null),l=t.useRef(null),o=t.useRef(5);function b(s){c(!0),F.authRegister(s).subscribe(i=>{if(c(!1),i.status_code!==200){v.error(i.message);return}_(i.data),h(2),N()})}t.useEffect(()=>()=>{l.current&&clearInterval(l.current)},[]);function N(){l.current=setInterval(()=>{o.current-=1,w(),o.current===1&&(clearInterval(l.current),window.location.href=document.referrer||"/")},1e3)}return f===1?e.jsx(e.Fragment,{children:e.jsxs(r,{layout:"vertical",form:j,onFinish:b,requiredMark:!1,validateTrigger:"onBlur",initialValues:R.isLaravelLocal()?{first_name:"wu",last_name:"wchy",email:"<EMAIL>",password:"12345Abc$",confirm_password:"12345Abc$",city:"西安",country:49,is_message:!0}:{is_message:!0},children:[e.jsxs("div",{className:"flex flex-col lg:flex-row",children:[e.jsx(r.Item,{label:"姓*",name:"first_name",className:"flex-1 lg:mr-5",rules:[{required:!0,message:"请输入您的姓"}],children:e.jsx(a,{variant:"underlined",placeholder:"请输入您的姓"})}),e.jsx(r.Item,{label:"名*",name:"last_name",className:"flex-1",rules:[{required:!0,message:"请输入您的名"}],children:e.jsx(a,{variant:"underlined",placeholder:"请输入您的名"})})]}),e.jsx(r.Item,{label:e.jsxs(e.Fragment,{children:["邮箱",e.jsx("span",{className:"text-[#919191]/60",children:"（接收订单确认函，请准确填写）"}),"*"]}),name:"email",rules:[{required:!0,message:"请输入您的邮箱"},{pattern:p.emailPattern,message:"邮箱格式不正确"}],children:e.jsx(a,{variant:"underlined",placeholder:"请输入您的邮箱"})}),e.jsxs("div",{className:"flex flex-col lg:flex-row",children:[e.jsx(r.Item,{label:"密码*",name:"password",className:"flex-1 lg:mr-5",rules:[{required:!0,message:"请输入密码"},{pattern:p.passwordPattern,message:"密码至少8位，包含字母、数字和符号，且首尾不能有空格"}],children:e.jsx(a.Password,{variant:"underlined",placeholder:"请输入密码"})}),e.jsx(r.Item,{label:"确认密码*",name:"confirm_password",className:"flex-1",dependencies:["password"],rules:[{required:!0,message:"请输入确认密码"},({getFieldValue:s})=>({validator(i,u){return!u||s("password")===u?Promise.resolve():Promise.reject(new Error("密码与确认密码不一致!"))}})],children:e.jsx(a.Password,{variant:"underlined",placeholder:"请输入确认密码"})})]}),e.jsxs("div",{className:"flex flex-col lg:flex-row",children:[e.jsx(r.Item,{label:"城市*",name:"city",className:"flex-1 lg:mr-5",rules:[{required:!0,message:"请输入城市"}],children:e.jsx(a,{variant:"underlined",placeholder:"请输入城市"})}),e.jsx(r.Item,{label:"国家/地区*",name:"country",className:"flex-1",rules:[{required:!0,message:"请选择国家/地区"}],children:e.jsx(x,{variant:"underlined",placeholder:"请选择国家/地区",children:window.__ServerVars__.countryList.map(s=>e.jsx(x.Option,{value:s.id,children:s.country_name},s.id))})})]}),e.jsxs("div",{className:"tip-wrap",children:[e.jsx("h5",{children:"不要错过 GHA DISCOVERY 通过电子邮件发送的限时优惠、闪购个性化更新以及特别酒店和合作伙伴促销活动。 按照下面链接的隐私政策中的说明随时取消订阅。"}),e.jsx(r.Item,{label:null,name:"is_message",className:"mt-2",rules:[{required:!1}],children:e.jsxs(m.Group,{size:"small",style:{display:"flex",flexDirection:"column",gap:7},children:[e.jsx(m,{value:!0,children:"是的，我希望独家获取新闻和优惠信息。"}),e.jsx(m,{value:!1,children:"不，我不需要最新消息和优惠。"})]})}),e.jsx(r.Item,{valuePropName:"checked",label:null,name:"is_auth",className:"-mt-4",rules:[{required:!0,message:"请同意隐私政策"}],children:e.jsx(P,{children:"同意将个人数据根据GHA隐私政策中国附录的条款转移到中国境外。"})}),e.jsx(r.Item,{hidden:!0,name:"language",initialValue:"ZH",children:e.jsx(a,{})}),e.jsxs("p",{className:"-mt-3",children:["点击“立即注册”，即表示我接受 GHA DISCOVERY 计划的 ",e.jsx("a",{href:"",children:"条款和条件"})," 以及 ",e.jsx("a",{href:"",children:"隐私政策"}),"。"]}),e.jsxs("div",{className:"submit-wrap",children:[e.jsx(A,{className:"gha-primary-btn",loading:g,type:"primary",shape:"round",block:!0,htmlType:"submit",children:"注册"}),e.jsx("p",{children:e.jsx("a",{href:window.__ServerVars__.activeUri,className:"underline",children:"激活您的在线帐户"})}),e.jsxs("p",{children:["已有账号，",e.jsx("a",{href:window.__ServerVars__.loginUri,children:"立即登录"})]})]})]})]})}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex flex-col items-center justify-center text-center h-full",children:[e.jsx("i",{className:"iconfont icon-a-14Benefits_brand_benefits text-primary text-4xl"}),e.jsx("h2",{className:"mt-1 font-18 font-bold",children:"注册成功"}),e.jsxs("h5",{className:"mt-4 pb-20 font-12",children:["感谢您选择 GHA 会员计划",e.jsx("br",{}),"请登录您的邮箱：",(d=n==null?void 0:n.user)==null?void 0:d.email," 进行账号激活"]}),e.jsxs("p",{className:"font-12 text-[#919191]",children:[e.jsxs("span",{className:"font-bold text-black",children:["0",o.current]})," 秒后返回上一页"]})]})})}y.createRoot(document.querySelector(".form-wrap")).render(e.jsx(I,{children:e.jsx(E,{})}));
