import{j as t}from"./hotel-item-D_zvdyIk.js";import{c as m}from"./client-CHYie6Ha.js";import{r as s}from"./PeopleSelectPopover-CDKo4AC-.js";import{O as a}from"./order-item-BcHjEzxx.js";import{m as r}from"./mock-OVdH_lkV.js";import"./index-C_TizibV.js";function i(){const[e,d]=s.useState([{id:Math.random().toString(),title:r.Random.ctitle(5,10),price:r.Random.float(100,1e3,2,2)},{id:Math.random().toString(),title:r.Random.ctitle(5,10),price:r.Random.float(100,1e3,2,2)}]);return t.jsx("div",{className:"p-0 lg:p-10",children:e.map(o=>t.jsx(a,{order:o},o.id))})}m.createRoot(document.querySelector("#orders")).render(t.jsx(i,{}));
