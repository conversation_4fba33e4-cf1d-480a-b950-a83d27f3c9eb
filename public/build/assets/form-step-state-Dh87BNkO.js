import{j as t}from"./hotel-item-D_zvdyIk.js";import{r as s}from"./PeopleSelectPopover-CDKo4AC-.js";import{a as c}from"./GhaConfigProvider-BB5IW5PM.js";import"./isVisible-Bd4H7hpW.js";import"./genStyleUtils-CI3YU7Yv.js";import"./index-C_TizibV.js";import"./CloseOutlined-CGWqYTdG.js";import"./button-C2fNxKeA.js";import"./index-DKvg8qd3.js";import"./useSize-CbUlsqBW.js";import"./color-DKTup0-d.js";import"./KeyCode-lh1qUinJ.js";import"./pickAttrs-D1C8emUZ.js";import"./useCSSVarCls-DWPRWpfJ.js";import"./ExclamationCircleFilled-DGwIvyP4.js";import"./index-DxcGRMc6.js";import"./ContextIsolator-CVrwwDX4.js";import"./context-Gzj2nObQ.js";import"./zoom-BbRr1fkt.js";import"./extendsObject-78o_rR5W.js";import"./index-DR0pJ98a.js";function U({state:n="success"}){const r=s.useRef(null),e=s.useRef(5),i=c();s.useEffect(()=>(n==="success"&&o(),()=>{r.current&&clearInterval(r.current)}),[]);function o(){r.current=setInterval(()=>{e.current-=1,i(),e.current===1&&(clearInterval(r.current),window.location.href=document.referrer||"/")},1e3)}return t.jsx("div",{className:"flex flex-col items-center justify-center",children:n==="success"?t.jsxs(t.Fragment,{children:[t.jsx("i",{className:"iconfont icon-a-14Benefits_brand_benefits text-primary text-4xl"}),t.jsx("h2",{className:"font-bold text-lg",children:"激活成功"}),t.jsx("h5",{className:"text-xs mt-2",children:"感谢您选择 GHA 会员计划"}),t.jsxs("p",{className:"text-xs text-[#737373] mt-16",children:[t.jsx("span",{className:"text-primary font-bold mr-1",children:e.current}),"秒后进入会员首页"]})]}):t.jsxs(t.Fragment,{children:[t.jsx("i",{className:"iconfont icon-Information text-primary text-4xl"}),t.jsx("h2",{className:"font-bold text-lg",children:"已有账户"}),t.jsx("p",{className:"mt-5 text-xs text-[#737373]",children:"已有一个在线账户关联此信息，请返回并使用您的电子邮箱登录。"}),t.jsx("a",{href:"",className:"gha-primary-btn w-full mt-5",children:"返回首页"})]})})}export{U as F};
