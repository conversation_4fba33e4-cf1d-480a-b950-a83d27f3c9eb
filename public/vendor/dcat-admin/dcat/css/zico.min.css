.zi_lg{font-size: 1.8rem; line-height: 0.8rem; vertical-align: -.08rem}.zi_sm{font-size: .8rem}.zi_1x{font-size: 1rem}.zi_2x{font-size: 2rem}.zi_3x{font-size: 3rem}.zi_4x{font-size: 4rem}.zi_5x{font-size: 5rem}.zi_6x{font-size: 6rem}.zi_7x{font-size: 7rem}.zi_8x{font-size: 8rem}.zi_9x{font-size: 9rem}.zi_10x{font-size: 10rem}.zi_11x{font-size: 11rem}.zi_12x{font-size: 12rem}.zi_w1{width: 1rem}.zi_w2{width: 2rem}.zi_w3{width: 3rem}.zi_w4{width: 4rem}.zi_w5{width: 5rem}.zi_w6{width: 6rem}.zi_w7{width: 7rem}.zi_w8{width: 8rem}.zi_border{border: solid 0.05rem #666; border-radius: .1rem; padding: .2rem .25rem .15rem}.zi_spin{-webkit-animation: zi_rotate 2s infinite linear; animation: zi_rotate 2s infinite linear}.zi_pulse{-webkit-animation: zi_rotate 1s infinite steps(8); animation: zi_rotate 1s infinite steps(8)}.zi_bounce{-webkit-animation: bounce 1.4s linear infinite; animation: bounce 1.4s linear infinite}@-webkit-keyframes bounce{25%{-webkit-transform: translateY(10px)} 50%, 100%{-webkit-transform: translateY(0)} 75%{-webkit-transform: translateY(-10px)}}@keyframes bounce{25%{-webkit-transform: translateY(10px); transform: translateY(10px)} 50%, 100%{-webkit-transform: translateY(0); transform: translateY(0)} 75%{-webkit-transform: translateY(-10px); transform: translateY(-10px)}}.zi_zoom{opacity: 0; -webkit-animation: zoom 3s ease-out; animation: zoom 3s ease-out; -webkit-animation-iteration-count: infinite; animation-iteration-count: infinite}@keyframes zoom{0%{-webkit-transform: scale(0); transform: scale(0); opacity: 0.0} 25%{-webkit-transform: scale(0); transform: scale(0); opacity: 0.1} 50%{-webkit-transform: scale(0.1); transform: scale(0.1); opacity: 0.3} 75%{-webkit-transform: scale(0.5); transform: scale(0.5); opacity: 0.5} 100%{-webkit-transform: scale(1); transform: scale(1); opacity: 1.0}}@-webkit-keyframes zoom{0%{-webkit-transform: scale(0); opacity: 0.0} 25%{-webkit-transform: scale(0); opacity: 0.1} 50%{-webkit-transform: scale(0.1); opacity: 0.3} 75%{-webkit-transform: scale(0.5); opacity: 0.5} 100%{-webkit-transform: scale(1); opacity: 1.0}}.zi_danger{animation: danger 8ms infinite alternate; -webkit-animation: danger 8ms infinite alternate}@keyframes danger{0%{opacity: .2} 100%{opacity: 1}}@-webkit-keyframes danger{0%{opacity: .2} 100%{opacity: 1}}@-webkit-keyframes zi_rotate{0%{-webkit-transform: rotate(0deg); transform: rotate(0deg)} 100%{-webkit-transform: rotate(360deg); transform: rotate(360deg)}}@keyframes zi_rotate{0%{-webkit-transform: rotate(0deg); transform: rotate(0deg)} 100%{-webkit-transform: rotate(360deg); transform: rotate(360deg)}}.zi_load{animation: zi_load 1400ms steps(1, end) infinite 0s; -webkit-animation: zi_load 1400ms steps(1, end) infinite 0s}@keyframes zi_load{0%{opacity: 0.1} 20%{opacity: 0.2} 30%{opacity: 0.3} 40%{opacity: 0.4} 50%{opacity: 0.5} 60%{opacity: 0.6} 70%{opacity: 0.7} 80%{opacity: 0.8} 90%{opacity: 0.9} 100%{opacity: 1}}@-webkit-keyframes zi_load{0%{opacity: 0.1} 20%{opacity: 0.2} 30%{opacity: 0.3} 40%{opacity: 0.4} 50%{opacity: 0.5} 60%{opacity: 0.6} 70%{opacity: 0.7} 80%{opacity: 0.8} 90%{opacity: 0.9} 100%{opacity: 1}}.zi_pull{display: table-caption !important; overflow: hidden; -webkit-animation: zi_pull 1s ease 0.6s; animation: zi_pull 1s ease 0.6s}@keyframes zi_pull{0%{width: 0} 100%{width: 100%}}@-webkit-keyframes zi_pull{0%{width: 0} 100%{width: 100%}}.zi_rotate90{-ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)"; -webkit-transform: rotate(90deg); transform: rotate(90deg)}.zi_rotate180{-ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)"; -webkit-transform: rotate(180deg); transform: rotate(180deg)}.zi_rotate270{-ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)"; -webkit-transform: rotate(270deg); transform: rotate(270deg)}.zi_flipLevel{-ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)"; -webkit-transform: scale(-1, 1); transform: scale(-1, 1)}.zi_flipVertical{-ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)"; -webkit-transform: scale(1, -1); transform: scale(1, -1)}.zi_flipLevel.zi_flipVertical{-ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)"; -webkit-transform: scale(-1, -1); transform: scale(-1, -1)}:root .zi_rotate90, :root .zi_rotate180, :root .zi_rotate270, :root .zi_flipLevell, :root .zi_flipVertical{-webkit-filter: none; filter: none}.zi_group{display: inline-block; position: relative; width: 2rem; height: 2rem; line-height: 2rem; vertical-align: middle}.zi_group1x, .zi_group2x{left: 0; position: absolute; text-align: center; width: 100%}.zi_group1x{line-height: inherit; font-size: 1rem}.zi_group2x{font-size: 2rem}.zi_inverse{color: #fff}