<?php

namespace App\Services;

use App\DTOs\Sabre\ReservationRequestDTO;
use App\DTOs\Sabre\BookingInfoDTO;
use App\DTOs\Sabre\OriginalSourceDTO;
use App\DTOs\Sabre\ChannelDTO;
use App\DTOs\Sabre\ChainDTO;
use App\DTOs\Sabre\HotelDTO;
use App\DTOs\Sabre\ChannelsDTO;
use App\DTOs\Sabre\LanguageDTO;
use App\DTOs\Sabre\MarketSourceDTO;
use App\DTOs\Sabre\NotificationDTO;
use App\DTOs\Sabre\RoomStayDTO;
use App\DTOs\Sabre\GuestDTO;
use App\DTOs\Sabre\LoyaltyMembershipDTO;
use App\DTOs\Sabre\PromotionDTO;
use App\Exceptions\SabreApiException;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SabreReservationService
{
    protected SabreService $sabreService;
    protected SabreReservationDataTransformer $dataTransformer;

    public function __construct(
        SabreService $sabreService,
        SabreReservationDataTransformer $dataTransformer
    ) {
        $this->sabreService = $sabreService;
        $this->dataTransformer = $dataTransformer;
    }

    /**
     * 创建单房间预订（非会员）
     */
    public function createSingleRoomBookingNonMember(array $params): array
    {
        $request = $this->buildReservationRequest($params, false, false);
        try {
            $result = $this->sabreService->createReservation($request);
            return $this->parseReservationResponse($result);
        } catch (SabreApiException $e) {
            Log::error('Create Single Room Booking Non-Member Error', [
                'message' => $e->getMessage(),
                'params' => $params,
            ]);
            throw $e;
        }
    }

    /**
     * 创建单房间预订（非会员，含促销）
     */
    public function createSingleRoomBookingNonMemberWithPromo(array $params): array
    {
        $request = $this->buildReservationRequest($params, false, true);

        try {
            $result = $this->sabreService->createReservation($request);
            return $this->parseReservationResponse($result);
        } catch (SabreApiException $e) {
            Log::error('Create Single Room Booking Non-Member With Promo Error', [
                'message' => $e->getMessage(),
                'params' => $params,
            ]);
            throw $e;
        }
    }

    /**
     * 创建单房间预订（会员）
     */
    public function createSingleRoomBookingWithMembership(array $params): array
    {
        $request = $this->buildReservationRequest($params, true, false);

        try {
            $result = $this->sabreService->createReservation($request);
            return $this->parseReservationResponse($result);
        } catch (SabreApiException $e) {
            Log::error('Create Single Room Booking With Membership Error', [
                'message' => $e->getMessage(),
                'params' => $params,
            ]);
            throw $e;
        }
    }

    /**
     * 创建单房间预订（会员，含促销）
     */
    public function createSingleRoomBookingWithMembershipAndPromo(array $params): array
    {
        $request = $this->buildReservationRequest($params, true, true);

        try {
            $result = $this->sabreService->createReservation($request);
            return $this->parseReservationResponse($result);
        } catch (SabreApiException $e) {
            Log::error('Create Single Room Booking With Membership And Promo Error', [
                'message' => $e->getMessage(),
                'params' => $params,
            ]);
            throw $e;
        }
    }

    /**
     * 创建多房间预订
     */
    public function createMultiRoomBooking(array $params): array
    {
        // 多房间预订需要先创建第一个房间，然后使用返回的行程号创建后续房间
        $firstRoomParams = $params;
        $firstRoomParams['numRooms'] = 1;

        // 创建第一个房间
        $firstRoomResult = $this->createSingleRoomBookingWithMembership($firstRoomParams);

        if (!$firstRoomResult['success'] || empty($firstRoomResult['itinerary_number'])) {
            throw new SabreApiException('创建第一个房间失败');
        }

        $itineraryNumber = $firstRoomResult['itinerary_number'];
        $results = [$firstRoomResult];

        // 创建剩余房间
        $remainingRooms = ($params['numRooms'] ?? 2) - 1;
        for ($i = 0; $i < $remainingRooms; $i++) {
            $additionalRoomParams = $params;
            $additionalRoomParams['itineraryNumber'] = $itineraryNumber;
            $additionalRoomParams['numRooms'] = 1;

            try {
                $request = $this->buildReservationRequest($additionalRoomParams, true, isset($params['accessCode']));
                $result = $this->sabreService->createReservation($request);
                $results[] = $this->parseReservationResponse($result);
            } catch (SabreApiException $e) {
                Log::error('Create Additional Room Error', [
                    'message' => $e->getMessage(),
                    'room_index' => $i + 2,
                    'itinerary_number' => $itineraryNumber,
                ]);
                // 继续创建其他房间，但记录错误
                $results[] = [
                    'success' => false,
                    'error' => $e->getMessage(),
                    'room_index' => $i + 2,
                ];
            }
        }

        return [
            'success' => true,
            'itinerary_number' => $itineraryNumber,
            'rooms' => $results,
            'total_rooms' => count($results),
        ];
    }

    /**
     * 构建预订请求DTO
     */
    protected function buildReservationRequest(array $params, bool $withMembership = false, bool $withPromo = false): ReservationRequestDTO
    {
        $now = Carbon::now('UTC');

        // 构建预订信息
        $bookingInfo = new BookingInfoDTO(
            bookingDate: $now->format('Y-m-d\\TH:i:s'),
            entryChannelBookingDate: $now->format('Y-m-d\\TH:i:s'),
            entryChannelCode: config('sabre.defaults.entry_channel_code'),
            originalSource: new OriginalSourceDTO(
                primaryChannel: new ChannelDTO(
                    code: config('sabre.defaults.primary_channel'),
                    description: 'Channel Connect'
                ),
                secondaryChannel: new ChannelDTO(
                    code: config('sabre.defaults.secondary_channel'),
                    description: 'DISCOVERY Loyalty'
                ),
                subSourceCode: config('sabre.defaults.sub_source_code', 'GHA')
            )
        );

        // 构建酒店和链信息
        $chain = new ChainDTO($params['chainId'] ?? config('sabre.defaults.chain_id'));
        $hotel = new HotelDTO($params['hotelId'], $params['hotelCode'] ?? null);

        // 构建渠道信息
        $channels = new ChannelsDTO(
            primaryChannel: new ChannelDTO(config('sabre.defaults.primary_channel')),
            secondaryChannel: new ChannelDTO(config('sabre.defaults.secondary_channel')),
            subSourceCode: config('sabre.defaults.sub_source_code')
        );
        $startDate = Carbon::parse($params['roomStay']['StartDate'], 'UTC')->format('Y-m-d\\TH:i:s');
        $endDate = Carbon::parse($params['roomStay']['EndDate'], 'UTC')->format('Y-m-d\\TH:i:s');
        // 构建客人信息（标准化 Guests 的起止时间为 UTC ISO）
        $guests = [];
        foreach ($params['guests'] as $guestData) {
            $guestData['StartDate'] = $startDate;
            $guestData['EndDate'] = $endDate;
            $guests[] = GuestDTO::fromArray($guestData);
        }

        // 构建语言信息
        $language = new LanguageDTO(
            code: $params['language']['code'] ?? config('sabre.defaults.language', 'zh-CN'),
            name: $params['language']['name'] ?? '中文'
        );

        // 构建市场来源
        $marketSource = new MarketSourceDTO(config('sabre.defaults.market_source_code'));

        // 构建通知设置
        $notification = new NotificationDTO(
            sendBookerEmail: $params['sendBookerEmail'] ?? false,
            sendGuestEmail: $params['sendGuestEmail'] ?? false
        );

        // 构建住宿信息（统一为 UTC ISO）
        $roomStayInput = $params['roomStay'];
        $roomStayInput['StartDate'] = $startDate;
        $roomStayInput['EndDate'] = $endDate;

        //遍历给$product添加时间
        foreach ($roomStayInput['Products'] as &$product) {
            $product['StartDate'] = $startDate;
            $product['EndDate'] = $endDate;
        }
        $roomStay = RoomStayDTO::fromArray($roomStayInput);

        // 构建忠诚度会员信息
        $loyaltyMemberships = null;
        if ($withMembership && isset($params['loyaltyMemberships'])) {
            $loyaltyMemberships = [];
            foreach ($params['loyaltyMemberships'] as $membershipData) {
                $membershipData['StartDate'] = $startDate;
                $membershipData['EndDate'] = $endDate;
                $loyaltyMemberships[] = LoyaltyMembershipDTO::fromArray($membershipData);
            }
        }

        // 构建促销信息
        $promotion = null;
        if ($withPromo && isset($params['promotion'])) {
            $promotion = PromotionDTO::fromArray($params['promotion']);
        }
        return new ReservationRequestDTO(
            bookingInfo: $bookingInfo,
            chain: $chain,
            hotel: $hotel,
            channels: $channels,
            guests: $guests,
            language: $language,
            marketSource: $marketSource,
            notification: $notification,
            roomStay: $roomStay,
            status: $params['status'] ?? 'Confirmed',
            loyaltyMemberships: $loyaltyMemberships,
            promotion: $promotion,
            itineraryNumber: $params['itineraryNumber'] ?? null
        );
    }

    /**
     * 解析预订响应
     */
    protected function parseReservationResponse(array $response): array
    {
        $parsed = [
            'success' => false,
            'reservation_id' => null,
            'confirmation_number' => null,
            'itinerary_number' => null,
            'status' => null,
            'errors' => [],
        ];

        try {
            if (isset($response['reservations']) && is_array($response['reservations']) && count($response['reservations']) > 0) {
                $reservation = $response['reservations'][0];

                $parsed['success'] = true;
                $parsed['reservation_id'] = $reservation['Id'] ?? null;
                $parsed['confirmation_number'] = $reservation['CrsConfirmationNumber'] ?? null;
                $parsed['itinerary_number'] = $reservation['ItineraryNumber'] ?? null;
                $parsed['status'] = $reservation['Status'] ?? null;
                $parsed['raw_response'] = $response;
            } else {
                $parsed['errors'][] = '未找到预订信息';
                if (isset($response['errors'])) {
                    $parsed['errors'] = array_merge($parsed['errors'], $response['errors']);
                }
            }
        } catch (\Exception $e) {
            Log::error('Parse Reservation Response Error', [
                'message' => $e->getMessage(),
                'response' => $response,
            ]);

            $parsed['errors'][] = '解析预订响应失败: ' . $e->getMessage();
        }

        return $parsed;
    }

    /**
     * 查询预订
     */
    public function getReservation(array $params): array
    {
        try {
            if (isset($params['itineraryNumber'])) {
                $result = $this->sabreService->getReservationByItinerary(
                    $params['itineraryNumber'],
                    $params['channel'] ?? 'DSCVRYLYLTY'
                );
            } elseif (isset($params['confirmationNumber'])) {
                $result = $this->sabreService->getReservationByConfirmation(
                    $params['confirmationNumber'],
                    $params['chainId'] ?? config('sabre.defaults.chain_id'),
                    $params['hotelId'],
                    $params['channel'] ?? 'DSCVRYLYLTY'
                );
            } else {
                throw new SabreApiException('必须提供行程号或确认号');
            }

            // 使用新的数据转换器处理响应数据
            $response = [
                'confirmationNumber' => $params['confirmationNumber'] ?? null,
                'response' => $result
            ];

            return $this->dataTransformer->transformReservationResponse($response);
        } catch (SabreApiException $e) {
            Log::error('Get Reservation Error', [
                'message' => $e->getMessage(),
                'params' => $params,
            ]);
            throw $e;
        }
    }

    /**
     * 修改预订
     */
    public function modifyReservation(array $params): array
    {
        try {
            $result = $this->sabreService->modifyReservation($params);
            return $this->parseReservationResponse($result);
        } catch (SabreApiException $e) {
            Log::error('Modify Reservation Error', [
                'message' => $e->getMessage(),
                'params' => $params,
            ]);
            throw $e;
        }
    }

    /**
     * 取消预订
     */
    public function cancelReservation(string $confirmationNumber, int $hotelId, ?string $hotelCode = null): array
    {
        try {
            $result = $this->sabreService->cancelReservation($confirmationNumber, $hotelId, $hotelCode);

            return [
                'success' => true,
                'confirmation_number' => $confirmationNumber,
                'status' => 'Cancelled',
                'raw_response' => $result,
            ];
        } catch (SabreApiException $e) {
            Log::error('Cancel Reservation Error', [
                'message' => $e->getMessage(),
                'confirmation_number' => $confirmationNumber,
                'hotel_id' => $hotelId,
            ]);

            return [
                'success' => false,
                'confirmation_number' => $confirmationNumber,
                'error' => $e->getMessage(),
            ];
        }
    }
}
