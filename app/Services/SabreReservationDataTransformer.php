<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

/**
 * Sabre预订数据转换器
 * 用于将Sabre API返回的复杂预订数据转换为标准化格式
 */
class SabreReservationDataTransformer
{
    protected CurrencyConversionService $currencyService;

    public function __construct(CurrencyConversionService $currencyService = null)
    {
        $this->currencyService = $currencyService ?? new CurrencyConversionService();
    }
    /**
     * 转换预订查询响应数据
     */
    public function transformReservationResponse(array $response): array
    {
        try {
            if (!isset($response['response']['Reservations']) || empty($response['response']['Reservations'])) {
                return [
                    'success' => false,
                    'message' => '未找到预订信息',
                    'data' => null
                ];
            }

            $reservations = [];
            foreach ($response['response']['Reservations'] as $reservation) {
                $reservations[] = $this->transformSingleReservation($reservation);
            }

            return [
                'success' => true,
                'message' => '查询成功',
                'data' => [
                    'confirmationNumber' => $response['confirmationNumber'] ?? null,
                    'reservations' => $reservations,
                    'pagination' => $this->transformPagination($response['response']['Pagination'] ?? [])
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Transform Reservation Response Error', [
                'message' => $e->getMessage(),
                'response' => $response,
            ]);

            return [
                'success' => false,
                'message' => '数据转换失败: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 转换单个预订数据
     */
    protected function transformSingleReservation(array $reservation): array
    {
        // 获取货币信息
        $currencyCode = $reservation['Currency']['Code'] ?? 'USD';

        return [
            // 基本预订信息
            'reservation_id' => $reservation['Id'] ?? null,
            'confirmation_number' => $reservation['CRS_confirmationNumber'] ?? $reservation['CRSConfirmationNumber'] ?? null,
            'itinerary_number' => $reservation['ItineraryNumber'] ?? null,
            'external_reference_number' => $reservation['ExternalReferenceNumber'] ?? null,
            'channel_confirmation_number' => $reservation['ChannelConfirmationNumber'] ?? null,
            'status' => $reservation['Status'] ?? null,
            'on_property_status' => $reservation['OnPropertyStatus'] ?? null,
            'purpose_of_stay' => $reservation['PurposeOfStay'] ?? null,
            'modification_permitted' => $reservation['ModificationPermitted'] ?? false,
            'cancellation_permitted' => $reservation['CancellationPermitted'] ?? false,

            // 时间信息
            'create_date_time' => $reservation['CreateDateTime'] ?? null,
            'update_date_time' => $reservation['UpdateDateTime'] ?? null,
            'on_hold_release_time' => $reservation['OnHoldReleaseTime'] ?? 0,

            // 酒店信息
            'hotel' => $this->transformHotelInfo($reservation['Hotel'] ?? []),
            'brand' => $this->transformBrandInfo($reservation['Brand'] ?? []),
            'chain' => $this->transformChainInfo($reservation['Chain'] ?? []),

            // 房间住宿信息
            'room_stay' => $this->transformRoomStay($reservation['RoomStay'] ?? []),

            // 客人信息
            'guests' => $this->transformGuests($reservation['Guests'] ?? []),

            // 价格信息
            'room_prices' => $this->transformRoomPrices($reservation['RoomPrices'] ?? []),

            // 预订政策
            'booking_policy' => $this->transformBookingPolicy($reservation['BookingPolicy'] ?? []),
            'cancel_policy' => $this->transformCancelPolicy($reservation['CancelPolicy'] ?? []),

            // 预订费用
            'booking_dues' => $this->transformBookingDues($reservation['BookingDues'] ?? [], $currencyCode),

            // 预订信息
            'booking_info' => $this->transformBookingInfo($reservation['BookingInfo'] ?? []),

            // 渠道信息
            'channels' => $this->transformChannels($reservation['Channels'] ?? []),

            // 内容信息（房型、价格等描述）
            'content' => $this->transformContent($reservation['Content'] ?? []),

            // 货币信息
            'currency' => $this->transformCurrency($reservation['Currency'] ?? []),

            // 语言信息
            'language' => $this->transformLanguage($reservation['Language'] ?? []),

            // 市场来源
            'market_source' => $this->transformMarketSource($reservation['MarketSource'] ?? []),

            // 通知信息
            'notification' => $this->transformNotification($reservation['Notification'] ?? []),

            // 活动记录
            'activity' => $this->transformActivity($reservation['Activity'] ?? []),

            // 其他信息
            'overrides' => $reservation['Overrides'] ?? [],
            'transportation' => $reservation['Transporation'] ?? [], // 注意原数据中的拼写错误
            'rule_tracking_list' => $reservation['RuleTrackingList'] ?? [],
            'source' => $this->transformSource($reservation['Source'] ?? []),
            'on_property_instructions' => $this->transformOnPropertyInstructions($reservation['OnPropertyInstructions'] ?? []),
            'administrative_actions' => $reservation['AdministrativeActions'] ?? [],
        ];
    }

    /**
     * 转换酒店信息
     */
    protected function transformHotelInfo(array $hotel): array
    {
        return [
            'id' => $hotel['Id'] ?? null,
            'code' => $hotel['Code'] ?? null,
            'name' => $hotel['Name'] ?? null,
        ];
    }

    /**
     * 转换品牌信息
     */
    protected function transformBrandInfo(array $brand): array
    {
        return [
            'id' => $brand['Id'] ?? null,
            'code' => $brand['Code'] ?? null,
            'name' => $brand['Name'] ?? null,
        ];
    }

    /**
     * 转换连锁酒店信息
     */
    protected function transformChainInfo(array $chain): array
    {
        return [
            'id' => $chain['Id'] ?? null,
            'code' => $chain['Code'] ?? null,
            'name' => $chain['Name'] ?? null,
        ];
    }

    /**
     * 转换房间住宿信息
     */
    protected function transformRoomStay(array $roomStay): array
    {
        return [
            'start_date' => $roomStay['StartDate'] ?? null,
            'end_date' => $roomStay['EndDate'] ?? null,
            'num_rooms' => $roomStay['NumRooms'] ?? 1,
            'group' => $roomStay['Group'] ?? false,
            'suppressed' => $roomStay['Suppressed'] ?? false,
            'is_eligible_for_accrual' => $roomStay['IsEligibleForAccrual'] ?? false,
            'guest_count' => $this->transformGuestCount($roomStay['GuestCount'] ?? []),
            'products' => $this->transformProducts($roomStay['Products'] ?? []),
        ];
    }

    /**
     * 转换客人数量信息
     */
    protected function transformGuestCount(array $guestCount): array
    {
        $transformed = [];
        foreach ($guestCount as $count) {
            $transformed[] = [
                'age_qualifying_code' => $count['AgeQualifyingCode'] ?? null,
                'num_guests' => $count['NumGuests'] ?? 0,
                'ages' => $count['Ages'] ?? [],
            ];
        }
        return $transformed;
    }

    /**
     * 转换产品信息
     */
    protected function transformProducts(array $products): array
    {
        $transformed = [];
        foreach ($products as $product) {
            $transformed[] = [
                'product' => [
                    'id' => $product['Product']['Id'] ?? null,
                    'rate_code' => $product['Product']['RateCode'] ?? null,
                    'room_code' => $product['Product']['RoomCode'] ?? null,
                ],
                'start_date' => $product['StartDate'] ?? null,
                'end_date' => $product['EndDate'] ?? null,
                'primary' => $product['Primary'] ?? false,
            ];
        }
        return $transformed;
    }

    /**
     * 转换客人信息
     */
    protected function transformGuests(array $guests): array
    {
        $transformed = [];
        foreach ($guests as $guest) {
            $transformed[] = [
                'role' => $guest['Role'] ?? null,
                'start_date' => $guest['StartDate'] ?? null,
                'end_date' => $guest['EndDate'] ?? null,
                'crs_reference_number' => $guest['CRS_referenceNumber'] ?? null,
                'deny_charge_to_room' => $guest['DenyChargeToRoom'] ?? false,
                'marketing_opt_in' => $guest['MarketingOptIn'] ?? false,
                'person_name' => $this->transformPersonName($guest['PersonName'] ?? []),
                'email_addresses' => $this->transformEmailAddresses($guest['EmailAddress'] ?? []),
                'contact_numbers' => $this->transformContactNumbers($guest['ContactNumbers'] ?? []),
                'locations' => $this->transformLocations($guest['Locations'] ?? []),
                'payments' => $this->transformPayments($guest['Payments'] ?? []),
            ];
        }
        return $transformed;
    }

    /**
     * 转换人名信息
     */
    protected function transformPersonName(array $personName): array
    {
        return [
            'prefix' => $personName['Prefix'] ?? null,
            'given_name' => $personName['GivenName'] ?? null,
            'surname' => $personName['Surname'] ?? null,
        ];
    }

    /**
     * 转换邮箱地址
     */
    protected function transformEmailAddresses(array $emailAddresses): array
    {
        $transformed = [];
        foreach ($emailAddresses as $email) {
            $transformed[] = [
                'type' => $email['Type'] ?? null,
                'value' => $email['Value'] ?? null,
                'default' => $email['Default'] ?? false,
            ];
        }
        return $transformed;
    }

    /**
     * 转换联系电话
     */
    protected function transformContactNumbers(array $contactNumbers): array
    {
        $transformed = [];
        foreach ($contactNumbers as $contact) {
            $transformed[] = [
                'type' => $contact['Type'] ?? null,
                'role' => $contact['Role'] ?? null,
                'use' => $contact['Use'] ?? null,
                'number' => $contact['Number'] ?? null,
                'code' => $contact['Code'] ?? null,
                'default' => $contact['Default'] ?? false,
                'sort_order' => $contact['SortOrder'] ?? 0,
            ];
        }
        return $transformed;
    }

    /**
     * 转换地址信息
     */
    protected function transformLocations(array $locations): array
    {
        $transformed = [];
        foreach ($locations as $location) {
            $address = $location['Address'] ?? [];
            $transformed[] = [
                'code' => $location['Code'] ?? null,
                'name' => $location['Name'] ?? null,
                'address' => [
                    'address_line' => is_string($address['AddressLine'] ?? null) ? $address['AddressLine'] : null,
                    'city' => is_string($address['City'] ?? null) ? $address['City'] : null,
                    'state_prov' => is_string($address['StateProv'] ?? null) ? $address['StateProv'] : null,
                    'country' => is_string($address['Country'] ?? null) ? $address['Country'] : null,
                    'postal_code' => is_string($address['PostalCode'] ?? null) ? $address['PostalCode'] : null,
                    'type' => is_string($address['Type'] ?? null) ? $address['Type'] : null,
                    'default' => is_bool($address['Default'] ?? null) ? $address['Default'] : false,
                ],
            ];
        }
        return $transformed;
    }

    /**
     * 转换支付信息
     */
    protected function transformPayments(array $payments): array
    {
        $transformed = [];
        foreach ($payments as $payment) {
            $paymentCard = $payment['PaymentCard'] ?? [];
            $transformed[] = [
                'type' => $payment['Type'] ?? null,
                'role' => $payment['Role'] ?? null,
                'vendor_status' => $payment['VendorStatus'] ?? null,
                'payment_card' => [
                    'card_holder' => is_string($paymentCard['CardHolder'] ?? null) ? $paymentCard['CardHolder'] : null,
                    'card_code' => is_string($paymentCard['CardCode'] ?? null) ? $paymentCard['CardCode'] : null,
                    'card_name' => is_string($paymentCard['CardName'] ?? null) ? $paymentCard['CardName'] : null,
                    'card_number' => is_string($paymentCard['CardNumber'] ?? null) ? $paymentCard['CardNumber'] : null,
                    'token' => is_string($paymentCard['Token'] ?? null) ? $paymentCard['Token'] : null,
                    'expire_date' => is_string($paymentCard['ExpireDate'] ?? null) ? $paymentCard['ExpireDate'] : null,
                ],
            ];
        }
        return $transformed;
    }

    /**
     * 转换房间价格信息
     */
    protected function transformRoomPrices(array $roomPrices): array
    {
        return [
            'total_price' => $this->transformPriceInfo($roomPrices['TotalPrice'] ?? []),
            'average_price' => $this->transformPriceInfo($roomPrices['AveragePrice'] ?? []),
            'price_breakdowns' => $this->transformPriceBreakdowns($roomPrices['PriceBreakdowns'] ?? []),
        ];
    }

    /**
     * 转换价格信息
     */
    protected function transformPriceInfo(array $priceInfo): array
    {
        $price = $priceInfo['Price'] ?? [];
        $currencyCode = $price['CurrencyCode'] ?? 'USD';

        $priceData = [
            'currency_code' => $currencyCode,
            'original_amount' => $price['OriginalAmount'] ?? 0,
            'total_amount' => $price['TotalAmount'] ?? 0,
            'amount_payable_now' => $price['AmountPayableNow'] ?? 0,
            'amount_pay_at_property' => $price['AmountPayAtProperty'] ?? 0,
            'total_amount_with_inclusive_taxes_fees' => $price['TotalAmountWithInclusiveTaxesFees'] ?? 0,
            'total_amount_including_taxes_fees' => $price['TotalAmountIncludingTaxesFees'] ?? 0,
            'original_amount_including_taxes_and_fees' => $price['OriginalAmountIncludingTaxesAndFees'] ?? 0,
            'taxes_fees_included' => $price['TaxesFeesIncluded'] ?? false,
            'tax' => $this->transformTaxInfo($price['Tax'] ?? [], $currencyCode),
            'fees' => $this->transformFeesInfo($price['Fees'] ?? [], $currencyCode),
        ];

        // 添加人民币转换
        if ($currencyCode !== 'CNY') {
            try {
                $convertedPriceData = $this->currencyService->convertPriceData($priceData, $currencyCode);
                $priceData = array_merge($priceData, $convertedPriceData);
            } catch (\Exception $e) {
                Log::warning('价格货币转换失败', [
                    'currency' => $currencyCode,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $priceData;
    }

    /**
     * 转换税费信息
     */
    protected function transformTaxInfo(array $tax, string $currencyCode = 'USD'): array
    {
        $taxData = [
            'amount' => $tax['Amount'] ?? 0,
            'stay_tax_amount' => $tax['StayTaxAmount'] ?? 0,
            'breakdown' => is_array($tax['Breakdown'] ?? null) ? $tax['Breakdown'] : [],
        ];

        // 添加人民币转换
        if ($currencyCode !== 'CNY') {
            try {
                $rate = $this->currencyService->getExchangeRate($currencyCode, 'CNY');
                if ($taxData['amount'] > 0) {
                    $taxData['amount_cny'] = round($taxData['amount'] * $rate, 2);
                }
                if ($taxData['stay_tax_amount'] > 0) {
                    $taxData['stay_tax_amount_cny'] = round($taxData['stay_tax_amount'] * $rate, 2);
                }
            } catch (\Exception $e) {
                Log::warning('税费货币转换失败', [
                    'currency' => $currencyCode,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $taxData;
    }

    /**
     * 转换费用信息
     */
    protected function transformFeesInfo(array $fees, string $currencyCode = 'USD'): array
    {
        $feesData = [
            'amount' => $fees['Amount'] ?? 0,
            'stay_fee_amount' => $fees['StayFeeAmount'] ?? 0,
            'breakdown' => is_array($fees['Breakdown'] ?? null) ? $fees['Breakdown'] : [],
        ];

        // 添加人民币转换
        if ($currencyCode !== 'CNY') {
            try {
                $rate = $this->currencyService->getExchangeRate($currencyCode, 'CNY');
                if ($feesData['amount'] > 0) {
                    $feesData['amount_cny'] = round($feesData['amount'] * $rate, 2);
                }
                if ($feesData['stay_fee_amount'] > 0) {
                    $feesData['stay_fee_amount_cny'] = round($feesData['stay_fee_amount'] * $rate, 2);
                }
            } catch (\Exception $e) {
                Log::warning('费用货币转换失败', [
                    'currency' => $currencyCode,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $feesData;
    }

    /**
     * 转换价格明细
     */
    protected function transformPriceBreakdowns(array $priceBreakdowns): array
    {
        $transformed = [];
        foreach ($priceBreakdowns as $breakdown) {
            $transformed[] = [
                'type' => $breakdown['Type'] ?? null,
                'product_prices' => $this->transformProductPrices($breakdown['ProductPrices'] ?? []),
            ];
        }
        return $transformed;
    }

    /**
     * 转换产品价格
     */
    protected function transformProductPrices(array $productPrices): array
    {
        $transformed = [];
        foreach ($productPrices as $productPrice) {
            $transformed[] = [
                'product' => is_string($productPrice['Product'] ?? null) ? $productPrice['Product'] : null,
                'price' => is_numeric($productPrice['Price'] ?? null) ? $productPrice['Price'] : null,
                'start_date' => is_string($productPrice['StartDate'] ?? null) ? $productPrice['StartDate'] : null,
                'end_date' => is_string($productPrice['EndDate'] ?? null) ? $productPrice['EndDate'] : null,
            ];
        }
        return $transformed;
    }

    /**
     * 转换预订政策
     */
    protected function transformBookingPolicy(array $bookingPolicy): array
    {
        return [
            'code' => $bookingPolicy['Code'] ?? null,
            'description' => $bookingPolicy['Description'] ?? null,
            'guarantee_level' => $bookingPolicy['GuaranteeLevel'] ?? null,
            'hold_time' => $bookingPolicy['HoldTime'] ?? null,
            'allow_pay' => $bookingPolicy['AllowPay'] ?? false,
            'requirements' => $bookingPolicy['Requirements'] ?? [],
            'deposit_fee' => $this->transformDepositFee($bookingPolicy['DepositFee'] ?? []),
        ];
    }

    /**
     * 转换押金费用
     */
    protected function transformDepositFee(array $depositFee): array
    {
        return [
            'amount' => $depositFee['Amount'] ?? 0,
            'due_days' => $depositFee['DueDays'] ?? 0,
            'due_type' => $depositFee['DueType'] ?? null,
            'tax_inclusive' => $depositFee['TaxInclusive'] ?? false,
            'is_pre_payment' => $depositFee['IsPrePayment'] ?? false,
            'type' => $depositFee['Type'] ?? null,
        ];
    }

    /**
     * 转换取消政策
     */
    protected function transformCancelPolicy(array $cancelPolicy): array
    {
        return [
            'code' => $cancelPolicy['Code'] ?? null,
            'description' => $cancelPolicy['Description'] ?? null,
            'cancellation_permitted' => $cancelPolicy['CancellationPermitted'] ?? false,
            'late_cancellation_permitted' => $cancelPolicy['LateCancellationPermitted'] ?? false,
            'cancel_time' => $cancelPolicy['CancelTime'] ?? null,
            'cancel_time_in' => $cancelPolicy['CancelTimeIn'] ?? null,
            'charge_threshold' => $cancelPolicy['ChargeThreshold'] ?? null,
            'charge_type' => $cancelPolicy['ChargeType'] ?? null,
            'modification_restrictions' => $cancelPolicy['ModificationRestrictions'] ?? null,
            'cancel_fee_amount' => $this->transformFeeAmount($cancelPolicy['CancelFeeAmount'] ?? []),
            'cancel_fee_type' => $cancelPolicy['CancelFeeType'] ?? null,
            'no_show_fee_amount' => $this->transformFeeAmount($cancelPolicy['NoShowFeeAmount'] ?? []),
            'no_show_fee_type' => $cancelPolicy['NoShowFeeType'] ?? null,
            'charges' => $this->transformCancelCharges($cancelPolicy['Charges'] ?? []),
        ];
    }

    /**
     * 转换费用金额
     */
    protected function transformFeeAmount(array $feeAmount): array
    {
        return [
            'value' => $feeAmount['Value'] ?? 0,
            'tax_inclusive' => $feeAmount['TaxInclusive'] ?? false,
        ];
    }

    /**
     * 转换取消费用
     */
    protected function transformCancelCharges(array $charges): array
    {
        $transformed = [];
        foreach ($charges as $charge) {
            $transformed[] = [
                'days_to_arrive' => $charge['DaysToArrive'] ?? 0,
                'cancel_fee_includes_tax' => $charge['CancelFeeIncludesTax'] ?? false,
                'cancel_fee_amount' => $charge['CancelFeeAmount'] ?? 0,
            ];
        }
        return $transformed;
    }

    /**
     * 转换预订费用
     */
    protected function transformBookingDues(array $bookingDues): array
    {
        return [
            'deposit' => $this->transformDeposit($bookingDues['Deposit'] ?? []),
            'cancel_penalty' => $this->transformCancelPenalty($bookingDues['CancelPenalty'] ?? []),
            'no_show_charge' => $this->transformNoShowCharge($bookingDues['NoShowCharge'] ?? []),
        ];
    }

    /**
     * 转换押金信息
     */
    protected function transformDeposit(array $deposit): array
    {
        return [
            'due_date' => $deposit['DueDate'] ?? null,
            'amount' => $deposit['Amount'] ?? 0,
            'amount_without_tax' => $deposit['AmountWithoutTax'] ?? 0,
            'status' => $deposit['Status'] ?? null,
        ];
    }

    /**
     * 转换取消罚金
     */
    protected function transformCancelPenalty(array $cancelPenalty): array
    {
        return [
            'amount' => $cancelPenalty['Amount'] ?? 0,
            'deadline' => $cancelPenalty['Deadline'] ?? null,
            'charge_list' => $cancelPenalty['ChargeList'] ?? [],
        ];
    }

    /**
     * 转换未到店费用
     */
    protected function transformNoShowCharge(array $noShowCharge): array
    {
        return [
            'amount' => $noShowCharge['Amount'] ?? 0,
        ];
    }

    /**
     * 转换预订信息
     */
    protected function transformBookingInfo(array $bookingInfo): array
    {
        return [
            'booked_by' => $bookingInfo['BookedBy'] ?? null,
            'booking_date' => $bookingInfo['BookingDate'] ?? null,
            'modified_by' => $bookingInfo['ModifiedBy'] ?? null,
            'entry_channel_booking_date' => $bookingInfo['EntryChannelBookingDate'] ?? null,
        ];
    }

    /**
     * 转换渠道信息
     */
    protected function transformChannels(array $channels): array
    {
        return [
            'primary_channel' => [
                'code' => $channels['PrimaryChannel']['Code'] ?? null,
                'description' => $channels['PrimaryChannel']['Description'] ?? null,
            ],
            'secondary_channel' => [
                'code' => $channels['SecondaryChannel']['Code'] ?? null,
                'description' => $channels['SecondaryChannel']['Description'] ?? null,
            ],
            'sub_source_code' => $channels['SubSourceCode'] ?? null,
        ];
    }

    /**
     * 转换内容信息
     */
    protected function transformContent(array $content): array
    {
        return [
            'rooms' => $this->transformContentRooms($content['Rooms'] ?? []),
            'rates' => $this->transformContentRates($content['Rates'] ?? []),
            'room_categories' => $this->transformRoomCategories($content['RoomCategories'] ?? []),
        ];
    }

    /**
     * 转换内容房型信息
     */
    protected function transformContentRooms(array $rooms): array
    {
        $transformed = [];
        foreach ($rooms as $room) {
            $transformed[] = [
                'code' => $room['Code'] ?? null,
                'name' => $room['Name'] ?? null,
                'description' => $room['Description'] ?? null,
                'detailed_description' => $room['DetailedDescription'] ?? null,
                'category_code' => $room['CategoryCode'] ?? null,
            ];
        }
        return $transformed;
    }

    /**
     * 转换内容价格信息
     */
    protected function transformContentRates(array $rates): array
    {
        $transformed = [];
        foreach ($rates as $rate) {
            $transformed[] = [
                'code' => $rate['Code'] ?? null,
                'name' => $rate['Name'] ?? null,
                'display_name' => $rate['DisplayName'] ?? null,
                'description' => $rate['Description'] ?? null,
                'detailed_description' => $rate['DetailedDescription'] ?? null,
                'effective_date' => $rate['EffectiveDate'] ?? null,
                'expire_date' => $rate['ExpireDate'] ?? null,
                'redemption_type' => $rate['RedemptionType'] ?? null,
                'primary' => $rate['Primary'] ?? false,
            ];
        }
        return $transformed;
    }

    /**
     * 转换房型分类
     */
    protected function transformRoomCategories(array $roomCategories): array
    {
        $transformed = [];
        foreach ($roomCategories as $category) {
            $transformed[] = [
                'category_code' => $category['CategoryCode'] ?? null,
                'name' => $category['Name'] ?? null,
                'description' => $category['Description'] ?? null,
            ];
        }
        return $transformed;
    }

    /**
     * 转换货币信息
     */
    protected function transformCurrency(array $currency): array
    {
        return [
            'code' => $currency['Code'] ?? null,
            'name' => $currency['Name'] ?? null,
            'symbol' => $currency['Symbol'] ?? null,
        ];
    }

    /**
     * 转换语言信息
     */
    protected function transformLanguage(array $language): array
    {
        return [
            'code' => $language['Code'] ?? null,
            'lang_id' => $language['LangId'] ?? 0,
            'name' => $language['Name'] ?? null,
        ];
    }

    /**
     * 转换市场来源
     */
    protected function transformMarketSource(array $marketSource): array
    {
        return [
            'code' => $marketSource['Code'] ?? null,
            'name' => $marketSource['Name'] ?? null,
        ];
    }

    /**
     * 转换通知信息
     */
    protected function transformNotification(array $notification): array
    {
        return [
            'language_code' => $notification['LanguageCode'] ?? null,
            'language_id' => $notification['LanguageId'] ?? 0,
        ];
    }

    /**
     * 转换活动记录
     */
    protected function transformActivity(array $activity): array
    {
        $transformed = [];
        foreach ($activity as $act) {
            $transformed[] = [
                'type' => $act['Type'] ?? null,
                'value' => $act['Value'] ?? null,
            ];
        }
        return $transformed;
    }

    /**
     * 转换来源信息
     */
    protected function transformSource(array $source): array
    {
        return [
            'booking_url' => $source['BookingUrl'] ?? null,
        ];
    }

    /**
     * 转换酒店内指令
     */
    protected function transformOnPropertyInstructions(array $instructions): array
    {
        return [
            'charge_routing_list' => $instructions['ChargeRoutingList'] ?? [],
        ];
    }

    /**
     * 转换分页信息
     */
    protected function transformPagination(array $pagination): array
    {
        return [
            'size' => $pagination['Size'] ?? 0,
            'start' => $pagination['Start'] ?? 0,
            'total' => $pagination['Total'] ?? 0,
        ];
    }
}
