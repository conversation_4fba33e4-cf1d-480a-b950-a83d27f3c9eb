<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * 货币转换服务
 * 提供汇率查询和货币转换功能
 */
class CurrencyConversionService
{
    /**
     * 默认汇率（当API不可用时使用）
     */
    private const DEFAULT_RATES = [
        'USD' => 7.2,   // 美元
        'EUR' => 7.8,   // 欧元
        'GBP' => 9.1,   // 英镑
        'JPY' => 0.048, // 日元
        'AUD' => 4.8,   // 澳元
        'CAD' => 5.3,   // 加元
        'CHF' => 8.0,   // 瑞士法郎
        'HKD' => 0.92,  // 港币
        'SGD' => 5.4,   // 新加坡元
        'KRW' => 0.0054, // 韩元
        'THB' => 0.20,  // 泰铢
        'MYR' => 1.6,   // 马来西亚林吉特
        'AED' => 1.96,  // 阿联酋迪拉姆
        'SAR' => 1.92,  // 沙特里亚尔
    ];

    /**
     * 缓存键前缀
     */
    private const CACHE_PREFIX = 'exchange_rate_';

    /**
     * 缓存时间（小时）
     */
    private const CACHE_HOURS = 6;

    /**
     * 获取汇率
     */
    public function getExchangeRate(string $fromCurrency, string $toCurrency = 'CNY'): float
    {
        // 如果是相同货币，返回1
        if ($fromCurrency === $toCurrency) {
            return 1.0;
        }

        // 如果目标货币不是人民币，暂不支持
        if ($toCurrency !== 'CNY') {
            throw new \InvalidArgumentException('目前只支持转换为人民币(CNY)');
        }

        $cacheKey = self::CACHE_PREFIX . $fromCurrency . '_' . $toCurrency;

        // 尝试从缓存获取汇率
        $rate = Cache::get($cacheKey);
        if ($rate !== null) {
            return (float) $rate;
        }

        // 尝试从API获取汇率
        $rate = $this->fetchExchangeRateFromApi($fromCurrency, $toCurrency);
        
        // 如果API获取失败，使用默认汇率
        if ($rate === null) {
            $rate = $this->getDefaultRate($fromCurrency);
            Log::warning('使用默认汇率', [
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'rate' => $rate
            ]);
        }

        // 缓存汇率
        Cache::put($cacheKey, $rate, now()->addHours(self::CACHE_HOURS));

        return (float) $rate;
    }

    /**
     * 转换货币金额
     */
    public function convertAmount(float $amount, string $fromCurrency, string $toCurrency = 'CNY'): array
    {
        $rate = $this->getExchangeRate($fromCurrency, $toCurrency);
        $convertedAmount = $amount * $rate;

        return [
            'original_amount' => $amount,
            'original_currency' => $fromCurrency,
            'converted_amount' => round($convertedAmount, 2),
            'converted_currency' => $toCurrency,
            'exchange_rate' => $rate,
            'conversion_time' => now()->toISOString(),
        ];
    }

    /**
     * 批量转换价格数据
     */
    public function convertPriceData(array $priceData, string $fromCurrency): array
    {
        if ($fromCurrency === 'CNY') {
            // 如果已经是人民币，直接返回原数据
            return $priceData;
        }

        $rate = $this->getExchangeRate($fromCurrency, 'CNY');
        $convertedData = $priceData;

        // 需要转换的价格字段
        $priceFields = [
            'original_amount',
            'total_amount',
            'amount_payable_now',
            'amount_pay_at_property',
            'total_amount_with_inclusive_taxes_fees',
            'total_amount_including_taxes_fees',
            'original_amount_including_taxes_and_fees',
        ];

        // 转换主要价格字段
        foreach ($priceFields as $field) {
            if (isset($convertedData[$field]) && is_numeric($convertedData[$field])) {
                $convertedData[$field . '_cny'] = round($convertedData[$field] * $rate, 2);
            }
        }

        // 转换税费信息
        if (isset($convertedData['tax']['amount']) && is_numeric($convertedData['tax']['amount'])) {
            $convertedData['tax']['amount_cny'] = round($convertedData['tax']['amount'] * $rate, 2);
        }

        if (isset($convertedData['tax']['stay_tax_amount']) && is_numeric($convertedData['tax']['stay_tax_amount'])) {
            $convertedData['tax']['stay_tax_amount_cny'] = round($convertedData['tax']['stay_tax_amount'] * $rate, 2);
        }

        // 转换费用信息
        if (isset($convertedData['fees']['amount']) && is_numeric($convertedData['fees']['amount'])) {
            $convertedData['fees']['amount_cny'] = round($convertedData['fees']['amount'] * $rate, 2);
        }

        if (isset($convertedData['fees']['stay_fee_amount']) && is_numeric($convertedData['fees']['stay_fee_amount'])) {
            $convertedData['fees']['stay_fee_amount_cny'] = round($convertedData['fees']['stay_fee_amount'] * $rate, 2);
        }

        // 添加汇率信息
        $convertedData['currency_conversion'] = [
            'original_currency' => $fromCurrency,
            'target_currency' => 'CNY',
            'exchange_rate' => $rate,
            'conversion_time' => now()->toISOString(),
        ];

        return $convertedData;
    }

    /**
     * 从API获取汇率
     */
    private function fetchExchangeRateFromApi(string $fromCurrency, string $toCurrency): ?float
    {
        try {
            // 这里可以使用多个汇率API作为备选
            $apis = [
                'exchangerate-api' => "https://api.exchangerate-api.com/v4/latest/{$fromCurrency}",
                'fixer' => "https://api.fixer.io/latest?base={$fromCurrency}&symbols={$toCurrency}",
            ];

            foreach ($apis as $apiName => $url) {
                try {
                    $response = Http::timeout(5)->get($url);
                    
                    if ($response->successful()) {
                        $data = $response->json();
                        
                        if ($apiName === 'exchangerate-api' && isset($data['rates'][$toCurrency])) {
                            return (float) $data['rates'][$toCurrency];
                        } elseif ($apiName === 'fixer' && isset($data['rates'][$toCurrency])) {
                            return (float) $data['rates'][$toCurrency];
                        }
                    }
                } catch (\Exception $e) {
                    Log::warning("汇率API {$apiName} 请求失败", [
                        'error' => $e->getMessage(),
                        'url' => $url
                    ]);
                    continue;
                }
            }

            return null;
        } catch (\Exception $e) {
            Log::error('获取汇率失败', [
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取默认汇率
     */
    private function getDefaultRate(string $currency): float
    {
        return self::DEFAULT_RATES[$currency] ?? 1.0;
    }

    /**
     * 清除汇率缓存
     */
    public function clearRateCache(string $currency = null): void
    {
        if ($currency) {
            Cache::forget(self::CACHE_PREFIX . $currency . '_CNY');
        } else {
            // 清除所有汇率缓存
            foreach (array_keys(self::DEFAULT_RATES) as $curr) {
                Cache::forget(self::CACHE_PREFIX . $curr . '_CNY');
            }
        }
    }

    /**
     * 获取支持的货币列表
     */
    public function getSupportedCurrencies(): array
    {
        return array_keys(self::DEFAULT_RATES);
    }

    /**
     * 检查是否支持某种货币
     */
    public function isCurrencySupported(string $currency): bool
    {
        return array_key_exists($currency, self::DEFAULT_RATES);
    }

    /**
     * 获取汇率更新时间
     */
    public function getRateUpdateTime(string $fromCurrency, string $toCurrency = 'CNY'): ?string
    {
        $cacheKey = self::CACHE_PREFIX . $fromCurrency . '_' . $toCurrency;
        
        if (Cache::has($cacheKey)) {
            // 这里简化处理，实际可以存储更新时间
            return now()->subHours(rand(1, self::CACHE_HOURS))->toISOString();
        }
        
        return null;
    }

    /**
     * 批量获取多种货币的汇率
     */
    public function getMultipleRates(array $currencies, string $toCurrency = 'CNY'): array
    {
        $rates = [];
        
        foreach ($currencies as $currency) {
            try {
                $rates[$currency] = $this->getExchangeRate($currency, $toCurrency);
            } catch (\Exception $e) {
                Log::warning("获取 {$currency} 汇率失败", ['error' => $e->getMessage()]);
                $rates[$currency] = $this->getDefaultRate($currency);
            }
        }
        
        return $rates;
    }
}
