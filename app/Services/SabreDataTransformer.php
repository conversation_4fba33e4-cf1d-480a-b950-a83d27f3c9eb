<?php

namespace App\Services;

use App\DTOs\RoomAvailabilityResponseDTO;
use App\DTOs\RoomDTO;
use App\DTOs\RoomRateDTO;
use App\DTOs\DailyPriceDTO;
use App\Models\ExchangeRate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class SabreDataTransformer
{
    /**
     * 汇率配置 - 从配置文件获取或使用默认值
     */
    protected array $exchangeRates = [
        // 兜底倍率（到CNY），当数据库未命中时使用
        'USD' => 7.2,
        'EUR' => 7.8,
        'GBP' => 9.1,
        'JPY' => 0.048,
        'CNY' => 1.0,
    ];

    public function __construct()
    {
        // 从配置文件加载汇率（如果config函数存在）
        if (function_exists('config')) {
            $this->exchangeRates = array_merge(
                $this->exchangeRates,
                config('sabre.exchange_rates', [])
            );
        }
    }

    /**
     * 将Sabre可用性响应转换为标准格式
     */
    public function transformAvailabilityResponse(array $sabreResponse): RoomAvailabilityResponseDTO
    {
        $rooms = [];

        try {
            // 检查响应结构
            if (!isset($sabreResponse['productAvailability']['Prices'])) {
                Log::warning('Sabre response missing productAvailability.Prices', ['response' => $sabreResponse]);
                return new RoomAvailabilityResponseDTO([]);
            }
            $prices = $sabreResponse['productAvailability']['Prices'];

            // 从真实Sabre数据结构中获取房型和价格信息
            $roomList = $sabreResponse['contentLists']['RoomList'] ?? [];
            $rateList = $sabreResponse['contentLists']['RateList'] ?? [];

            // 按房型分组价格数据
            $groupedRooms = $this->groupByRoom($prices, $roomList, $rateList);

            foreach ($groupedRooms as $roomCode => $roomData) {
                $room = $this->transformRoom($roomCode, $roomData, $roomList, $rateList);
                if ($room) {
                    $rooms[] = $room;
                }
            }

        } catch (\Exception $e) {
            Log::error('Error transforming Sabre availability response', [
                'error' => $e->getMessage(),
                'response' => $sabreResponse
            ]);
        }

        return new RoomAvailabilityResponseDTO($rooms);
    }

    /**
     * 按房型分组价格数据
     */
    protected function groupByRoom(array $prices, array $roomList, array $rateList): array
    {
        $grouped = [];

        foreach ($prices as $priceData) {
            $roomCode = $priceData['Product']['Room']['Code'] ?? 'UNKNOWN';

            if (!isset($grouped[$roomCode])) {
                // 从RoomList中获取房型详细信息
                $roomInfo = $this->getRoomInfoFromRoomList($roomCode, $roomList);

                $grouped[$roomCode] = [
                    'room_info' => $roomInfo,
                    'rates' => []
                ];
            }

            $grouped[$roomCode]['rates'][] = $priceData;
        }

        return $grouped;
    }

    /**
     * 从RoomList中获取房型信息
     */
    protected function getRoomInfoFromRoomList(string $roomCode, array $roomList): array
    {
        $roomInfo = [
            'Code' => $roomCode,
            'Name' => $roomCode,
            'Description' => $roomCode
        ];

        // 查找房型信息
        foreach ($roomList as $room) {
            if ($room['Code'] === $roomCode) {
                $roomInfo['Name'] = $room['Name'] ?? $roomCode;
                // 优先使用Details.DetailedDescription，然后是Details.Description，最后是Name
                $roomInfo['Description'] = $room['Details']['DetailedDescription'] ??
                                         $room['Details']['Description'] ??
                                         $room['DetailedDescription'] ??
                                         $room['Description'] ??
                                         $room['Name'] ??
                                         $roomCode;
                $roomInfo['MaxOccupancy'] = $room['MaxOccupancy'] ?? null;
                break;
            }
        }

        return $roomInfo;
    }

    /**
     * 从RateList中获取价格信息
     */
    protected function getRateInfoFromRateList(string $rateCode, array $rateList): array
    {
        $rateInfo = [
            'Code' => $rateCode,
            'Name' => $rateCode,
            'Description' => $rateCode
        ];

        // 查找价格信息
        foreach ($rateList as $rate) {
            if ($rate['Code'] === $rateCode) {
                $rateInfo['Name'] = $rate['Name'] ?? $rateCode;
                // 优先使用Details.DetailedDescription，然后是Details.Description，最后是Name
                $rateInfo['Description'] = $rate['Details']['DetailedDescription'] ??
                                         $rate['Details']['Description'] ??
                                         $rate['DetailedDescription'] ??
                                         $rate['Description'] ??
                                         $rate['Name'] ??
                                         $rateCode;
                break;
            }
        }

        return $rateInfo;
    }

    /**
     * 从Sabre数据中提取价格信息
     */
    protected function extractPriceFromSabreData(array $rateData): array
    {
        $priceInfo = [
            'currency' => 'USD',
            'total' => 0.0,
            'base' => 0.0,
            'tax' => 0.0,
            'fee' => 0.0
        ];

        // 根据真实Sabre数据结构查找价格数据
        $product = $rateData['Product'] ?? [];
        $prices = $product['Prices'] ?? [];

        // 优先使用Total价格
        if (isset($prices['Total']['Price'])) {
            $price = $prices['Total']['Price'];
            $priceInfo = $this->parsePriceObject($price);
        }
        // 其次使用PerNight价格
        elseif (isset($prices['PerNight']['Price'])) {
            $price = $prices['PerNight']['Price'];
            $priceInfo = $this->parsePriceObject($price);
        }
        // 最后尝试从Daily价格中获取（使用第一天的价格）
        elseif (isset($prices['Daily']) && is_array($prices['Daily']) && !empty($prices['Daily'])) {
            $firstDayPrice = $prices['Daily'][0]['Price'] ?? [];
            if (!empty($firstDayPrice)) {
                $priceInfo = $this->parsePriceObject($firstDayPrice);
            }
        }

        return $priceInfo;
    }

    /**
     * 解析价格对象
     */
    protected function parsePriceObject(array $price): array
    {
        $priceInfo = [
            'currency' => $price['CurrencyCode'] ?? 'USD',
            'total' => 0.0,
            'base' => 0.0,
            'tax' => 0.0,
            'fee' => 0.0
        ];

        // 基础价格
        $priceInfo['base'] = (float) ($price['Amount'] ?? 0);

        // 税费
        if (isset($price['Tax']['Amount'])) {
            $priceInfo['tax'] = (float) $price['Tax']['Amount'];
        }

        // 服务费
        if (isset($price['Fees']['Amount'])) {
            $priceInfo['fee'] = (float) $price['Fees']['Amount'];
        }

        // 总价 - 优先使用AmountWithTaxesFees，其次使用计算值
        if (isset($price['Total']['AmountWithTaxesFees'])) {
            $priceInfo['total'] = (float) $price['Total']['AmountWithTaxesFees'];
        } elseif (isset($price['Total']['Amount'])) {
            $priceInfo['total'] = (float) $price['Total']['Amount'];
        } else {
            // 如果没有总价，计算总价
            $priceInfo['total'] = $priceInfo['base'] + $priceInfo['tax'] + $priceInfo['fee'];
        }

        return $priceInfo;
    }

    /**
     * 转换单个房型数据
     */
    protected function transformRoom(string $roomCode, array $roomData, array $roomList, array $rateList): ?RoomDTO
    {
        try {
            $roomInfo = $roomData['room_info'];
            $rates = $roomData['rates'];

            $roomName = $roomInfo['Name'] ?? $roomCode;
            $roomDescription = $roomInfo['Description'] ?? $roomInfo['Name'] ?? $roomCode;

            $roomRateList = [];
            foreach ($rates as $rateData) {
                $roomRate = $this->transformRoomRate($roomCode, $rateData, $rateList);
                if ($roomRate) {
                    $roomRateList[] = $roomRate;
                }
            }

            if (empty($roomRateList)) {
                return null;
            }

            return new RoomDTO(
                roomCode: $roomCode,
                roomName: $roomName,
                roomDescription: $roomDescription,
                roomRateList: $roomRateList
            );

        } catch (\Exception $e) {
            Log::error('Error transforming room data', [
                'room_code' => $roomCode,
                'error' => $e->getMessage(),
                'room_data' => $roomData
            ]);
            return null;
        }
    }

    /**
     * 转换房价数据
     */
    protected function transformRoomRate(string $roomCode, array $rateData, array $rateList): ?RoomRateDTO
    {
        try {
            // 根据真实的Sabre数据结构提取信息
            $product = $rateData['Product'] ?? [];
            $rate = $product['Rate'] ?? [];
            $policies = $rateData['Policies'] ?? [];

            // 提取基本信息
            $rateCode = $rate['Code'] ?? 'UNKNOWN';

            // 从RateList中获取价格名称和描述
            $rateInfo = $this->getRateInfoFromRateList($rateCode, $rateList);
            $rateName = $rateInfo['Name'] ?? $rate['Name'] ?? $rateCode;
            $rateDescription = $rateInfo['Description'] ?? $rate['Description'] ?? $rateInfo['Name'] ?? $rateName;

            // 根据真实Sabre数据结构提取价格信息
            $priceInfo = $this->extractPriceFromSabreData($rateData);

            $currency = $priceInfo['currency'];
            $totalAmount = toCent($priceInfo['total']);
            $baseAmount = toCent($priceInfo['base']);
            $taxAmount = toCent($priceInfo['tax']);
            $feeAmount = toCent($priceInfo['fee']);

            // 转换为人民币（分）
            $exchangeRate = $this->getToCnyMultiplier($currency);
            $cnyPrice = $baseAmount * $exchangeRate;
            $cnyFee = $feeAmount * $exchangeRate;
            $cnyTax = $taxAmount * $exchangeRate;
            $cnyTotalPrice = $cnyPrice + $cnyTax + $cnyFee;
            // 提取担保政策
            $guaranteePolicy = $this->extractGuaranteePolicy($policies);

            // 提取取消政策
            $cancelRuleString = $this->extractCancellationPolicy($policies);

            // 检测是否是会员价格
            $isMemberRate = $this->checkIfMemberRate($rateData);

            // 提取每日价格数据
            $dailyPrices = $this->extractDailyPrices($rateData, $currency);

            return new RoomRateDTO(
                roomCode: $roomCode,
                rateCode: $rateCode,
                rateName: $rateName,
                rateDescription: $rateDescription,
                guaranteePolicy: $guaranteePolicy,
                currency: $currency,
                price: $baseAmount ,
                fee: $feeAmount,
                tax: $taxAmount,
                total_price: $totalAmount,
                cny_price: $cnyPrice,
                cny_fee: $cnyFee,
                cny_tax: $cnyTax,
                cny_total_price: $cnyTotalPrice,
                cancelRuleString: $cancelRuleString,
                isMemberRate: $isMemberRate,
                dailyPrices: $dailyPrices
            );

        } catch (\Exception $e) {
            Log::error('Error transforming room rate data', [
                'room_code' => $roomCode,
                'error' => $e->getMessage(),
                'rate_data' => $rateData
            ]);
            return null;
        }
    }

    /**
     * 提取担保政策
     */
    protected function extractGuaranteePolicy(array $policies): string
    {
        // 根据真实Sabre数据结构查找担保政策
        if (isset($policies['Guarantee'])) {
            $guarantee = $policies['Guarantee'];
            if (is_array($guarantee) && isset($guarantee[0]['Code'])) {
                return $guarantee[0]['Code'];
            } elseif (is_string($guarantee)) {
                return $guarantee;
            }
        }

        // 查找其他可能的担保政策字段
        foreach ($policies as $policy) {
            if (is_array($policy)) {
                if (isset($policy['Type']) && $policy['Type'] === 'Guarantee') {
                    return $policy['Code'] ?? 'GCC_CRD';
                }
                if (isset($policy['GuaranteeType'])) {
                    return $policy['GuaranteeType'];
                }
            }
        }

        // 默认担保政策
        return 'GCC_CRD';
    }

    /**
     * 提取取消政策
     */
    protected function extractCancellationPolicy(array $policies): string
    {
        $cancellationRules = [];

        // 根据真实Sabre数据结构查找取消政策
        if (isset($policies['Cancellation'])) {
            $cancellation = $policies['Cancellation'];

            if (is_array($cancellation)) {
                foreach ($cancellation as $rule) {
                    if (is_array($rule)) {
                        $description = $rule['Description'] ?? '';
                        $deadline = $rule['Deadline'] ?? '';
                        $penalty = $rule['Penalty'] ?? '';
                        $amount = $rule['Amount'] ?? '';

                        if ($description) {
                            $cancellationRules[] = $description;
                        } elseif ($deadline) {
                            $penaltyText = $penalty ?: ($amount ? "收取{$amount}费用" : "收取费用");
                            $cancellationRules[] = "在{$deadline}前取消，{$penaltyText}";
                        }
                    } elseif (is_string($rule)) {
                        $cancellationRules[] = $rule;
                    }
                }
            } elseif (is_string($cancellation)) {
                $cancellationRules[] = $cancellation;
            }
        }

        // 查找其他可能的取消政策字段
        foreach ($policies as $policy) {
            if (is_array($policy) && isset($policy['Type']) && $policy['Type'] === 'Cancellation') {
                $description = $policy['Description'] ?? '';
                $deadline = $policy['Deadline'] ?? '';
                $penalty = $policy['Penalty'] ?? '';

                if ($description) {
                    $cancellationRules[] = $description;
                } elseif ($deadline && $penalty) {
                    $cancellationRules[] = "在{$deadline}前取消，收取{$penalty}费用";
                }
            }
        }

        if (empty($cancellationRules)) {
            return '取消政策请咨询酒店';
        }

        return implode('；', $cancellationRules);
    }

    /**
     * 设置汇率
     */
    public function setExchangeRate(string $currency, float $rate): void
    {
        $this->exchangeRates[$currency] = $rate;
    }

    /**
     * 获取汇率
     */
    public function getExchangeRate(string $currency): float
    {
        return $this->getToCnyMultiplier($currency);
    }

    /**
     * 批量设置汇率
     */
    public function setExchangeRates(array $rates): void
    {
        $this->exchangeRates = array_merge($this->exchangeRates, $rates);
    }

    /**
     * 计算到CNY的倍率：multiplier = rateUSDPerUnit(currency) / rateUSDPerUnit(CNY)
     * 其中 rate 字段存的是“1 单位该货币 = ? USD”。
     */
    protected function getToCnyMultiplier(string $currency): float
    {
        try {
            $currency = strtoupper($currency);
            $rateCurrency = $this->getUsdPerUnit($currency);
            $rateCny = $this->getUsdPerUnit('CNY');

            if ($rateCurrency !== null && $rateCny !== null && $rateCny > 0) {
                return (float) ($rateCurrency / $rateCny);
            }
        } catch (\Throwable $e) {
            Log::warning('读取数据库汇率失败，使用兜底倍率', [
                'currency' => $currency,
                'error' => $e->getMessage()
            ]);
        }

        return $this->exchangeRates[$currency] ?? 1.0;
    }

    /**
     * 读取数据库中的“1 单位该货币 = ? USD”。
     */
    protected function getUsdPerUnit(string $currency): ?float
    {
        $currency = strtoupper($currency);
        $cacheKey = 'exchange_rate_usd_per_unit_' . $currency;
        $rate = Cache::remember($cacheKey, now()->addDay(), function () use ($currency) {
            return ExchangeRate::query()
                ->where('currency', $currency)
                ->value('rate');
        });
        return $rate === null ? null : (float) $rate;
    }

    /**
     * 检测是否是会员价格
     * 通过检查ReferenceList中是否包含Loyalty和GHA来判断
     */
    protected function checkIfMemberRate(array $rateData): bool
    {
        // 检查Product级别的ReferenceList
        $product = $rateData['Product'] ?? [];
        $referenceList = $product['ReferenceList'] ?? [];

        // 也检查根级别的ReferenceList
        if (empty($referenceList)) {
            $referenceList = $rateData['ReferenceList'] ?? [];
        }

        if (!is_array($referenceList)) {
            return false;
        }

        // 遍历ReferenceList查找Loyalty标记
        foreach ($referenceList as $reference) {
            if (is_array($reference)) {
                $ref = $reference['Ref'] ?? '';
                $refValue = $reference['RefValue'] ?? '';

                // 检查是否是会员价格标记
                if ($ref === 'Loyalty' && $refValue === 'GHA') {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 提取每日价格数据
     */
    protected function extractDailyPrices(array $rateData, string $currency): array
    {
        $dailyPrices = [];

        // 获取每日价格数据
        $product = $rateData['Product'] ?? [];
        $prices = $product['Prices'] ?? [];
        $dailyPricesData = $prices['Daily'] ?? [];

        if (!is_array($dailyPricesData)) {
            return [];
        }

        // 获取到CNY的倍率
        $exchangeRate = $this->getToCnyMultiplier($currency);

        foreach ($dailyPricesData as $dailyPriceData) {
            try {
                $price = $dailyPriceData['Price'] ?? [];
                $date = $dailyPriceData['Date'] ?? '';
                $availableInventory = $dailyPriceData['AvailableInventory'] ?? 0;

                // 解析每日价格
                $dailyPriceInfo = $this->parsePriceObject($price);

                // 转换为人民币（分）
                $cnyPrice = toCent($dailyPriceInfo['base'] * $exchangeRate);
                $cnyFee = toCent($dailyPriceInfo['fee'] * $exchangeRate);
                $cnyTax = toCent($dailyPriceInfo['tax'] * $exchangeRate);
                $cnyTotalPrice = toCent($dailyPriceInfo['total'] * $exchangeRate);

                $dailyPrice = new DailyPriceDTO(
                    date: date('Y-m-d', strtotime($date)),
                    currency: $dailyPriceInfo['currency'],
                    price: $dailyPriceInfo['base'] * 100,
                    fee: $dailyPriceInfo['fee'] * 100,
                    tax: $dailyPriceInfo['tax'] * 100,
                    total_price: $dailyPriceInfo['total'] * 100,
                    cny_price: $cnyPrice,
                    cny_fee: $cnyFee,
                    cny_tax: $cnyTax,
                    cny_total_price: $cnyTotalPrice,
                    availableInventory: (int) $availableInventory
                );

                $dailyPrices[] = $dailyPrice;

            } catch (\Exception $e) {
                Log::warning('Error parsing daily price data', [
                    'error' => $e->getMessage(),
                    'daily_price_data' => $dailyPriceData
                ]);
                continue;
            }
        }

        return $dailyPrices;
    }
}
