# GXP App-Key 配置说明

本文档详细说明了GXP API接口中不同app-key的使用配置。

## 🔑 App-Key 类型

根据Postman文档分析，GXP API使用两种不同的app-key：

### 1. China App-Key (`{{app-key-china}}`)
用于以下接口：
- **Guest Token** - `POST /api/v1/auth/token`
- **Reserve Repo Posting** - `POST /api/v1/reservations-repo`
- **My Bookings** - `GET /api/v1/reservations-repo`

### 2. Rotana App-Key (`{{app-key-rotana}}`)
用于以下接口：
- **Currency Conversion Rate** - `GET /api/v1/ddollars/exchange-rate`

## 🏗️ 实现架构

### 常量类定义
```php
// app/Constants/GxpAppKeyType.php
class GxpAppKeyType
{
    public const CHINA = 'china';
    public const ROTANA = 'rotana';
    
    public static function getTypeByEndpoint(string $endpoint): string
    {
        $endpointMapping = [
            'auth/token' => self::CHINA,
            'ddollars/exchange-rate' => self::ROTANA,
            'reservations-repo' => self::CHINA,
        ];
        // ...
    }
}
```

### HTTP服务层
```php
// app/Services/GxpHttpService.php
class GxpHttpService
{
    protected $appKeyChinaKey;
    protected $appKeyRotana;
    
    public function get($url, $params = [], $token = null, $appKeyType = GxpAppKeyType::CHINA)
    {
        $headers = [
            'app-key' => $this->getAppKey($appKeyType),
        ];
        // ...
    }
    
    protected function getAppKey(string $type): string
    {
        switch ($type) {
            case GxpAppKeyType::CHINA:
                return $this->appKeyChinaKey;
            case GxpAppKeyType::ROTANA:
                return $this->appKeyRotana;
        }
    }
}
```

### 业务服务层
```php
// app/Services/GxpService.php
class GxpService
{
    public function getGuestToken(string $username, string $password): GuestTokenDTO
    {
        // 使用china app-key
        $response = $this->httpService->post('auth/token', $params, null, GxpAppKeyType::CHINA);
    }
    
    public function getCurrencyExchangeRate(User $user, string $currency, string $startDate): array
    {
        // 使用rotana app-key
        $response = $this->httpService->get('ddollars/exchange-rate', $params, $token, GxpAppKeyType::ROTANA);
    }
}
```

## ⚙️ 配置设置

### 环境变量
在 `.env` 文件中配置：
```env
# GXP API配置
GHA_API_V1=https://gxp.stage.ghaloyalty.com/api/v1/
GHA_APP_KEY_CHINA=your_china_app_key_value
GHA_APP_KEY_ROTANA=your_rotana_app_key_value
```

### 配置文件
在 `config/gha.php` 中：
```php
return [
    'api_v1' => env('GHA_API_V1', 'https://gxp.stage.ghaloyalty.com/api/v1/'),
    'app_key_china' => env('GHA_APP_KEY_CHINA', ''),
    'app_key_rotana' => env('GHA_APP_KEY_ROTANA', ''),
];
```

## 📋 接口映射表

| 接口名称 | HTTP方法 | 路径 | App-Key类型 | 常量 |
|---------|----------|------|-------------|------|
| Guest Token | POST | `/auth/token` | China | `GxpAppKeyType::CHINA` |
| Currency Rate | GET | `/ddollars/exchange-rate` | Rotana | `GxpAppKeyType::ROTANA` |
| Reserve Posting | POST | `/reservations-repo` | China | `GxpAppKeyType::CHINA` |
| My Bookings | GET | `/reservations-repo` | China | `GxpAppKeyType::CHINA` |

## 🔧 使用示例

### 直接调用HTTP服务
```php
$gxpHttpService = new GxpHttpService();

// 获取访客令牌 - 使用china app-key
$response = $gxpHttpService->post('auth/token', $params, null, GxpAppKeyType::CHINA);

// 获取汇率 - 使用rotana app-key
$response = $gxpHttpService->get('ddollars/exchange-rate', $params, $token, GxpAppKeyType::ROTANA);
```

### 通过业务服务调用
```php
$gxpService = app(GxpService::class);

// 服务层会自动选择正确的app-key
$tokenDto = $gxpService->getGuestToken('username', 'password');
$rates = $gxpService->getCurrencyExchangeRate($user, 'USD', '2025-01-01');
```

## 🧪 测试验证

### 单元测试
```php
public function test_correct_app_key_is_used()
{
    $mockHttpService = Mockery::mock(GxpHttpService::class);
    
    // 验证guest token使用china app-key
    $mockHttpService->shouldReceive('post')
        ->with('auth/token', Mockery::any(), null, GxpAppKeyType::CHINA)
        ->once();
    
    // 验证汇率接口使用rotana app-key
    $mockHttpService->shouldReceive('get')
        ->with('ddollars/exchange-rate', Mockery::any(), Mockery::any(), GxpAppKeyType::ROTANA)
        ->once();
}
```

### 集成测试
```php
public function test_api_endpoints_with_correct_headers()
{
    // 测试实际HTTP请求是否包含正确的app-key头
    $this->postJson('/api/v1/gxp/auth/token', $data)
         ->assertStatus(200);
         
    $this->getJson('/api/v1/gxp/ddollars/exchange-rate?currency=USD&start_date=2025-01-01')
         ->assertStatus(200);
}
```

## ⚠️ 注意事项

1. **环境变量安全**: 确保app-key值不被泄露到版本控制系统
2. **配置验证**: 启动时验证所有必需的app-key都已配置
3. **错误处理**: 当app-key无效时提供清晰的错误信息
4. **日志记录**: 记录API调用但不记录敏感的app-key值
5. **类型安全**: 使用常量类避免字符串拼写错误

## 🔄 迁移指南

如果从旧版本升级，需要：

1. 更新环境变量配置
2. 确保两个app-key都已正确配置
3. 运行测试验证所有接口正常工作
4. 检查日志确认正确的app-key被使用

## 📚 相关文档

- [GXP API集成文档](GXP_API_INTEGRATION.md)
- [GXP服务架构文档](GXP_SERVICE_ARCHITECTURE.md)
- [GXP API使用示例](GXP_API_USAGE_EXAMPLES.md)

通过这种配置方式，系统能够根据不同的接口自动选择正确的app-key，确保API调用的成功率和安全性。
