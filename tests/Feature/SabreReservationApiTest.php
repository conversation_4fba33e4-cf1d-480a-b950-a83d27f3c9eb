<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\SabreService;
use App\Services\SabreReservationDataTransformer;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;

class SabreReservationApiTest extends TestCase
{
    /**
     * 测试查询预订API - 标准格式
     */
    public function testGetReservationStandardFormat()
    {
        // 模拟Sabre API响应
        $mockSabreResponse = [
            'Reservations' => [
                [
                    'Id' => '19856134-ab58-447f-8c04-fd752c3bf47f',
                    'CRS_confirmationNumber' => '100820CK000412',
                    'ItineraryNumber' => '32446B0003092',
                    'Status' => 'Confirmed',
                    'ModificationPermitted' => true,
                    'CancellationPermitted' => true,
                    'Hotel' => [
                        'Id' => 100820,
                        'Code' => 'CH0001',
                        'Name' => 'Cheval <PERSON><PERSON> ¿ The Palm Dubai'
                    ],
                    'Brand' => [
                        'Code' => 'CC',
                        'Id' => 14161569,
                        'Name' => 'Cheval Collection'
                    ],
                    'Chain' => [
                        'Code' => 'GHALL',
                        'Id' => 32446,
                        'Name' => 'Global Hotel Alliance'
                    ],
                    'Currency' => [
                        'Code' => 'USD',
                        'Name' => 'US Dollars',
                        'Symbol' => '$'
                    ],
                    'RoomStay' => [
                        'StartDate' => '2025-09-29T00:00:00',
                        'EndDate' => '2025-09-30T00:00:00',
                        'NumRooms' => 1,
                        'GuestCount' => [
                            [
                                'AgeQualifyingCode' => 'Adult',
                                'NumGuests' => 2
                            ],
                            [
                                'AgeQualifyingCode' => 'Child',
                                'NumGuests' => 1,
                                'Ages' => [8]
                            ]
                        ]
                    ],
                    'Guests' => [
                        [
                            'Role' => 'Primary',
                            'PersonName' => [
                                'GivenName' => '张',
                                'Surname' => '三'
                            ],
                            'EmailAddress' => [
                                [
                                    'Type' => 'Primary',
                                    'Value' => '<EMAIL>',
                                    'Default' => true
                                ]
                            ]
                        ]
                    ],
                    'RoomPrices' => [
                        'TotalPrice' => [
                            'Price' => [
                                'CurrencyCode' => 'USD',
                                'OriginalAmount' => 444.72,
                                'AmountPayableNow' => 560.0,
                                'TotalAmountIncludingTaxesFees' => 560.0,
                                'Tax' => [
                                    'Amount' => 55.28
                                ],
                                'Fees' => [
                                    'Amount' => 60.0
                                ]
                            ]
                        ]
                    ],
                    'BookingPolicy' => [
                        'Code' => '100%',
                        'Description' => '100% Deposit at the time of booking',
                        'GuaranteeLevel' => 'Deposit'
                    ],
                    'CreateDateTime' => '2025-09-17T08:11:41',
                    'UpdateDateTime' => '2025-09-17T08:11:41'
                ]
            ],
            'Pagination' => [
                'Size' => 1,
                'Start' => 0,
                'Total' => 1
            ]
        ];

        // 模拟SabreService的getReservationByConfirmation方法
        $this->mock(SabreService::class, function ($mock) use ($mockSabreResponse) {
            $mock->shouldReceive('getReservationByConfirmation')
                ->once()
                ->with('100820CK000412', 32446, 100820, 'DSCVRYLYLTY')
                ->andReturn($mockSabreResponse);
        });

        // 发送API请求 - 标准格式
        $response = $this->getJson('/api/v1/sabre/reservation?' . http_build_query([
            'confirmationNumber' => '100820CK000412',
            'hotelId' => 100820,
            'format' => 'standard'
        ]));

        // 验证响应状态
        $response->assertStatus(200);

        // 验证响应结构
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'success',
                'message',
                'data' => [
                    'confirmationNumber',
                    'reservations' => [
                        '*' => [
                            'reservation_id',
                            'confirmation_number',
                            'itinerary_number',
                            'status',
                            'modification_permitted',
                            'cancellation_permitted',
                            'hotel' => [
                                'id',
                                'code',
                                'name'
                            ],
                            'brand' => [
                                'code',
                                'name'
                            ],
                            'room_stay' => [
                                'start_date',
                                'end_date',
                                'num_rooms'
                            ],
                            'guests' => [
                                '*' => [
                                    'role',
                                    'person_name' => [
                                        'given_name',
                                        'surname'
                                    ]
                                ]
                            ],
                            'room_prices' => [
                                'total_price' => [
                                    'currency_code',
                                    'original_amount',
                                    'amount_payable_now'
                                ]
                            ]
                        ]
                    ],
                    'pagination' => [
                        'size',
                        'start',
                        'total'
                    ]
                ]
            ]
        ]);

        // 验证具体数据
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertTrue($responseData['data']['success']);
        $this->assertEquals('查询成功', $responseData['data']['message']);
        
        $reservation = $responseData['data']['data']['reservations'][0];
        $this->assertEquals('19856134-ab58-447f-8c04-fd752c3bf47f', $reservation['reservation_id']);
        $this->assertEquals('100820CK000412', $reservation['confirmation_number']);
        $this->assertEquals('32446B0003092', $reservation['itinerary_number']);
        $this->assertEquals('Confirmed', $reservation['status']);
        $this->assertTrue($reservation['modification_permitted']);
        $this->assertTrue($reservation['cancellation_permitted']);
        
        // 验证酒店信息
        $this->assertEquals(100820, $reservation['hotel']['id']);
        $this->assertEquals('CH0001', $reservation['hotel']['code']);
        $this->assertEquals('Cheval Maison ¿ The Palm Dubai', $reservation['hotel']['name']);
        
        // 验证客人信息
        $this->assertEquals('Primary', $reservation['guests'][0]['role']);
        $this->assertEquals('张', $reservation['guests'][0]['person_name']['given_name']);
        $this->assertEquals('三', $reservation['guests'][0]['person_name']['surname']);
        
        // 验证价格信息
        $this->assertEquals('USD', $reservation['room_prices']['total_price']['currency_code']);
        $this->assertEquals(444.72, $reservation['room_prices']['total_price']['original_amount']);
        $this->assertEquals(560.0, $reservation['room_prices']['total_price']['amount_payable_now']);
    }

    /**
     * 测试查询预订API - 原始Sabre格式（向后兼容）
     */
    public function testGetReservationSabreFormat()
    {
        // 模拟Sabre API响应
        $mockSabreResponse = [
            'Reservations' => [
                [
                    'Id' => '19856134-ab58-447f-8c04-fd752c3bf47f',
                    'CRS_confirmationNumber' => '100820CK000412',
                    'Status' => 'Confirmed'
                ]
            ]
        ];

        // 模拟SabreService
        $this->mock(SabreService::class, function ($mock) use ($mockSabreResponse) {
            $mock->shouldReceive('getReservationByConfirmation')
                ->once()
                ->andReturn($mockSabreResponse);
        });

        // 发送API请求 - 原始格式
        $response = $this->getJson('/api/v1/sabre/reservation?' . http_build_query([
            'confirmationNumber' => '100820CK000412',
            'hotelId' => 100820,
            'format' => 'sabre'
        ]));

        // 验证响应状态
        $response->assertStatus(200);

        // 验证返回的是转换后的第一个预订数据
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertArrayHasKey('data', $responseData);
    }

    /**
     * 测试参数验证
     */
    public function testGetReservationValidation()
    {
        // 测试缺少必要参数
        $response = $this->getJson('/api/v1/sabre/reservation');
        $response->assertStatus(400);
        $response->assertJson([
            'success' => false,
            'message' => '必须提供行程号或确认号'
        ]);

        // 测试无效的format参数
        $response = $this->getJson('/api/v1/sabre/reservation?' . http_build_query([
            'confirmationNumber' => '100820CK000412',
            'hotelId' => 100820,
            'format' => 'invalid'
        ]));
        $response->assertStatus(400);
        $response->assertJsonValidationErrors(['format']);
    }

    /**
     * 测试默认格式（应该是standard）
     */
    public function testGetReservationDefaultFormat()
    {
        // 模拟Sabre API响应
        $mockSabreResponse = [
            'Reservations' => [
                [
                    'Id' => '19856134-ab58-447f-8c04-fd752c3bf47f',
                    'CRS_confirmationNumber' => '100820CK000412',
                    'Status' => 'Confirmed'
                ]
            ]
        ];

        $this->mock(SabreService::class, function ($mock) use ($mockSabreResponse) {
            $mock->shouldReceive('getReservationByConfirmation')
                ->once()
                ->andReturn($mockSabreResponse);
        });

        // 不指定format参数，应该默认使用standard格式
        $response = $this->getJson('/api/v1/sabre/reservation?' . http_build_query([
            'confirmationNumber' => '100820CK000412',
            'hotelId' => 100820
        ]));

        $response->assertStatus(200);
        
        // 验证返回的是标准格式（包含success, message, data结构）
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertArrayHasKey('success', $responseData['data']);
        $this->assertArrayHasKey('message', $responseData['data']);
    }
}
